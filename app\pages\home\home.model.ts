export interface Bank_Transaction_List {
       links:{
                  next: string
                  previous: string
              }
              total:number
              page:number
              page_size:number
              total_pages:number
              results: Bank_Transaction_List_Item[]
  }

  export interface Bank_Transaction_List_Item {
    id: number;
    created_at: string;
    emi_amount: number;
    customer_name: string;
    transaction_mode: string
    agent_name: string;
    remark: string;
    status: string;
    care_of: string;
    short_title: string;
    payment_reference_no: number;
    transaction_id: number;
    approved: string;
    screenshot: string;
    customer_profile_photo: string;
    
  }

  export interface Approval_Details {
    approval_status : string;
    remarks: string;
  }

  export interface Header_Items {
    total_agents: number;
    total_customer: number,
    total_pending_approvel: number;
    total_routes: number;
  }