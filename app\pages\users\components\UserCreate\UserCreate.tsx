import React, { useCallback, useEffect, useState } from "react";
import "./UserCreate.scss";
import { useForm, SubmitHand<PERSON>, Controller } from "react-hook-form";
import { Branch, User_Create } from "../../users.model";
import { Autocomplete, TextField } from "@mui/material";
import {
  createUser,
  editUserById,
  fetchBranchList,
  fetchUserById,
} from "../../users-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { AxiosError } from "axios";

interface UserCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  selectedUserId: number | null;
}

interface ErrorResponseData {
  detail?: string;
}

function UserCreate({
  showCreate,
  handleCloseCreate,
  selectedUserId,
}: UserCreateProps) {
  // const [ userDetails, setUserDetails] = useState<User_Create>();
  const [options, setOptions] = useState<Branch[]>([]);

  const { fire } = useAlert();

  const { setIsLoading , userData } = useCommonContext();

  const {
    register,
    control,
    getValues,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
  } = useForm<User_Create>();

  const getBranchList = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await fetchBranchList();
      setOptions(res.results);
      if (!getValues("branch_id")) {
        const initialValue = res?.results[0]?.id;
        if (initialValue) {
          setValue("branch_id", initialValue);
        }
      }
    } catch (err) {
      console.error("error", err);

      fire({
        position: "center",
        icon: "error",
        title: "Some error occured",
        text: "Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });

      getBranchList();

    }finally{
      setIsLoading(false);
    }
  }, [getValues, setValue, fire, setIsLoading]);

  const fetchUserDetails = useCallback(
    async (id: number) => {
      try {
        setIsLoading(true);
        const data = await fetchUserById(id);
        reset({
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address,
          user_type: data.user_type,
          username: data.username,
          password: data.password,
          role: data.role,
          branch_id: data.branch_id,
        });
        getBranchList(); // Call getBranchList after resetting the form
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    [reset, setIsLoading, getBranchList] // Add reset and setIsLoading as dependencies to ensure stability
  );

  useEffect(() => {
    if (selectedUserId) {
      fetchUserDetails(selectedUserId);
    } else {
      //fetch select branch options
      getBranchList();
    }
  }, [selectedUserId, getBranchList, fetchUserDetails]);

  //   useEffect(() => {
  // console.log("optionsss", getValues('branch_id'));

  //   }, [getValues('branch_id')])

  const onSubmit: SubmitHandler<User_Create> = async (data) => {
    //console.log("Form Data:", data);
    if (!data.branch_id) {
      data.branch_id = null;
    }
    try {
      if (selectedUserId) {
        //update user
        setIsLoading(true);
        await editUserById(selectedUserId, data);
        //console.log("User updated", res);

        fire({
          position: "top-right",
          icon: "success", // Use success icon
          title: "User updated",
          text: "The user details were updated successfully!",
          autoClose: 2000,
        });
      } else {
        //create user
        await createUser(data);
        //console.log("user created", res);

        fire({
          position: "top-right",
          icon: "success", // Use success icon
          title: "User created",
          text: "The user is created successfully!",
          autoClose: 2000,
        });
      }

      handleCloseCreate();
      reset();
    } catch (err) {
      //handleCloseCreate();
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          {selectedUserId ? <h3>Edit User</h3> : <h3>Add User</h3>}
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="name">Name</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Type..."
                  {...register("name", {
                    required: "Please provide the name.",
                  })}
                />
                <p className="error-message">{errors.name?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="email">Email</label>
                <input
                  {...register("email", {
                    required: "Please enter an email address.",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // Regex for email validation
                      message:
                        "The email format is invalid. Please enter a valid email address.",
                    },
                  })}
                  id="email"
                  type="email"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.email?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="username">Username</label>
                <input
                  id="username"
                  type="text"
                  placeholder="Type..."
                  {...register("username", {
                    required: "Please provide the user name.",
                  })}
                />
                <p className="error-message">{errors.username?.message}</p>
              </div>

              {selectedUserId ? (
                <></>
              ) : (
                <div className="input-field wf-50">
                  <label htmlFor="password">Password</label>
                  <input
                    id="password"
                    type="text"
                    placeholder="Type..."
                    {...register("password", {
                      required: "Please enter the password.",
                    })}
                  />
                  <p className="error-message">{errors.name?.message}</p>
                </div>
              )}

              <div className="input-field wf-50">
                <label htmlFor="role">Role</label>
                <input
                  id="role"
                  type="text"
                  placeholder="Type..."
                  {...register("role")}
                />
                {/* <p className="error-message">{errors.name?.message}</p> */}
              </div>
              <div className="input-field wf-50">
                <label htmlFor="address">Address</label>

                <input
                  {...register("address", {
                    required: "Please enter the user's address.",
                  })}
                  id="address"
                  type="text"
                  placeholder="Type..."
                />

                <p className="error-message">{errors.address?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="phoneNumber">Phone Number</label>
                <input
                  {...register("phone", {
                    required: "Please enter the phone number.",
                  })}
                  id="phoneNumber"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="userType">User Type</label>
                <div className="input purchase-order">
                  <select
                    {...register("user_type", {
                      required: "Please select user type.",
                    })}
                    id="userType"
                  >
                    <option value="BranchManager">Branch Manager</option>
                    {userData?.user_type === "ManagingDirector" && 
                      <option value="ManagingDirector">Managing director</option>
                    }
                  </select>
                </div>
                <p className="error-message">{errors.user_type?.message}</p>
              </div>

              {selectedUserId ? (
                <div className="select-input-field wf-50">
                  <label htmlFor="branch">Branch</label>

                  <Controller
                    name="branch_id"
                    control={control}
                    rules={{ required: "Please select a branch." }}
                    render={({ field }) => (
                      <Autocomplete
                        {...field}
                        id="branch"
                        options={options}
                        getOptionLabel={(option) => option?.name}
                        value={
                          options.length > 0
                            ? options.find(
                                (option) => option.id === getValues("branch_id")
                              )
                            : null
                        } // Set initial value
                        onChange={(_, value) => {
                          setValue("branch_id", value?.id ?? 0);
                        }}
                        // Update selected branch or default to first option
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select Branch"
                            error={!!errors?.branch_id}
                            variant="outlined"
                            InputProps={{
                              ...params.InputProps,
                              style: {
                                padding: "2px",
                                borderRadius: "6px",
                                backgroundColor: "#ffffff",
                              },
                            }}
                            sx={{
                              "& .MuiInputBase-input::placeholder": {
                                opacity: 1,
                              },
                              "& .MuiOutlinedInput-root": {
                                "& fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&:hover fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                                "& input": {
                                  padding: "10px",
                                },
                                "&.Mui-error fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused.Mui-error fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                              },
                            }}
                          />
                        )}
                      />
                    )}
                  />

                  {/* Show error message if the field is empty and not selected */}
                  {errors?.branch_id && (
                    <p className="error-message" style={{ color: "#f44336" }}>
                      {errors?.branch_id?.message}
                    </p>
                  )}
                </div>
              ) : (
                <div className="select-input-field wf-100">
                  <label htmlFor="branch">Branch</label>

                  <Controller
                    name="branch_id"
                    control={control}
                    rules={{ required: "Please select a branch." }}
                    render={({ field }) => (
                      <Autocomplete
                        {...field}
                        id="branch"
                        options={options}
                        getOptionLabel={(option) => option?.name}
                        value={
                          options.length > 0
                            ? options.find(
                                (option) => option.id === getValues("branch_id")
                              )
                            : null
                        } // Set initial value
                        onChange={(_, value) => {
                          setValue("branch_id", value?.id ?? 0);
                        }}
                        // Update selected branch or default to first option
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select Branch"
                            error={!!errors?.branch_id}
                            variant="outlined"
                            InputProps={{
                              ...params.InputProps,
                              style: {
                                padding: "2px",
                                borderRadius: "6px",
                                backgroundColor: "#ffffff",
                              },
                            }}
                            sx={{
                              "& .MuiInputBase-input::placeholder": {
                                opacity: 1,
                              },
                              "& .MuiOutlinedInput-root": {
                                "& fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&:hover fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                                "& input": {
                                  padding: "10px",
                                },
                                "&.Mui-error fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused.Mui-error fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                              },
                            }}
                          />
                        )}
                      />
                    )}
                  />

                  {/* Show error message if the field is empty and not selected */}
                  {errors?.branch_id && (
                    <p className="error-message" style={{ color: "#f44336" }}>
                      {errors?.branch_id?.message}
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default UserCreate;
