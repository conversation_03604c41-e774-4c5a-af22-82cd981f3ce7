import React, { useCallback, useEffect, useState } from "react";
import "./agent-activity.scss";
import { getAgentActivity } from "../../../agent-service";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import { ActivityLog } from "../../../agent.model";
import { useCommonContext } from "@/app/contexts/commonContext";

interface Prop {
  agentId: number | null;
}

function AgentActivity({ agentId }: Prop) {
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [list, setList] = useState<ActivityLog[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>("");
  const { maxDate} = useCommonContext()
  const getList = useCallback(
    async (date: string) => {
      setIsTableLoading(true);
      try {
        const api = await getAgentActivity(agentId, date);
        setList(api);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [agentId]
  );

  const handleDateChange = (value: string) => {
    setSelectedDate(value);
    getList(value);
  };

  useEffect(() => {
    getList("");
  }, [getList]);

  return (
    <div className="activity-card-div scroll-bar-1">
      <div className="table-header-details-page" style={{ height: "60px" }}>
        <div className="filter-search-container">
          <div className="filter-option-select-box">
            <input
              className="date-picker"
              type="date"
              max={maxDate}
              value={selectedDate}
              onChange={(e) => handleDateChange(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div className="activity-container">
        {isTableLoading ? (
          <TableWithShimmer no_of_cols={5} no_of_rows={5} />
        ) : list.length > 0 ? (
          list.map((item, index) => {
            return (
              <div key={index} className="activity-day scroll-bar-1">
                {/* Date Header */}
                <div className="header">
                  <span className="date">
                    {item.day} |{" "}
                    {new Date(item.created_at).toLocaleDateString()}
                  </span>
                  {/* <a href="#" className="view-details">
                    View Details
                  </a> */}
                </div>

                {/* Timeline Section */}
                <div className="timeline">
                  {item.data.map((event, eventIndex) => {
                    const isFirst = eventIndex === 0;
                    const isLast = eventIndex === item.data.length - 1;

                    return (
                      <div key={eventIndex} className="event">
                        {/* Time Display */}
                        <div className="time">{event.time}</div>

                        {/* Timeline Dots & Lines */}
                        <div className="dot-container">
                          {/* Only show left dotted line if it's NOT the first event */}
                          {!isFirst && <div className="dotted-line left"></div>}

                          {/* Main dot */}
                          <div className="dot"></div>

                          {/* Only show right dotted line if it's NOT the last event */}
                          {!isLast && <div className="dotted-line right"></div>}
                        </div>

                        {/* Event Description */}
                        <div className="description">{event.activity}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })
        ) : (
          <div className="no-data">
            {selectedDate ? (
              <h5>
                No activity available for the selected date: {selectedDate}
              </h5>
            ) : (
              <h5>No activity available to display.</h5>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default AgentActivity;
