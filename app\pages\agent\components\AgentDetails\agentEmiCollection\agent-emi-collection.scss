@import '/app/styles/variables.scss';

.emi-collection-history-container{
    height: calc(100dvh - 85px);
    background-color: $white_color;

    @media(max-width: $breakpoint-lg) {
      height: auto;
    }

    .table2{
        max-height: calc(100dvh - 344px);

        table{
            tbody{
                tr{

                    .date-time-invoiceNumber{
                        display: flex;
                        flex-direction: column;
                        white-space: nowrap;
                        
          
                        .dateAndTime{
                          display: flex;
                          flex-direction: row;
                          margin-bottom: 3px;
                          white-space: nowrap;
                          
                    
                          .date, .time{
                            font-size: 13px;
                            color: $black_color;
                            font-weight: 600;
                            white-space: nowrap;
                            
                          }
          
                          .date{
                            border-right:  2px solid $black_color;
                            padding-right: 5px;
                          }
          
                          .time{
                            padding-left: 5px;
                          }
          
                        
                         }
          
                         .invoiceNumber{
                          font-size: 10px;
                          color: $black_color3;
                          white-space: nowrap;
                        }
                       }

                    .cash{
                        color: $green_color2;
                          font-weight: 600;
                          font-size: 12px;
                          white-space: nowrap;
      
                      }
      
                      .gpay{
                        color: $black_color2;
                          font-weight: 600;
                          font-size: 12px;
                          white-space: nowrap;
      
                      }

                    .customerName{
                        color: $black_color2;
                        font-weight: 600;
                        font-size: 12px;
                        white-space: wrap;
                    }

                    .modeOfTransaction{
                        display: flex;
                        flex-direction: column;
                        white-space: nowrap;
      
                        .mode{
                          font-size: 12px;
                          color: $black_color2;
                          font-weight: 600;
                          margin-bottom: 2px;
                          white-space: nowrap;
                        }
                        
      
                        .transactionId{
                          font-size: 12px;
                          color: $black_color3;
                          font-weight: 500;
                          white-space: nowrap;
      
                        }
                      }
                      
                    .remark{
                        white-space: wrap;
                        font-size: 12px;
                        font-weight: 600;
                        color: $black_color3;
        
                        .remark-text-field {
                            input {
                                width: 100%;
                                height: 30px;
                                border: 1px solid $black_color4;
                                border-radius: 3px;
                                padding: 0 0 0 5px;
        
                                &::placeholder{
                                    color: $black_color3;
                                    font-size: 11px;
                                }
                            }
        
                          
                        }
                        }
                }
            }
        }

    }
}