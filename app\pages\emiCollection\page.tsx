"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./emiCollection.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer";
import { fetchCollectionHistory } from "./emi-service";
import { Emi_Collection_Overview_List_Item } from "./emi.model";
import DateTimeDisplay from "@/app/components/utilities/DateTimeDisplay/DateTimeDisplay";
import { useCommonContext } from "@/app/contexts/commonContext";
import ViewScreenshot from "../home/<USER>/ViewScreenshot/ViewScreenshot";
import TableMenuTwo, {
  DropdownItem,
} from "@/app/components/TableMenuTwo/TableMenuTwo";
import EditEmiCollection from "./components/EditEmiCollection";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [emiCollectionOverviewData, setEmiCollectionOverviewData] = useState<
    Emi_Collection_Overview_List_Item[]
  >([]);
  const [visible, setVisible] = useState<boolean>(false);
  //filters
  const [status, setStatus] = useState<string>("");
  const [transactionMode, setTransactionMode] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  const [showScreenshot, setShowScreenshot] = useState<boolean>(false);
  const [screenshotUrl, setScreenShotUrl] = useState<string>("");
  const [selectedEmi, setSelectedEmi] = useState<number | null>(null);
  const [isEditEmi, setIsEditEmi] = useState<boolean>(false);

  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const menuItems = [{ id: "edit", label: "Edit EMICollection" }];

  const { isLoading, setIsLoading ,maxDate } = useCommonContext();

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  //fetch collection history
  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPageNo: number,
      searchKey: string,
      status: string,
      transaction_mode: string,
      date: string
    ) => {
      const skip = (pageNo - 1) * itemsPerPageNo;
      try {
        setIsLoading(true);
        const response = await fetchCollectionHistory(
          skip,
          itemsPerPageNo,
          searchKey,
          status,
          transaction_mode,
          date
        );

        setEmiCollectionOverviewData(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    [setEmiCollectionOverviewData, setTotalPages, setIsLoading]
  );

  useEffect(() => {
    getList(1, 10, "", "", "", "");
  }, [getList]);

  const handleReset = () => {
    setSearchValue("");
    setStatus("");
    setSelectedDate("");
    setTransactionMode("");
    getList(1, itemsPerPage, "", "", "", "");
  };

  // useEffect(() => {
  //   console.log(isSearchBoxVisible); // This will log the updated state
  // }, [isSearchBoxVisible]);

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo,"page changed");
    getList(
      pageNo,
      itemsPerPage,
      searchValue,
      status,
      transactionMode,
      selectedDate
    );
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, status, transactionMode, selectedDate);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;

    if (item === "status") {
      setStatus(event.target.value);
      getList(
        1,
        itemsPerPage,
        searchValue,
        value,
        transactionMode,
        selectedDate
      );
      setStatus(value);
    } else if (item === "transaction_mode") {
      setTransactionMode(value);
      getList(1, itemsPerPage, searchValue, status, value, selectedDate);
    } else if (item === "date") {
      setSelectedDate(value);
      getList(1, itemsPerPage, searchValue, status, transactionMode, value);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);
      debouncedGetList(value);
    };
  
    const debouncedGetList = debounce(
      (value: string) => getList(1, itemsPerPage, value, status, transactionMode, selectedDate),
      300
    );

  const handleShowScreenshot = (imgUrl: string) => {
    setShowScreenshot(true);
    setScreenShotUrl(imgUrl);
  };

  const handleCloseScreenshot = () => {
    setShowScreenshot(false);
  };

  const handleCloseEdit = () => {
    setIsEditEmi(false);
    getList(
      currentPage,
      itemsPerPage,
      searchValue,
      status,
      transactionMode,
      selectedDate
    );
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "edit") {
      setSelectedEmi(id);
      setIsEditEmi(true);
    }
  };

  return (
    <>
      <div
        className={`emiCollectionOverviewList overall-list-padding ${
          visible ? "visible" : ""
        }`}
      >
        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span> Status {status ? `(${status})` : ""}</span>
                    <select
                      className="dropdown"
                      value={status}
                      onChange={(e) => {
                        handleFilterSelect(e, "status");
                      }}
                    >
                      <option value="">None</option>
                      <option value="success">Success</option>
                      <option value="inprogress">In progress</option>
                      <option value="failed">Failed</option>
                      {/* <option value="due">Due</option> */}
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>

                  <div className="filterOption">
                    <span>
                      Transaction Mode{" "}
                      {transactionMode ? `(${transactionMode})` : ""}
                    </span>
                    <select
                      className="dropdown"
                      value={transactionMode}
                      onChange={(e) => {
                        handleFilterSelect(e, "transaction_mode");
                      }}
                    >
                      <option value="">None</option>
                      <option value="cash">Cash</option>
                      <option value="bank">Bank</option>
                      <option value="unavailable">Unavailable</option>
                      {/* <option value="due">Due</option> */}
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>

                  <input
                    className="date-picker2"
                    type="date"
                    max={maxDate}
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                  />
                </div>

               

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[
                    `Search "name"`,
                    `Search "reference number"`,
                    `Search "transaction id"`,
                   
                  ]}
                />


              </div>
            </div>

            {isLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={7}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll-fullHeight">
                <table>
                  <thead>
                    <tr>
                      <th>Date & Time</th>
                      <th>Amount</th>
                      <th>Customer Name</th>
                      <th>Transaction Mode</th>
                      <th>Agent</th>
                      <th>Status</th>

                      {/* <th>Branch</th> */}
                      <th>Remark</th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    {emiCollectionOverviewData &&
                    emiCollectionOverviewData.length > 0 ? (
                      emiCollectionOverviewData.map((data, index) => (
                        <tr key={index}>
                          <td>
                            <div className="date-time-invoiceNumber">
                              <div>
                                <DateTimeDisplay
                                  created_at={data.created_at}
                                  pageName=""
                                />
                              </div>
                              <p className="invoiceNumber">
                                {data.transaction_id}
                              </p>
                            </div>
                          </td>

                          <td>
                            <span
                              className={` ${
                                data.transaction_mode == "cash"
                                  ? "cash"
                                  : "gpay"
                              }  `}
                            >
                              {data.emi_amount}
                            </span>
                          </td>

                          <td>
                            <div className="customerName">
                              <p className="name">{data.customer_name}</p>

                              <p className="careOf">{data.care_of ? 'C/O ' + data.care_of : ''}</p>
                              <p className="careOf">{data.short_title}</p>
                            </div>
                          </td>

                          <td>
                            {data.transaction_mode == "bank" ? (
                              <div>
                                <div className="transactionMode">
                                  <span className="mode">
                                    {data.transaction_mode}
                                  </span>
                                  <span>
                                    <i
                                      onClick={() =>
                                        handleShowScreenshot(data.screenshot)
                                      }
                                      className="fa-solid fa-paperclip"
                                    ></i>
                                  </span>
                                </div>
                                <p className="invoiceNumber">
                                  {data.payment_reference_no}
                                </p>
                              </div>
                            ) : (
                              <div className="transactionMode">
                                <span className="mode">
                                  {data.transaction_mode}
                                </span>
                              </div>
                            )}
                          </td>

                          <td>
                            <div className="agent">{data.agent_name}</div>
                          </td>

                          <td>
                            <div className="agent">
                              <div className={`status ${data.status} `}>
                                {data.status}
                              </div>
                            </div>
                          </td>

                          {/* <td>
                      <div className="branchName">
                          {data.branch_name}
                      </div>
                  </td> */}

                          <td>
                            <p className="remark">
                              {data.remark ? data.remark : "N/A"}
                            </p>
                          </td>
                          <td>
                            <TableMenuTwo
                              items={menuItems}
                              id={data.id}
                              onClick={handleMenuItemClick}
                            />
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={7} className="no-data-emiCollection">
                          <h5>
                            {(() => {
                              const filters = [];

                              if (searchValue)
                                filters.push(`search "${searchValue}"`);
                              if (status) filters.push(`status "${status}"`);
                              if (transactionMode)
                                filters.push(
                                  `transaction mode "${transactionMode}"`
                                );
                              if (selectedDate)
                                filters.push(`date "${selectedDate}"`);

                              if (filters.length > 0) {
                                return `No data found with ${filters.join(
                                  " and "
                                )}.`;
                              }

                              return "Looks like there's no collection history yet.";
                            })()}
                          </h5>

                          {(status ||
                            transactionMode ||
                            selectedDate ||
                            searchValue) && (
                            <button
                              onClick={handleReset}
                              style={{
                                marginLeft: "auto",
                                marginRight: "auto",
                              }}
                              className="submitButton"
                            >
                              <span className="material-icons">
                                restart_alt
                              </span>
                              Reset
                            </button>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {emiCollectionOverviewData &&
              emiCollectionOverviewData?.length > 0 && (
                <Pagination
                  totalPages={totalPages}
                  handlePage={handlePageChange}
                  itemsPerPage={itemsPerPage}
                  page={currentPage}
                  handleItemsPerPageChange={handleItemsPerPageChange}
                />
              )}
          </div>
        </div>
      </div>
      <ViewScreenshot
        screenshotUrl={screenshotUrl}
        showScreenshot={showScreenshot}
        handleCloseScreenshot={handleCloseScreenshot}
      />
      <EditEmiCollection
        id={selectedEmi}
        handleCloseCreate={handleCloseEdit}
        showCreate={isEditEmi}
      />
    </>
  );
}

export default Page;
