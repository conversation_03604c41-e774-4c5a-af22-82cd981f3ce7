import React from 'react';
import './DateTimeDisplay.scss';

interface DateTimeDisplayProps {
  created_at: string;
  pageName: string;
}

const DateTimeDisplay: React.FC<DateTimeDisplayProps> = ({ created_at, pageName }) => {
  const formattedDate = new Date(created_at).toLocaleDateString();
  const formattedTime = new Date(created_at).toLocaleTimeString();

  return (
    <div>
      {
        pageName == "home" ? (<div className={`${pageName == "home" ? ('date-time') : ('dateAndTime')}`}>
      
        {formattedDate} - {formattedTime}
      </div>) : (<div className="dateAndTime">
                  <span className="date">{formattedDate}</span>
                  <span className="time">
                    {formattedTime}</span>       
                     </div>)
      }




    </div>
    
    
  );
};

export default DateTimeDisplay;
