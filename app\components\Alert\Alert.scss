@import '/app/styles/variables.scss';

.alert-box-container
{
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
     top: 0;
     bottom: 0;
     left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    //backdrop-filter: blur(3px);
    // z-index: 5000;
    opacity: 0;
    z-index: -1;
    // z-index: 1000;
    transition: opacity 0.3s ease, z-index 0s linear 0.3s;
    overflow-y: auto;
    padding-bottom: 15px;
    //border-radius: 8px;

    &.show{
        opacity: 1;
        z-index: 1000;
    }

    .alert-box{
        background-color: $white_color;
        padding: 35px 25px;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 8px;

        .blockIcon{
            font-size: 52px;
            color: $primary_color;
            font-weight: 900;
            margin-bottom: 10px;
        }

        h5{
            font-size: 13px;
            margin-bottom: 5px;
        }

        p{
            color: $black_color2;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .inputBox{
            margin-bottom: 15px;
            input{
                width: 250px;
                border: 1px solid $black_color4;
                border-radius: 3px;
                padding: 5px 5px 40px 5px;
                font-size: 8px;
                color: $black_color3; 
            }

        }


        .button-container{
            display: flex;
            flex-direction: row;
            gap: 10px;

            .yesButton{
                background-color: $primary_color;
                color: $white_color1;
                font-size: 11px;
                padding: 6px 26px;
                border-radius: 6px;
                border: 0;
                cursor: pointer;
                transition: background-color 0.3s ease;
            
                &:hover{
                    background-color: darken($primary_color, 10%);     
                }
            
            
              
            }

            .noButton{
                background-color: $white_color;
                color: $black_color;
                font-size: 11px;
                font-weight: 600;
                padding: 6px 26px;
                border-radius: 6px;
                border: 1px solid $black_color4;
                cursor: pointer;
                transition: background-color 0.3s ease;
                

                &:hover{
                    background-color: darken($white_color, 10%);     
                }
            
            
              
            }
        }
    }

}