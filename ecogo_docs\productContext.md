# EcoGO Choice EMI Dashboard - Product Context

## Project Overview
This is a Next.js-based Progressive Web Application (PWA) for managing EMI (Equated Monthly Installment) collections and customer management for Choice EMI business.

## Purpose
- **EMI Collection Management**: Track and manage EMI payments from customers
- **Customer Management**: Maintain customer profiles, ratings, and payment history
- **Purchase Order Management**: Create and track purchase orders with EMI plans
- **Agent Management**: Manage field agents who collect EMIs
- **Branch Management**: Multi-branch operations support
- **Route Management**: Organize collection routes for agents

## Key Business Problems Solved
1. **EMI Tracking**: Digitize manual EMI collection processes
2. **Customer Credit Management**: Track customer creditworthiness and payment history
3. **Agent Performance**: Monitor agent collection performance and wallet management
4. **Branch Operations**: Coordinate multi-branch EMI collection operations
5. **Payment Verification**: Screenshot-based payment verification system
6. **Route Optimization**: Organize customer visits by geographic routes

## Core Features
- **Dashboard**: Bank transaction approvals and overview
- **EMI Collection**: Record, edit, and track EMI payments
- **Customer Management**: Complete customer lifecycle management
- **Purchase Orders**: Create orders with flexible EMI plans (weekly/monthly)
- **Agent Management**: Agent profiles, wallet management, QR codes
- **Branch Management**: Multi-branch support with filtering
- **Route Management**: Geographic organization of customers
- **User Management**: Role-based access control
- **Reports**: Collection and performance analytics

## Technology Stack
- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: SCSS, Tailwind CSS, Material-UI
- **State Management**: React Context API
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **PWA**: next-pwa for offline capabilities
- **Drag & Drop**: react-beautiful-dnd, react-dnd
- **File Upload**: react-dropzone

## Target Users
- **Field Agents**: Collect EMIs and manage customer relationships
- **Branch Managers**: Oversee branch operations and agent performance
- **Admin Users**: System administration and reporting
- **Customers**: (Indirect) EMI payment and account management
