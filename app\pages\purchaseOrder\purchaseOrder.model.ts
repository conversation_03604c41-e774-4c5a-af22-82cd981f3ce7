import { customer } from "../customer/customer.model"

export interface Purchase_Order_Overview_List {
  links:{
          next: string
          previous: string
      }
      total:number
      page:number
      page_size:number
      total_pages:number
      results: Purchase_Order_Overview_List_Item[]   
}

export interface Purchase_Order_Overview_List_Item{
    id : number;
    customer_name : string | null;
    total_amount : number | null;
    advance_amount : number | null;
    created_at : string;
    emi_amount : number | null;
    emi_type : string | null;
    emi_period : number | null;
    emi_period_type : string | null;
    branch_name : string;
    invoice_no: number;
    care_of: string;
    short_name :string;   
}

export interface order_create{
    title: string;
    invoice_no: string;
    reference_no: string;
    total_amount: number | null;
    advance_amount: number | null;
    customer_id: number;
    emi_amount: number | null;
    emi_type: string;
    emi_period: number | null;
    emi_period_type: string;
    monthly_emi_due_date?: number;
    monthly_emi_due_date_inputs?: string;
    weekly_emi_due_date?: string;
    branch_id:number
    items: {
        name: string;
        qty: number;
        amount: number;
      }[];
    attachments_ids: number[];
    is_accounted: boolean;  
  };

  export interface orderEdit{
    emi_type: string;
    emi_period_type: string;
    monthly_emi_due_date?: number;
    weekly_emi_due_date?: string;
  }


  export interface customerPurchaseOrder{
    links:{
      next: string
      previous: string
  }
  total:number
  page:number
  page_size:number
  total_pages:number
  results: customer[] 
  }