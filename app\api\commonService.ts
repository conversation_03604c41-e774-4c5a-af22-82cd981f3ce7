import { address, createAddressType, loginForm, loginRes, userDetails } from "../models/common.models";
import axiosInstance from "./axiosInstance";

export const login = async (body:loginForm) : Promise<loginRes> =>{
    try{
        const response = await axiosInstance.post<loginRes>('users/login/',body)
        return response.data
    } catch (error){
        throw error;
    }
}

export const getUserDetails = async () : Promise<userDetails> =>{
    try{
        const response = await axiosInstance.get<userDetails>('users/profile',)
        return response.data
    }catch(error){
        throw error;
    }
}

export const getAddressById = async (id:number | null) => {
    try{
        const response = await axiosInstance.get(`customer_addresses/dashboard/${id}`)
        return response.data
    }catch(error){
        throw error;
    }
}

export const editAddress = async (id:number | null,body:address) => {
    try{
        const response = await axiosInstance.put(`customer_addresses/dashboard/${id}`,body)
        return response.data
    }catch(error){
        throw error;
    }
}

export const createAddress = async (body:createAddressType) => {
    try{
        const response = await axiosInstance.post(`customer_addresses/dashboard/`,body)
        return response.data
    }catch(error){
        throw error;
    }
}