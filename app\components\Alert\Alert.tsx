import React from 'react'
import './Alert.scss'

interface alertProps{
  showAlert: boolean,
  handleCloseAlert: VoidFunction,
  isRejectActive:boolean,
  handleApproval: VoidFunction,
  handleRejection: VoidFunction,
  
}

function Alert({showAlert, handleCloseAlert, isRejectActive, handleApproval, handleRejection} : alertProps) {

  return (
    <div className={`alert-box-container ${showAlert ? 'show' : ''}`}>
      {
        isRejectActive ? (<div className="alert-box">
          <span className="material-icons blockIcon">
block
</span>
          <h5>
              Are you Sure?
          </h5>
          <p>Are you sure to confirm the transaction?</p>
          <div className="inputBox">
            <input type="text" value={'ഈ തുക ബാങ്ക് അക്കൗണ്ടിൽ ക്രെഡിറ്റ് ആയിട്ടില്ല'} placeholder='Type...' />
          </div>
          <div className="button-container">
              <button className='yesButton' onClick={handleRejection}>Yes</button>
              <button className='noButton' onClick={handleCloseAlert}>No</button>
          </div>
      </div>) : (<div className="alert-box">
        
          <h5>
              Are you Sure?
          </h5>
          <p>Are you sure to confirm the transaction?</p>
          <div className="button-container">
              <button className='yesButton' onClick={handleApproval}>Yes</button>
              <button className='noButton' onClick={handleCloseAlert}>No</button>
          </div>
      </div>)
      }
        
    </div>
  )
}

export default Alert