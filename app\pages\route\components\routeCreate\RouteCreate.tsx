import React, { useCallback, useEffect, useState } from "react";
import "./RouteCreate.scss";
import {
  useForm,
  SubmitHand<PERSON>,
  Controller,
} from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import { Route_Create } from "../../route.model";
import {
  createRoute,
  fetchAgentList,
  fetchBranchList,
} from "../../route-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { AxiosError } from "axios";

interface routeCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
}

interface ErrorResponseData {
  detail?: string;
}

// type Row = {
//   customer_id: number; // ID of the selected customer
//   priority: number; // Priority for the customer
// };

function RouteCreate({ showCreate, handleCloseCreate }: routeCreateProps) {
  // const [rows, setRows] = useState<Row[]>([
  //   {
  //     customer_id: 0, // Default value
  //     priority: 0, // Default value
  //   },
  // ]);
  const [options, setOptions] = useState<Route_Create[]>([]);
  // const [customerOptions, setCustomerOptions] = useState<Route_Create[]>([]);
  const [agentOptions, setAgentOptions] = useState<Route_Create[]>([]);

  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  // const handleAddRow = () => {
  //   setRows([...rows, { customer_id: 0, priority: 0 }]); // Add a new row with default values
  // };

  // const handleRemoveRow = (index: number) => {
  //   setRows(rows.filter((_, i) => i !== index)); // Remove the row at the specified index
  // };

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<Route_Create>();

  // const {} = useFieldArray({
  //   control,
  //   name: "customers",
  // });

  const onSubmit: SubmitHandler<Route_Create> = async (data) => {
    //console.log("Form Data:", data);
    const body = {
      day: data.day,
      route_name: data.route_name,
      ...(data.agent_id && data.agent_id !== 0 && { agent_id: data.agent_id }),
      ...(data.branch_id && data.branch_id !== 0 && { branch_id: data.branch_id })
    };
    
    try {
      setIsLoading(true);
      const res = await createRoute(body);
      if (res) {
        handleCloseCreate();
        reset();
        fire({
          position: "top-right",
          icon: "success", // Use success icon
          title: "Route created",
          text: "The route is created successfully!",
          autoClose: 2000,
        });
      }
    } catch (err) {
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getBranchList = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await fetchBranchList();
      setOptions(res.results);
      if (!getValues("branch_id")) {
        const initialValue = res?.results[0]?.id;
        if (initialValue) {
          setValue("branch_id", initialValue);
        }
      }
    } catch (err) {
      console.error("error", err);
      fire({
        position: "center",
        icon: "error",
        title: "Some error occured",
        text:"Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });

      getBranchList();
    }finally{
      setIsLoading(false);
    }
  }, [setValue, getValues, fire, setIsLoading]);

  // const getCustomerList = useCallback(async () => {
  //   try {
  //     const res = await fetchCustomerList();
  //     // setCustomerOptions(res.results);
  //     if (!getValues("customers.0.customer_id")) {
  //       const initialValue = res.results[0].id;
  //       if (initialValue) {
  //         setValue("customers.0.customer_id", initialValue);
  //       }
  //     }
  //   } catch (err) {
  //     console.error("error", err);
  //   }
  // }, [setValue, getValues]);

  const getAgentList = useCallback(async () => {
    try {
      const res = await fetchAgentList();
      setAgentOptions(res.results);
      if (!getValues("agent_id")) {
        const initialValue = res.results[0].id;
        if (initialValue) {
          setValue("agent_id", initialValue);
        }
      }
    } catch (err) {
      console.error("error", err);
    }
  }, [setValue, getValues]);

  useEffect(() => {
    getBranchList();
    // getCustomerList();
    getAgentList();
  }, [getBranchList, getAgentList]);

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Add New Route</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>

        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-100">
                <label htmlFor="routeTitle">Route Title</label>

                <input
                  {...register("route_name", {
                    required: "Please provide a title for the route.",
                  })}
                  id="routeTitle"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.route_name?.message}</p>
              </div>
              <div className="input-field wf-100">
                <label htmlFor="day">Day</label>

                <div className="input">
                  <select
                    id="day"
                    {...register("day", {
                      required: " Please select a day for the route. ",
                    })}
                  >
                    <option value="sunday">Sunday</option>
                    <option value="monday">Monday</option>
                    <option value="tuesday">Tuesday</option>
                    <option value="wednesday">Wednesday</option>
                    <option value="thursday">Thursday</option>
                    <option value="friday">Friday</option>
                    <option value="saturday">Saturday</option>
                  </select>
                </div>
                <p className="error-message">{errors.day?.message}</p>
              </div>
              <div className="select-input-field wf-100">
                <label htmlFor="branch">Branch</label>

                <Controller
                  name="branch_id"
                  control={control}
                  // rules={{ required: "Please select a branch." }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="branch"
                      options={options}
                      getOptionLabel={(option) => option?.name}
                      value={
                        options.length > 0
                          ? options.find(
                              (option) => option.id === getValues("branch_id")
                            )
                          : null
                      } // Set initial value
                      onChange={(_, value) => {
                        setValue("branch_id", value?.id ?? 0);
                      }}
                      // Update selected branch or default to first option
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Branch"
                          error={!!errors?.branch_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />

                {/* Show error message if the field is empty and not selected */}
                {errors?.branch_id && (
                  <p className="error-message" style={{ color: "#f44336" }}>
                    {errors?.branch_id?.message}
                  </p>
                )}
              </div>

              {/* <div className="wf-100">
                <div className="table-input-container2">
                  <h5>Customer & Priority</h5>
                  <div className="table-container scroll-bar-1">
                    <table>
                      <thead>
                        <tr>
                          <th>Customer Name</th>
                          <th>Priority</th>

                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {rows.map((row, index) => (
                          <tr key={index}>
                            <td className="customerName">
                              <div className="select-input-field ">
                                <Controller
                                  name={`customers.${index}.customer_id`}
                                  control={control}
                                  rules={{
                                    required: "Please select a customer.",
                                  }}
                                  render={({ field }) => (
                                    <Autocomplete
                                      {...field}
                                      id="customer"
                                      options={customerOptions}
                                      getOptionLabel={(option) =>
                                        option?.name || ""
                                      }
                                      value={
                                        customerOptions.length > 0
                                          ? customerOptions.find(
                                              (option) =>
                                                option.id ===
                                                getValues(
                                                  `customers.${index}.customer_id`
                                                )
                                            ) // Access customer_id for this row
                                          : null
                                      }
                                      onChange={(_, value) => {
                                        // Update the 'customer_id' for the specific row
                                        setRows((prevRows) =>
                                          prevRows.map((row, i) =>
                                            i === index
                                              ? {
                                                  ...row,
                                                  customer_id: value?.id ?? 0,
                                                }
                                              : row
                                          )
                                        );
                                        // Also update the form value for 'customer_id' in the customers array for the specific index
                                        setValue(
                                          `customers.${index}.customer_id`,
                                          value?.id ?? 0
                                        );
                                      }}
                                      renderInput={(params) => (
                                        <TextField
                                          {...params}
                                          placeholder="Select Customer"
                                          error={
                                            !!errors?.customers?.[index]
                                              ?.customer_id
                                          }
                                          variant="outlined"
                                          InputProps={{
                                            ...params.InputProps,
                                            style: {
                                              padding: "2px",
                                              borderRadius: "6px",
                                              backgroundColor: "#ffffff",
                                            },
                                          }}
                                          sx={{
                                            "& .MuiInputBase-input::placeholder":
                                              {
                                                opacity: 1,
                                              },
                                            "& .MuiOutlinedInput-root": {
                                              "& fieldset": {
                                                border: "none",
                                              },
                                              "&:hover fieldset": {
                                                border: "2px solid #D6D6D6",
                                              },
                                              "&.Mui-focused fieldset": {
                                                border: "2px solid #1E1E1E",
                                              },
                                              "& input": {
                                                padding: "10px",
                                              },
                                              "&.Mui-error fieldset": {
                                                border: "2px solid #D6D6D6",
                                              },
                                              "&.Mui-focused.Mui-error fieldset":
                                                {
                                                  border: "2px solid #1E1E1E",
                                                },
                                            },
                                          }}
                                        />
                                      )}
                                    />
                                  )}
                                />

                                {errors?.customers?.[index]?.customer_id && (
                                  <p
                                    className="error-message"
                                    style={{ color: "#f44336" }}
                                  >
                                    {
                                      errors?.customers?.[index]?.customer_id
                                        ?.message
                                    }
                                  </p>
                                )}
                              </div>
                            </td>
                            {/* <td>
                            <input
                              type="text"
                              placeholder="Type..."
                              {...register(`items.${index}.name`, { required: "Item Name is required" })}
                             
                            />
                            {errors.items?.[index]?.name && (
                    <p className="error-message">{errors.items[index].name.message}</p>
                  )}
                          </td> 
                            <td>
                              <input
                                type="text"
                                placeholder="Type..."
                                {...register(`customers.${index}.priority`, {
                                  required: "Priority is required",
                                })}
                              />
                              {errors?.customers?.[index]?.priority && (
                                <p className="error-message">
                                  {
                                    errors?.customers?.[index]?.priority
                                      ?.message
                                  }
                                </p>
                              )}
                            </td>

                            <td className="table-remove-button">
                              <button onClick={() => handleRemoveRow(index)}>
                                <span className="material-icons">delete</span>
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {rows.length === 0 && (
                    <div className="global-error">
                      <p className="error-message">
                        Please add at least one customer and priority.
                      </p>
                    </div>
                  )}
                  <div className="addRowBtn">
                    <button
                      className="closeButton"
                      onClick={handleAddRow}
                      type="button"
                    >
                      Add row
                    </button>
                  </div>
                </div>
              </div> */}

              {/* <div className="input-field wf-100">
                <label htmlFor="customers">Customers</label>
  
                <div className="input">
                  <select
                    id="customers"
                    {...register("customers", {
                      required: "Please select customers for this route.",
                    })}
                  >
                    <option value="">Select Customers</option>
                    <option value="">1</option>
                    <option value="">2</option>
                    <option value="">3</option>
                    <option value="">4</option>
                  </select>
                </div>
                <p className="error-message">{errors.customers?.message}</p>
              </div> */}

              <div className="select-input-field wf-100">
                <label htmlFor="branch">Agent</label>

                <Controller
                  name="agent_id"
                  control={control}
                  // rules={{ required: "Please select agent." }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="agent"
                      options={agentOptions}
                      getOptionLabel={(option) => option?.name}
                      value={
                        agentOptions.length > 0
                          ? agentOptions.find(
                              (option) => option.id === getValues("agent_id")
                            )
                          : null
                      } // Set initial value
                      onChange={(_, value) => {
                        setValue("agent_id", value?.id ?? 0);
                      }}
                      // Update selected branch or default to first option
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Agent"
                          error={!!errors?.agent_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />

                {/* Show error message if the field is empty and not selected */}
                {errors?.agent_id && (
                  <p className="error-message" style={{ color: "#f44336" }}>
                    {errors?.agent_id?.message}
                  </p>
                )}
              </div>
            </div>
            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default RouteCreate;
