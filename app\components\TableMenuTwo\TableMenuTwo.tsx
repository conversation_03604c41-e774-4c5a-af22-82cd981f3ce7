import React, { useRef, useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import './table-menu-two.scss';

export interface DropdownItem {
  id: string;
  label: string;
}

interface TableMenuTwoProps {
  items: DropdownItem[];
  onClick: (item: DropdownItem, id: number) => void;
  id: number;
}

const TableMenuTwo: React.FC<TableMenuTwoProps> = ({ items, onClick, id }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const menuRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLSpanElement | null>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);

  const handleToggle = () => {
    setIsOpen((prevState) => !prevState);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuRef.current &&
      !menuRef.current.contains(event.target as Node) &&
      buttonRef.current &&
      !buttonRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const menuWidth = 150; // Adjust based on menu width
      setMenuPosition({
        top: rect.top + window.scrollY, // Position below the button
        left: rect.left + window.scrollX - menuWidth + 20, // Position left of button
      });
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const renderDropdown = () => {
    if (!isOpen || !menuPosition) return null;

    return ReactDOM.createPortal(
      <>
        <div
          ref={menuRef}
          className={`menu ${isOpen ? 'show' : ''}`}
          style={{
            top: `${menuPosition.top}px`,
            left: `${menuPosition.left}px`,
          }}
        >
          <ul>
            {items.map((item) => (
              <li key={item.id} onClick={() => handleClick(item, id)}>
                {item.label}
              </li>
            ))}
          </ul>
        </div>
        <div className="overlay" onClick={() => setIsOpen(false)}></div>
      </>,
      document.body
    );
  };

  const handleClick = (item:DropdownItem,id:number) =>{
    setIsOpen(false);
    onClick(item,id);
  }

  return (
    <>
      <span
        ref={buttonRef}
        style={{fontSize: '18px'}}
        className="material-icons menu-button"
        onClick={handleToggle}
      >
        more_horiz
      </span>
      {renderDropdown()}
    </>
  );
};

export default TableMenuTwo;
