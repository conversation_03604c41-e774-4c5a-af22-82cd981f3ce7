@use '/app/styles/variables' as *;

// Main Container
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(#F2F2F2, #E0E0E0); /* Soft gradient background */
  padding: 20px;
  animation: fadeIn 1s ease-out; /* Animation for the background */
}

// Login Box
.login-box {
  background: $white_color;
  padding: 40px;
  border-radius: 15px; /* More rounded corners */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
  width: 100%;
  max-width: 400px;
  text-align: center;
  animation: slideIn 0.6s ease-out; /* Slide-in animation */
}

// Title
.login-title {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 28px;
  color: $primary_color;
  font-weight: bold;
  margin-bottom: 30px;
}

// Form
.login-form {
  .form-group {
    margin-bottom: 25px;
    text-align: left;

    label {
      display: block;
      font-size: 16px;
      color: $black_color2;
      margin-bottom: 8px;
    }

    .form-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 10px;
      font-size: 16px;
      outline: none;
      transition: border-color 0.3s, box-shadow 0.3s;
      
      &:focus {
        border-color: $primary_color;
        box-shadow: 0 0 8px rgba(255, 106, 0, 0.5);
      }
    }
  }
  .error {
    color: #ff4d4f; /* Red color for errors */
    font-size: 12px;
    font-weight: 400;
    margin-top: 5px;
    text-align: left;
  }
}

// Button
.login-button {
  width: 100%;
  padding: 12px 18px;
  background-color: $primary_color;
  color: $white_color;
  font-size: 18px;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: transform 0.3s, background-color 0.3s;

  &:hover {
    background-color: darken($primary_color, 10%);
    transform: scale(1.05); /* Button hover effect */
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.password-wrapper {
  position: relative;
}

.password-wrapper input {
  width: 100%;
  padding-right: 40px; /* Space for the toggle button */
  box-sizing: border-box;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 16px;
  cursor: pointer;
  outline: none;
}

.toggle-password:hover {
  color: #3182ce;
}

//media queries
@media(max-width: $breakpoint-sm ){
  .login-title {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .login-form {
    .form-group {
      margin-bottom: 15px;
  
      label {
        font-size: 11px;
        margin-bottom: 5px;
      }
  
      .form-input {
        width: 100%;
        padding: none;
        border-radius: 8px;
        font-size: 16px;

        &::placeholder{
          font-size: 10px;
        }
      }
    }
    .error {
      font-size: 8px;
      margin-top: 5px;

    }
  }

  .toggle-password {
    
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1px;

    span{
      font-size: 15px;
    }
    
  }

  .login-button {
    width: 100%;
    padding: 6px 18px;
    background-color: $primary_color;
    color: $white_color;
    font-size: 13px;
    font-weight: bold;
    border: none;
    border-radius: 10px;
 
  

  }
  

}
