'use client'
import React, { useCallback, useEffect, useState } from "react";
import "./PurchaseOrderDetail.scss";
import ReactDOM from "react-dom";
import {deleteOrderAttachment,editPurchaseOrder,getCustomerOrderDetails,} from "@/app/pages/customer/customer-service";
import {Discount,purchaseOrderAttachment,purchaseOrderDetail,} from "@/app/pages/customer/customer.model";
import AddAttachment from "@/app/components/AddAttachment/AddAttachment";
import { useCommonContext } from "@/app/contexts/commonContext";
import { postOrderAttachments } from "@/app/pages/purchaseOrder/purchaseOrder-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import AddDiscount from "./components/AddDiscount";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";

interface prop {
  purchase_order_id: number;
  isOpen: boolean | undefined;
  handleClose: VoidFunction;
}

export interface DropdownItem {
  id: string;
  label: string;
}

function PurchaseOrderDetail({ purchase_order_id, isOpen, handleClose }: prop) {
  const [detail, setDetail] = useState<purchaseOrderDetail>();
  const [remarkValue, setRemarkValue] = useState<string>("");
  const [isAttachMentOpen, setIsAddAttachmentOpen] = useState<boolean>(false);
  const [showAddDiscount, setShowAddDiscount] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState(false);
  const [payMode, setPayMode] = useState("weekly");
  const [monthlyEmiDate,setMonthlyEmiDate] = useState<number | undefined>()
  const [weeklyEmiDate,setWeeklyEmiDate] = useState<string>("")
  const [isTableLoading, setIsTableLoading] = useState<boolean>(true);
  const { setIsLoading } = useCommonContext();
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;
  const { fire } = useAlert();

  const getDayOptions = () => {
    const suffixes = ["th", "st", "nd", "rd"];
    return Array.from({ length: 31 }, (_, i) => {
      const day = i + 1;
      const suffix =
        day % 10 < 4 && day % 10 > 0 && (day < 11 || day > 13)
          ? suffixes[day % 10]
          : "th";
      return { label: `${day}${suffix}`, value: day }; // Return object
    });
  };
  const dayOptions = getDayOptions();
  
  const getDetail = useCallback(async () => {
    setIsTableLoading(true)
    try {
      const api = await getCustomerOrderDetails(purchase_order_id);
      setDetail(api);
      setPayMode(api.emi_type)
      setRemarkValue(api.remark);
      if (api.emi_type === "monthly") {
        setMonthlyEmiDate(api.monthly_emi_due_date);
      } else if (api.emi_type === "weekly") {
        setWeeklyEmiDate(api.weekly_emi_due_date);
      }
    } catch (error) {
      console.log(error);
      //alert
      fire({
        icon: "error", // Use success icon
        title: "Some error occured",
        text: "Please Try Again",
        confirmButtonText:"Ok"
      });
    } finally {
      setIsTableLoading(false)
      console.log(""); // Can be removed if not necessary
    }
  }, [purchase_order_id,fire]);

  useEffect(() => {
    if (
      typeof purchase_order_id === "number" &&
      !isNaN(purchase_order_id) &&
      isOpen
    ) {
      getDetail();
    }
  }, [isOpen, purchase_order_id, getDetail]);

  const addAttachment = (uploadedFile: { file: File; label: string }) => {
    setIsLoading(true);
    const formData = new FormData();
    formData.append("uploded_file", uploadedFile.file);
    formData.append("title", uploadedFile.label);
    formData.append("order_id", purchase_order_id.toString());
    postOrderAttachments(formData)
      .then((res) => {
        if (res) {
          setIsLoading(false);
          getDetail();
          setIsAddAttachmentOpen(false);
        }
        handleClose();
      }).catch((error) => {
        setIsLoading(false);
        fire({
          position: "center",
          icon: "error",
          title: "An Error Occured",
          text: error.response?.data?.detail || error.message,
          confirmButtonText: "Ok",
        });
      });
  };

  const deleteAttachment = async (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: "Are you sure?",
      text: "This action will permanently delete the attachment.",
      confirmButtonText: "Ok",
      cancelButtonText: "Cancel",
      onConfirm: () => {
        setIsLoading(true);
        deleteOrderAttachment(id)
          .then((res) => {
            getDetail();
            fire({
              position: "top-right",
              icon: "success",
              title: "Deleted successfully",
              autoClose:2000
            });
            if (res) {
            }
          }).catch((error) => {
            fire({
              position: "center",
              icon: "error",
              title: "Some error occured",
              text:
                error?.response?.data?.detail ||
                error?.message ||
                "An Error Occured Please Try Again",
              confirmButtonText: "Ok",
              onConfirm: () => {
                // console.log("Confirmed:");
              },
            });
            setIsLoading(false);
          });
      },
    });
  };

  const handleShowDiscountCreate = () => {
    setShowAddDiscount(true);
  };

  const handleCloseDiscountCreate = () => {
    getDetail()
    setShowAddDiscount(false);
  };

  const menuItems = [{ id: "add-discount", label: "Add Discount" }];
  const handleMenuItemClick = (item: DropdownItem) => {
    if (item.id == "add-discount") {
      handleShowDiscountCreate();
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const body = {
        emi_type: payMode,
        emi_period_type: payMode,
        monthly_emi_due_date: monthlyEmiDate,
        weekly_emi_due_date: weeklyEmiDate,
        remarks: remarkValue
      };
      const response = await editPurchaseOrder(purchase_order_id, body);
      console.log("order edit successfull", response);
      fire({
        icon: "success", // Use success icon
        title: "Order edited",
        text: "The Order is edited successfully!",
        position: "top-right",
        autoClose: 2000,
      });
      handleClose();
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(()=>{
    if(payMode === "weekly"){
      if(!weeklyEmiDate){
        setWeeklyEmiDate("Sunday")
      }
    }else{
      if(!monthlyEmiDate){
        setMonthlyEmiDate(1)
      }
    }
  },[payMode,monthlyEmiDate,weeklyEmiDate])

  useEffect(()=>{
    if(isOpen){
      setIsEditing(false)
    }
  },[isOpen])

  return (
    <div
      className={`transaction-additional-details ${
        isOpen ? "showData" : "hideData"
      }`}
    >
      {isTableLoading ? (
        <TableWithShimmer no_of_cols={5} no_of_rows={4} />
      ) : (
        <>
          <div className="t-a-d-row">
            <div className="t-a-d-col col1">
              <div className="attachedDocuments">
                <h5>Attached documents </h5>
                <div className="docs-field-border">
                  <div className="docs-field">
                    <ul>
                      {detail?.attachments &&
                        detail.attachments.length > 0 &&
                        detail?.attachments?.map(
                          (doc: purchaseOrderAttachment, index: number) => (
                            <li key={index}>
                              <a href={mediaUrl + doc.uploaded_file}>
                                {" "}
                                {doc.title}{" "}
                              </a>
                              <button className="delete-bttn">
                                <div
                                  className="material-icons"
                                  onClick={() => deleteAttachment(doc.id)}
                                >
                                  delete
                                </div>
                              </button>
                            </li>
                          )
                        )}
                      <div className="addBtn">
                        <button
                          className="addDocButton"
                          onClick={() => setIsAddAttachmentOpen(true)}
                        >
                          <span>+</span> Add Document
                        </button>
                      </div>
                    </ul>
                  </div>

                  <div className="t-a-d-border">
                    <div className="border"></div>
                  </div>
                </div>
              </div>

              <div className="DisountHistory">
                <div className="discountHistoryHead">
                  <h5>Discount History</h5>{" "}
                  <div className="moreVertButton">
                    <TableMenuTwo
                      items={menuItems}
                      onClick={handleMenuItemClick}
                      id={detail?.id || 1}
                    />
                  </div>
                </div>
                <div className="checkbox-date-time-discount-border-box">
                  <div className="checkbox-date-time-discount-container">
                    {detail?.discount &&
                      detail.discount.length > 0 ?
                      detail.discount.map(
                         (history: Discount, index: number) => (
                          <div
                            className="checkbox-date-time-discount-box"
                            key={index}
                          >
                            {/* <input type="checkbox" /> */}

                            <div className="date-time">
                              <span className="date">
                                {new Date(
                                  history.created_at
                                    ? history.created_at
                                    : history.description
                                ).toLocaleDateString()}
                              </span>
                              <span className="time">
                                {new Date(history.applied_by_user_name).toLocaleTimeString()}
                              </span>
                            </div>

                            <span className="hyphen">-</span>

                            <span className="discount">{history.amount}</span>
                          </div>
                        )
                      ) : (
                      <p className="error-message">No discount history available.</p>
                      )}
                  </div>

                  <div className="t-a-d-border">
                    <div className="border"></div>
                  </div>
                </div>
              </div>

              <div className="t-a-d-col col2">
                <div className="advAmt-payMode-dueDate">
                  <div className="advAmt">
                    <h5>Advance Amount</h5>
                    {detail?.advance_amount ? (
                      <h4>{detail?.advance_amount}</h4>
                    ):(
                      <h6>N/A</h6>
                    )}
                  </div>

                  {/* <div className="payMode">
                  <h5>Payment Mode</h5>
                  <h4 className="plan">
                    {detail?.emi_type}{" "}
                    <span className="material-icons">edit</span>
                  </h4>
                </div> */}

                  <div className="payMode">
                    <h5>Payment Mode</h5>
                    {isEditing ? (
                      // Dropdown to select payment mode
                      <select
                        value={payMode}
                        onChange={(e) => setPayMode(e.target.value)}
                        // onBlur={() => setIsEditing(false)} // Exit edit mode when focus is lost
                        autoFocus
                      >
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    ) : (
                      <h4 className="plan">
                        {payMode}
                        <span
                          className="material-icons"
                          onClick={() => setIsEditing(true)}
                          style={{ cursor: "pointer", marginLeft: "8px" }}
                        >
                          edit
                        </span>
                      </h4>
                    )}
                  </div>
                  {isEditing ? (
                  <div className="payMode">
                     {payMode === "weekly" ? (
                        <>
                          <h5>Weekly Emi Day</h5>
                          <select value={weeklyEmiDate} onChange={(e) => setWeeklyEmiDate(e.target.value)} autoFocus>
                            <option value="Sunday">Sunday</option>
                            <option value="Monday">Monday</option>
                            <option value="Tuesday">Tuesday</option>
                            <option value="Wednesday">Wednesday</option>
                            <option value="Thursday">Thursday</option>
                            <option value="Friday">Friday</option>
                            <option value="Saturday">Saturday</option>
                          </select>
                        </>
                      ):(
                        <>
                        <h5>Monthly Emi Day</h5>
                        <select value={monthlyEmiDate} onChange={(e) => setMonthlyEmiDate(Number(e.target.value))} autoFocus>
                          {dayOptions.map((item, index) => (
                            <option key={index} value={item.value}>{item.label}</option>
                          ))}
                        </select>
                        </>
                      )}
                  </div>
                  ):(
                  <div className="dueDate">
                    <h5>Next due date</h5>
                    <h4>
                      {detail?.due_date ? new Date(detail.due_date).toLocaleDateString() : "N/A"}
                    </h4>
                  </div>
                  )}
                </div>

                <div className="remark">
                  <p>Remark</p>
                  <div className="remark-text-field">
                    <textarea
                      placeholder="remark"
                      value={remarkValue}
                      onChange={(e) => setRemarkValue(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "end",
              padding: "10px",
              alignItems:"center",
              gap:"5px"
            }}
          >
            <button className="rejectButton" onClick={() => {handleClose();}}>
              Cancel
            </button>
            <button className="acceptButton" onClick={() => {handleSave();}}>
              Save
            </button>

          </div>
        </>
      )}
      {ReactDOM.createPortal(
              <AddAttachment
              showCreate={isAttachMentOpen}
              handleCloseCreate={() => setIsAddAttachmentOpen(false)}
              onFileUpload={addAttachment}
            />,document.body
      )}

      {ReactDOM.createPortal(
          <AddDiscount
            orderId={purchase_order_id}
            showAddDiscount={showAddDiscount}
            handleCloseDiscountCreate={handleCloseDiscountCreate}
          />,document.body
      )}


    </div>
  );
}

export default PurchaseOrderDetail;
