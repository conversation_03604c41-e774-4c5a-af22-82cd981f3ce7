@import '/app/styles/variables.scss';

.customer-overview-container{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    gap: 15px;
    background-color: $white_color1;
    //padding: 15px 0 0 0 ;

    .customer-overview{
        width: calc((100% - 60px) / 4);
        min-width: 250px;
        //height: 100px;
        display: flex;
        flex-direction: column;
        background-color: $white_color;
        border: 1px solid $black_color4;
        border-radius: 5px;

        @media (max-width: $breakpoint-md) {
            width: -webkit-fill-available;
         }

        .header{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid $black_color4 ;
            //flex: 0 0 auto;
            h3{
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 14px;
                

                span{
                    padding: 2px 3px;
                    border-radius: 6px;
                    margin-right: 8px;
                    font-size: 18px;
                   
                }
                
                .groups{
                    background-color: $green_color3;
                    color: $green_color2;
                    border: 1px solid $green_color;
                   
                }

                .person{
                    background-color: $red_color3;
                    color: $red_color;
                    border: 1px solid $red_color2;
                    padding: 2px 3px;
                    border-radius: 6px;

                }

                .group{
                    background-color: #9286DD1A;
                    color: #9286DD;
                    border: 1px solid #9286DD66;
                    padding: 2px 3px;
                    border-radius: 6px;
                }

                .group2{
                    background-color: #D65CE01A;
                    color:#D65CE0;
                    border: 1px solid #D65CE066;
                    padding: 2px 3px;
                    border-radius: 6px;
                }

            

             
            }

            .morehorizIcon{
                cursor: pointer;
            }

            @media (max-width: $breakpoint-md) {
                padding: 8px;
        
                h3 {
                    font-size: 12px;
        
                    span {
                        font-size: 16px;
                        margin-right: 6px;
                    }
                }
        
                .morehorizIcon {
                    font-size: 20px; 
                }
            }
        
            @media (max-width: $breakpoint-sm) {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
        
                h3 {
                    font-size: 12px;
        
                    span {
                        font-size: 14px;
                        //margin-right: 4px;
                    }
                }
        
                .morehorizIcon {
                    font-size: 18px;
                }
            }
        }
        .content{
            display: flex;
            justify-content: start;
            align-items: center;
            //flex: 1;
            padding: 20px 15px;

            h2{
                font-weight: 500;
                padding-right: 10px;
                font-size: 30px;
              
            }

         

               .raise{
                color: $green_color2;
                background-color: $green_color3;
                border: 1px solid $green_color;
                border-radius: 3px;
                display: flex;
                align-items: center;
                font-size: 11px;
                padding: .8px 2px;

                .arrow-upward{
                    padding-left: 2px;
                    font-size: 10px;
                }
           

          
            }


            p{
                padding-left: 5px;
                font-size: 12px;
                color: $black_color2;
            }

            .fall{
                color: $red_color;
                background-color: $red_color3;
                border: 1px solid $red_color2;
                border-radius: 3px;
                display: flex;
                align-items: center;
                font-size: 11px;
                padding: .8px 2px;

                .arrow-upward{
                    padding-left: 2px;
                    font-size: 10px;
                }
           

          
            }

    

    
            @media (max-width: $breakpoint-md) {
                flex-direction: column;
                align-items: flex-start;
                padding: 15px 10px;
        
                h2 {
                    font-size: 24px;
                    padding-right: 0;
                    margin-bottom: 8px; // Adds spacing when stacked
                }
        
                .raise,
                .fall {
                    font-size: 10px;
                    padding: 0.6px 2px;
        
                    .arrow-upward {
                        font-size: 8px;
                    }
                }
        
                p {
                    font-size: 10px;
                    padding-left: 0;
                    padding-top: 5px;
                }
            }
        
            @media (max-width: $breakpoint-sm) {
                h2 {
                    font-size: 20px;
                }
        
                .raise,
                .fall {
                    font-size: 9px;
                    padding: 0.4px 2px;
        
                    .arrow-upward {
                        font-size: 7px;
                    }
                }
        
                p {
                    font-size: 9px;
                    padding-top: 5px;
                }
            }
        
        }
    }


}