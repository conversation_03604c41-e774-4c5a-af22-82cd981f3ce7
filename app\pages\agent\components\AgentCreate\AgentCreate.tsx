import React, { useCallback, useEffect, useState } from "react";
import "./AgentCreate.scss";
import { useF<PERSON>, SubmitH<PERSON><PERSON>, Controller } from "react-hook-form";
import { createAgent } from "../../agent.model";
import { Autocomplete, TextField } from "@mui/material";
import {
  createAgentAPi,
  editAgent,
  getAgentDetail,
  getBrachList,
} from "../../agent-service";
import FileUpload from "@/app/components/utilities/Fileupload/FileUpload";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { Branch_List_Item } from "@/app/pages/branch/branch.model";
import { AxiosError } from "axios";

interface agentCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  id?: number;
}

interface ErrorResponseData {
  detail?: string;
}

function AgentCreate({ showCreate, handleCloseCreate, id }: agentCreateProps) {
  const { fire } = useAlert();
  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png"];
  const [profileImage, setProfileImage] = useState<string | File | null>(null);
  const [branchList, setBranchList] = useState<Branch_List_Item[]>([]);
  const { setIsLoading } = useCommonContext()
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    getValues,
    setError,
    control,
    formState: { errors },
  } = useForm<createAgent>();

  const onSubmit: SubmitHandler<createAgent> = (data) => {
    setIsLoading(true);
    if (!data.profile_photo) {
      fire({
        position: "center",
        icon: "error",
        title: "An Error Occured",
        text: "Please Add Profile Photo",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      setError("profile_photo", {
        type: "manual",
        message: "Please upload a Profile Image.",
      });
      setIsLoading(false);
      return;
    }
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("agent_code", data.agent_code);
    formData.append("phone", data.phone);
    formData.append("address", data.address);
    formData.append("email", data.email);
    formData.append("branch_id", String(data.branch_id));
    if (!id) {
      formData.append("password", data.password);
    }
    if (data.profile_photo && data.profile_photo instanceof File) {
      formData.append("profile_photo", data.profile_photo);
    }
    if (id) {
      editAgent(id, formData)
        .then((res) => {
          if (res) {
            setIsLoading(false);
            fire({
              position: "top-right",
              icon: "success",
              title: "Edited Successfully",
              autoClose: 2000,
            });
            handleCloseCreate();
          }
        })
        .catch((error) => {
          const axiosError = error as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          setIsLoading(false);
        });
    } else {
      createAgentAPi(formData)
        .then((res) => {
          if (res) {
            handleCloseCreate();
            setIsLoading(false);
            fire({
              position: "top-right",
              icon: "success",
              title: "Created Successfully",
              autoClose: 2000,
            });
          }
        })
        .catch((error) => {
          setIsLoading(false);
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              error?.response?.data?.detail ||
              error?.message ||
              "An Error Occured Please Try Again",
            confirmButtonText: "Ok",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
        });
    }
  };

  const patchValue = useCallback(async () => {
    if (id) {
      setIsLoading(true);
      try {
        const api = await getAgentDetail(id);
        setValue("name", api.name);
        setValue("agent_code", api.agent_code);
        setValue("address", api.address);
        setValue("email", api.email);
        setValue("phone", api.phone);
        setValue("profile_photo", api.profile_photo);
        if(api.branch_name){
          const branch_id =  branchList.find((item => item.name === api.branch_name))
          if(branch_id){
            setValue('branch_id', branch_id.id)
          }
        }
        setProfileImage(api.profile_photo);
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [id, setValue, setIsLoading,branchList]);

  const getBranchLists = useCallback(async () => {
    setIsLoading(true);
    try {
      const api = await getBrachList();
      setBranchList(api.results);
    } catch (error) {
      console.log("error", error);
      fire({
        position: "center",
        icon: "error",
        title: "Some error occurred",
        text: "Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
    } finally {
      setIsLoading(false);
    }
  }, [fire,setIsLoading]);
  

  useEffect(() => {
    getBranchLists();
  }, [getBranchLists]);

  useEffect(() => {
    if (!showCreate) {
      reset();
    } else {
      patchValue();
    }
  }, [showCreate, reset, patchValue]);



  const onFileUpload = (file: File | null) => {
    //console.log(file,"file changed");
    setProfileImage(file);
    setValue("profile_photo", file); // Update form value
  };

  useEffect(() => {
    if (!getValues("branch_id") && branchList.length > 0) {
      setValue("branch_id", branchList[0].id); // Set the default value only once
    }
  }, [branchList, setValue, getValues]);

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>{id ? "Edit" : "Add"} Agent</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="name">Agent Name</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Type..."
                  {...register("name", {
                    required: "Please provide the agent's name.",
                  })}
                />
                <p className="error-message">{errors.name?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="agent_code">Agent Code</label>
                <input
                  {...register("agent_code", {
                    required: "Please enter the 'Agent Code'.",
                    maxLength: {
                      value: 10,
                      message: "Agent Code cannot exceed 10 characters.",
                    },
                  })}
                  id="agent_code"
                  type="text"
                  placeholder="Type..."
                  maxLength={10} // Enforces the character limit in the input field
                />
                <p className="error-message">{errors.agent_code?.message}</p>
              </div>
              <div className={`input-field ${id ? "wf-100" : "wf-50"}`}>
                <label htmlFor="email">Email</label>

                <input
                  {...register("email", {
                    required: "Please enter an email address.",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // Regex for email validation
                      message:
                        "The email format is invalid. Please enter a valid email address.",
                    },
                  })}
                  id="email"
                  type="email"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.email?.message}</p>
              </div>
              {!id && (
                <div className="input-field wf-50">
                  <label htmlFor="password">Password</label>
                  <input
                    {...register("password", {
                      required: "Please enter the password.",
                    })}
                    id="password"
                    type="text"
                    placeholder="Type..."
                  />
                  <p className="error-message">{errors.password?.message}</p>
                </div>
              )}

              <div className="input-field wf-100">
                <label htmlFor="address">Address</label>

                <input
                  {...register("address", {
                    required: "Please enter the agent's address.",
                  })}
                  id="address"
                  type="text"
                  placeholder="Type..."
                />

                <p className="error-message">{errors.address?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="phone">Phone</label>
                <input
                  {...register("phone", {
                    required: "Please provide a phone number.",
                  })}
                  id="phone"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone?.message}</p>
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="branch_id">Branch</label>
                <Controller
                  name="branch_id"
                  control={control}
                  rules={{ required: "Please select a Branch." }} // Adding required rule with custom message
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="branch_id"
                      options={branchList}
                      getOptionLabel={(option) => option?.name}
                      value={
                        branchList.length > 0
                          ? branchList.find(
                              (option) => option.id === getValues("branch_id")
                            ) || null
                          : null
                      }
                      onChange={(_, value) => {
                        setValue("branch_id", value?.id ?? 0); // Store the selected customer's `id`
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Customer"
                          error={!!errors?.branch_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                <p className="error-message">{errors.branch_id?.message}</p>
              </div>
              <div className="wf-100">
                <FileUpload
                  uploadedFile={profileImage}
                  isOpen={showCreate}
                  allowedTypes={ALLOWED_TYPES}
                  maxSize={MAX_FILE_SIZE}
                  onFileUpload={onFileUpload}
                  label="Profile Image"
                  requiredMessage="Please upload a Profile Image."
                  maxFileSizeMessage="Profile image size is too large."
                  invalidTypeMessage="Invalid Profile image type."
                />
                {errors.profile_photo && (
                  <p className="error-message">
                    {errors.profile_photo.message}
                  </p>
                )}
              </div>

              {/* <div className="input-field wf-50">
                <label htmlFor="branch_id">Branch</label>
                <input
                  {...register("branch_id", {
                    required: "Please Select a Branch.",
                  })}
                  id="branch_id"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.branch_id?.message}</p>
              </div> */}
              {/* <div className="input-field wf-50">
                <label htmlFor="city">City</label>
                <input
                  {...register("city", { required: "Please enter the city." })}
                  id="city"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.city?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="pin">Pin</label>
                <input
                  {...register("pin", { required: "Please enter the pincode." })}
                  id="pin"
                  type="number"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.pin?.message}</p>
              </div> 
              <div className="input-field wf-50">
                <label htmlFor="primaryContact">Primary Contact</label>
                <input
                  {...register("primaryContact", {
                    required: "Please provide a primary contact number.",
                  })}
                  id="primaryContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.primaryContact?.message}</p>
              </div> 
              <div className="input-field wf-50">
                <label htmlFor="whatsappContact">Whatsapp Contact</label>
                <input
                  {...register("whatsappContact", {
                    required: "Please provide a WhatsApp contact number.",
                  })}
                  id="whatsappContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.whatsappContact?.message}</p>
              </div> */}
            </div>
            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default AgentCreate;
