import React, { useEffect, useState } from "react";
import "./edit-customer.scss";
import { useForm, SubmitHandler } from "react-hook-form";
import { customer_edit } from "../../customer.model";
import {  getCustomerDetails, updateCustomer } from "../../customer-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import FileUpload from "@/app/components/utilities/Fileupload/FileUpload";
import RatingComponent from "@/app/components/Rating/Rating";
import { useCommonContext } from "@/app/contexts/commonContext";

interface customerCreateProps {
  id:number | null;
  isOpen: boolean;
  handleCloseCreate: VoidFunction;
}


function EditCustomer({isOpen,handleCloseCreate,id}: customerCreateProps) {
  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png"];
  const { fire } = useAlert();
  const {setIsLoading} = useCommonContext()
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    // watch,
    formState: { errors },
  } = useForm<customer_edit>();
  const [uploadFile,setUploadFile] = useState<string | File | null>(null);
  const onSubmit: SubmitHandler<customer_edit> = async(data) => {
    setIsLoading(true)
    const formData = new FormData();
    formData.append('name',data.name);
    formData.append('short_title',data.short_title);
    formData.append('city',data.city);
    formData.append('care_of',data.care_of);
    formData.append('address',data.address);
    formData.append('pincode', data.pincode.toString());
    formData.append('son_of',data.son_of);
    formData.append('code',data.code);
    formData.append('phone_number',data.phone_number);
    formData.append('whatsapp_number',data.whatsapp_number);
    formData.append('email',data.email);
    formData.append('phone_number2',data.phone_number2);
    formData.append('phone_number3',data.phone_number3);
    formData.append('phone_number4',data.phone_number4);
    formData.append('star_rating',String(data.star_rating))
    if(data.profile_photo instanceof File){
      formData.append('profile_photo',data.profile_photo);
    }
    updateCustomer(id,formData).then((res)=>{
      if(res){
        handleCloseCreate()
        setIsLoading(false)
        fire({
          position: "top-right",
          icon: "success",
          title: "Edited Successfully",
          autoClose: 2000
        })
      }
    }).catch((error)=>{
      setIsLoading(false)
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text: error?.response?.data?.detail || error?.message || "An unknown error occurred",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      })

      console.log("error", error);
      
    })
  };


  const onFileUpload = (file: File | null) => {
    setUploadFile(file)
    setValue("profile_photo", file); // Update form value
  };

  useEffect(()=>{
    if(!isOpen){
      reset()
    }else{
        if(id){
            getCustomerDetails(id).then((res)=>{
                setValue("name",res.name);
                setValue("code",res.code)
                setValue("short_title",res.short_title);
                setValue("son_of",res.son_of);
                setValue("care_of",res.care_of);
                setValue("phone_number",res.phone_number);
                setValue("whatsapp_number",res.whatsapp_number);
                setValue("email",res.email);
                setValue("phone_number2",res.phone_number2);
                setValue("phone_number3",res.phone_number3);
                setValue("phone_number4",res.phone_number3);
                setValue("star_rating",res.star_rating);
                setValue("profile_photo",res.profile_photo);
                setValue("address",res.address);
                setValue("city",res.city);
                setValue("pincode",res.pincode);
                if(res.profile_photo){
                  setUploadFile(res.profile_photo)
                }
            })
        }
    }
  },[isOpen,id,reset,setValue])

  const handleRatingChange = (event: React.SyntheticEvent, newValue: number | null) => {
    console.log("New Rating:", newValue);
    if(newValue){
        setValue("star_rating", newValue);
    }
  };
  return (
    <div className={`create-form-overlay ${isOpen ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Edit Customer</h3>
          <span className="material-icons closeIcon" onClick={handleCloseCreate}>close</span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="cName">Customer Name</label>
                <input
                  {...register("name", {
                    required: "Please enter the customer's name.",
                  })}
                  id="cName"
                  type="text"
                  placeholder="Type..."
                />
                {<p className="error-message">{errors.name?.message}</p>}
              </div>
              <div className="input-field wf-50">
                <label htmlFor="code">Customer Code</label>
                <input
                  {...register("code")}
                  id="code"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">
                  {errors.code?.message}
                </p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="sName">Short Name</label>
                <input
                  {...register("short_title", {
                    required: "Please enter a short name for the customer.",
                  })}
                  id="sName"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.short_title?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="s/o">S/o D/o W/o</label>
                <input
                  {...register("son_of")}
                  id="s/o"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.son_of?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="c/o">C/o</label>
                <input
                  {...register("care_of")}
                  id="c/o"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.care_of?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="email">Email</label>
                <input
                  {...register("email")}
                  id="email"
                  type="email"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.email?.message}</p>
              </div>  
              <div className="input-field wf-100">
                <label htmlFor="address">Address</label>
  
                <input
                  {...register("address", {
                    required: "Please provide the customer's address.",
                  })}
                  id="address"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.address?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="city">City</label>
                <input
                  {...register("city")}
                  id="city"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.city?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="pin">Pin</label>
                <input
                  {...register("pincode")}
                  id="pin"
                  type="number"
                  placeholder="Type..."
                  max={999999} // Limits the number to 6 digits
                  onInput={(e) => {
                    if (e.currentTarget.value.length > 6) {
                      e.currentTarget.value = e.currentTarget.value.slice(0, 6);
                    }
                  }}
                />
                <p className="error-message">{errors.pincode?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="primaryContact">Primary Contact</label>
                <input
                  {...register("phone_number", {
                    required:
                      " Please enter the primary contact number for the customer. ",
                  })}
                  id="whatsappContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone_number?.message}</p>
              </div> 
              <div className="input-field wf-50">
                <label htmlFor="whatsappContact">Whatsapp Contact</label>
                <input
                  {...register("whatsapp_number", {
                    required:
                      "Please provide the customer's WhatsApp contact number. ",
                  })}
                  id="whatsappContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.whatsapp_number?.message}</p>
              </div>  
              <div className="input-field wf-50">
                <label htmlFor="SecondaryContact">Secondary Contact</label>
                <input
                  {...register("phone_number2")}
                  id="SecondaryContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">
                  {errors.phone_number2?.message}
                </p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="phone3">Additional Contact</label>
                <input
                  {...register("phone_number3")}
                  id="phone3"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">
                  {errors.phone_number3?.message}
                </p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="phone4">Additional Contact 2</label>
                <input
                  {...register("phone_number3")}
                  id="phone4"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">
                  {errors.phone_number4?.message}
                </p>
              </div>
              <div className="wf-50">
                <label htmlFor="rating">Rating</label>
                <div id="rating">
                    <RatingComponent size="large" isReadOnly={false} rating={watch('star_rating')} onChange={handleRatingChange}  />
                </div>
              </div>
              <div className="wf-100">
                <FileUpload
                  uploadedFile={uploadFile}
                  isOpen={isOpen}
                  allowedTypes={ALLOWED_TYPES}
                  maxSize={MAX_FILE_SIZE}
                  onFileUpload={onFileUpload}
                  label="Profile Image"
                  requiredMessage="Please upload a Profile Image."
                  maxFileSizeMessage="Profile image size is too large."
                  invalidTypeMessage="Invalid Profile image type."
                />
                {errors.profile_photo && <p className="error-message">{errors.profile_photo.message}</p>}
              </div>
            </div>
            <div style={{marginTop:'0px'}} className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default EditCustomer;
