"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./Home.scss";
import OverallOverview from "./components/OverallOverview";
import Image from "next/image";

import Pagination from "@/app/components/utilities/Pagination/Pagination";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import ViewFileIcon from "../../assets/svg/viewFileIcon.svg";
import { approvalStatusUpdate, fetchBankTransaction } from "./home-service";
import DateTimeDisplay from "@/app/components/utilities/DateTimeDisplay/DateTimeDisplay";
import { useCommonContext } from "@/app/contexts/commonContext";
import { Bank_Transaction_List_Item } from "./home.model";
import ViewScreenshot from "./components/ViewScreenshot/ViewScreenshot";
import TableWithShimmer from "@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";
import dummyprofile from "../../../public/images/profile.png";

interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const { fire } = useAlert();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);

  const [bankTransactionList, setBankTransactionList] = useState<
    Bank_Transaction_List_Item[]
  >([]);

  const {  setIsLoading , maxDate } = useCommonContext();
  //filters
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  const [showScreenshot, setShowScreenshot] = useState<boolean>(false);
  const [screenshotUrl, setScreenShotUrl] = useState<string>("");
  const [isHeaderReceived, setIsHeaderReceived] = useState<boolean>(true);
  const [visible, setVisible] = useState<boolean>(false);
  const [isTableLoading,setIsTableLoading] = useState<boolean>(false)
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;

  const handleHeaderReceived = () => {
    setIsHeaderReceived(false);
  };

  const handleShowScreenshot = (imgUrl: string) => {
    setShowScreenshot(true);
    setScreenShotUrl(imgUrl);
  };

  const handleCloseScreenshot = () => {
    setShowScreenshot(false);
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPageNo: number,
      searchKey: string,
      status: string,
      date: string
    ) => {
      const skip = (pageNo - 1) * itemsPerPageNo;
      try {
        //setIsLoading(true);
        setIsTableLoading(true)
        const response = await fetchBankTransaction(
          skip,
          itemsPerPageNo,
          searchKey,
          status,
          date
        );
        setBankTransactionList(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error("Failed to load bank transactions", err);
      } finally {
        setIsTableLoading(false);
      }
    },
    [setIsTableLoading]
  );

  // fetch  bank transactions
  useEffect(() => {
    getList(1, 10, "", "", "");
  }, [getList]);

  const handleShowAcceptAlert = (transaction_id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: "Are you Sure?",
      text: "Are you sure to confirm the transaction.",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        const body = {
          approval_status: "approve",
          remarks: "",
        };

        try {
          setIsLoading(true);
          await approvalStatusUpdate(transaction_id, body);
          //console.log("handle show reject result", response);
          fire({
            position: "top-right",
            icon: "success",
            title: "Transaction Confirmed",
            text: "The transaction confirmed successfully!",
            autoClose: 2000,
          });
          getList(currentPage, itemsPerPage, searchValue, status, selectedDate);
          setIsHeaderReceived(true);
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error(err);
          //setIsLoading(false);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleShowRejectAlert = (transaction_id: number) => {
    fire({
      position: "center",
      icon: "error",
      title: "Are you Sure?",
      text: "Are you sure to reject the transaction.",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      initialValue: "ഈ തുക ബാങ്ക് അക്കൗണ്ടിൽ ക്രെഡിറ്റ് ആയിട്ടില്ല",
      onConfirm: async (value: string) => {
        //console.log("Input Value:", value);
        const body = {
          approval_status: "reject",
          remarks: value,
        };
        try {
          setIsLoading(true);
          await approvalStatusUpdate(transaction_id, body);
          //console.log("handle show reject result", response);
          fire({
            position: "top-right",
            icon: "success",
            title: "Transaction Rejected",
            text: "The transaction rejected successfully!",
            autoClose: 2000,
          });
          getList(currentPage, itemsPerPage, searchValue, status, selectedDate);
          setIsHeaderReceived(true);
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error(err);
          //setIsLoading(false);
        } finally {
          setIsLoading(false);
        }
      },
      onCancel: () => {
        console.log("Cancelled");
      },
    });
  };

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue, status, selectedDate);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, status, selectedDate);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;

    if (item === "status") {
      setStatus(event.target.value);
      getList(1, itemsPerPage, searchValue, value, selectedDate);
      setStatus(value);
    } else if (item === "date") {
      setSelectedDate(value);
      getList(1, itemsPerPage, searchValue, status, value);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, status, selectedDate),
    300
  );

  const handleReset = () => {
    setSearchValue("");
    setStatus("");
    setSelectedDate("");
    getList(1, itemsPerPage, "", "", "");
  };

  return (
    <>
      <div
        className={`home-container overall-list-padding ${
          visible ? "visible" : ""
        }`}
      >
        <OverallOverview
          handleHeaderReceived={handleHeaderReceived}
          isHeaderReceived={isHeaderReceived}
        />

        <div className={`border-radius ${visible ? "visible" : ""}`}>
          <div className="bank-transaction-box ">
            {/* <h3 className='bank-transaction-box-head'>Bank Transaction</h3> */}
            <div className="table-header">
              <h5>Bank Transaction</h5>
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <input
                    className="date-picker2"
                    type="date"
                    max={maxDate}
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                  />
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[
                    `Search "name"`,
                    `Search "transaction id"`,
                    `Search "reference number"`,
                  ]}
                />
                
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 7 ? itemsPerPage : 7}
                no_of_cols={5}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="bank-transaction-container ">
                {bankTransactionList && bankTransactionList.length > 0 ? (
                  bankTransactionList.map((data, index) => (
                    <div className="bankTransaction" key={index}>
                      <div className="profile-name-date-time">
                        <div className="profile-pic">
                          {/* <Image
                            src={mediaUrl + data.customer_profile_photo}
                            alt="Profile Picture"
                            fill
                            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                            className="profileImage"
                            style={{ objectFit: "cover" }}
                          /> */}

                            <Image
                              src={data.customer_profile_photo !== null ? mediaUrl + data.customer_profile_photo : dummyprofile}
                              alt="Profile Picture"
                              fill
                              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              className="profileImage"
                              style={{ objectFit: "cover" }}
                            />
                        </div>

                        <div className="name-date-time">
                          <div className="name">{data.customer_name}</div>
                          <DateTimeDisplay
                            created_at={data.created_at}
                            pageName="home"
                          />
                        </div>
                      </div>

                      <div className="amount-transactionId">
                        <h3 className="amount">{data.emi_amount}</h3>
                        <p className="transactionId">
                          Transaction ID : {data.payment_reference_no ? data.payment_reference_no : 'N/A'}
                        </p>
                        <p className="transactionId">
                          Reference ID : {data.transaction_id ? data.transaction_id : 'N/A'}
                        </p>
                      </div>

                      {data.approved === "pending" || "null" ? (
                        <div className="buttons">
                          <div className="rejectBtn">
                            <button
                              className="rejectButton"
                              onClick={() => {
                                handleShowRejectAlert(data.id);
                              }}
                            >
                              Reject
                            </button>
                          </div>
                          <div
                            className="confirmBtn"
                            onClick={() => {
                              handleShowAcceptAlert(data.id);
                            }}
                          >
                            <button className="acceptButton">Confirm</button>
                          </div>
                        </div>
                      ) : (
                        //   <div className="inProcess">
                        //   <p>In Process</p>
                        // </div>
                        <div className="rejected">
                          <p>Rejected</p>
                        </div>
                      )}

                      <div className="remark">
                        <h5>Remark</h5>
                        <p>{data.remark ? data.remark : 'N/A'}</p>
                      </div>

                      <div
                        className="viewAttachment"
                        onClick={() => handleShowScreenshot(data.screenshot)}
                      >
                        <ViewFileIcon className="view-file-icon" />
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-data-bankTransactionList">
                    <h5>
                      {selectedDate &&
                        !searchValue &&
                        `There are no transactions with the selected date "${selectedDate}".`}
                      {searchValue &&
                        !selectedDate &&
                        `No transactions match your search for "${searchValue}".`}
                      {!searchValue &&
                        !selectedDate &&
                        "It looks like you don't have any transactions yet."}
                      {searchValue &&
                        selectedDate &&
                        `No transactions with the date "${selectedDate}" match your search for "${searchValue}".`}
                    </h5>
                    {(selectedDate || searchValue) && (
                      <button
                        onClick={handleReset}
                        style={{ marginLeft: "auto", marginRight: "auto" }}
                        className="submitButton"
                      >
                        <span className="material-icons">restart_alt</span>Reset
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {bankTransactionList && bankTransactionList?.length > 0 && (
            <>
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />

              <div className="border-bottom-container">
                <div className="border-bottom"></div>
              </div>
            </>
          )}
        </div>
      </div>
      <ViewScreenshot
        screenshotUrl={screenshotUrl}
        showScreenshot={showScreenshot}
        handleCloseScreenshot={handleCloseScreenshot}
      />
    </>
  );
}

export default Page;
