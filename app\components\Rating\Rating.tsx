import React from "react";
import Rating from '@mui/material/Rating';
import Stack from '@mui/material/Stack';

interface RatingProps {
  rating: number | undefined;
  isReadOnly: boolean;
  onChange?: (event: React.SyntheticEvent, newValue: number | null) => void;
  size?: "small" | "medium" | "large";
}



const RatingComponent = ({ rating=2.5, isReadOnly, onChange, size = "medium" }: RatingProps) => {

  return (
    <Stack spacing={1}>
      <Rating
        name="simple-controlled"
        value={rating}
        onChange={isReadOnly ? undefined : onChange} 
        precision={0.5} 
        size={size}
        readOnly={isReadOnly} 
      />
    </Stack>
  );
};

export default RatingComponent;
