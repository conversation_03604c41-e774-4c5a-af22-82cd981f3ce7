"use client";

import React, { createContext, useState, useContext, useRef, useCallback } from "react";
import ReactDOM from "react-dom";
import SuccessIcon from "../../../assets/svg/successIcon.svg"; 
import ErrorIcon from "../../../assets/svg/ErrorIcon.svg"; 
import '../../../styles/variables.scss'
import './alert.scss'

interface AlertContextType {
  fire: (options: AlertOptions) => void;
}
interface AlertOptions {
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "center";
  icon?: "success" | "error" | "info";
  title?: string;
  text?: string;
  autoClose?: number; // Time in milliseconds
  initialValue?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  onConfirm?: (inputValue: string) => void;
  onCancel?: () => void;
}

const AlertContext = createContext<AlertContextType | null>(null);

export const AlertProvider = ({ children }: { children: React.ReactNode }) => {
  const [alert, setAlert] = useState<AlertOptions | null>(null);
  const [inputValue,setInputValue] = useState<string>('')
  const timerRef = useRef<number | null>(null);
  // const fire = ({
  //   position = "center",
  //   icon = "info",
  //   title = "",
  //   text = "",
  //   autoClose = 0,
  //   initialValue = "",
  //   confirmButtonText = "",
  //   cancelButtonText = "",
  //   onConfirm,
  //   onCancel,
  // }: AlertOptions) => {
  //   setAlert({
  //     position,
  //     icon,
  //     title,
  //     text,
  //     confirmButtonText,
  //     cancelButtonText,
  //     initialValue,
  //     autoClose,
  //     onConfirm,
  //     onCancel,
  //   });
  //   setInputValue(initialValue);
  //   if (autoClose > 0) {
  //     timerRef.current = window.setTimeout(() => close(), autoClose);
  //   }
  // };
  // const close = () => {
  //   setAlert(null);
  //   setInputValue("");
  //   if (timerRef.current) {
  //     clearTimeout(timerRef.current);
  //     timerRef.current = null;
  //   }
  // };


  const close = useCallback(() => {
    setAlert(null);
    setInputValue("");
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Wrap `fire` in useCallback and include `close` in its dependencies.
  const fire = useCallback(({
    position = "center",
    icon = "info",
    title = "",
    text = "",
    autoClose = 0,
    initialValue = "",
    confirmButtonText = "",
    cancelButtonText = "",
    onConfirm,
    onCancel,
  }: AlertOptions) => {
    setAlert({
      position,
      icon,
      title,
      text,
      confirmButtonText,
      cancelButtonText,
      initialValue,
      autoClose,
      onConfirm,
      onCancel,
    });
    setInputValue(initialValue);
    if (autoClose > 0) {
      timerRef.current = window.setTimeout(() => close(), autoClose);
    }
  }, [close]);
  const getPositionClass = (position: string | undefined) => {
    switch (position) {
      case "top-right":
        return "items-start justify-end";
      case "top-left":
        return "items-start justify-start";
      case "bottom-right":
        return "items-end justify-end";
      case "bottom-left":
        return "items-end justify-start";
      case "center":
      default:
        return " items-center justify-center";
    }
  };
  

  return (
    <AlertContext.Provider value={{ fire }}>
      {children}
      {alert &&
        ReactDOM.createPortal(
          <div
            style={{zIndex:2000}}
            className={`fixed inset-0 bg-black bg-opacity-50 flex ${getPositionClass(alert.position)}`}
            onClick={close}
          >
            <div
               className={`alert-box bg-white rounded-lg shadow-lg w-96 p-6 flex flex-col items-center ${getPositionClass(
                alert.position
              )}`}
              onClick={(e) => e.stopPropagation()}
              style={{
                animation: alert.autoClose
                  ? `countdown ${alert.autoClose}ms linear forwards`
                  : undefined,
              }}
            >
              {/* Icon Section */}
              <div className="rounded-full flex items-center justify-center mb-4 alert-icons">
                {alert.icon === "success" && (
                  <SuccessIcon style={{width:'70px'}} className="" />
                )}
                {alert.icon === "error" && 
                  <ErrorIcon style={{width:'70px'}} />
                }
                 {alert.icon === "info" && <div style={{fontSize:'80px'}} className="material-icons">info</div>}
              </div>

              {/* Title and Text */}
              <h2 className="text-lg font-semibold text-center mb-2">{alert.title}</h2>
              <p className="text-sm text-gray-600 text-center mb-6">{alert.text}</p>
              {alert?.initialValue && 
                  <textarea
                  onChange={(e)=>setInputValue(e.target.value)}
                  value={inputValue}
                  className="text-area"
                  />
              }
              {/* Buttons Section */}
              <div className="flex space-x-4">
              {alert.confirmButtonText && (
                  <button
                    onClick={() => {
                      alert.onConfirm?.(inputValue);
                      close();
                    }}
                    className="primary-background px-8 py-1 rounded-lg"
                  >
                    {alert.confirmButtonText}
                  </button>
                )}
                {alert.cancelButtonText && (
                  <button
                    onClick={() => {
                      alert.onCancel?.();
                      close();
                    }}
                    className="px-8 py-1 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100"
                    >
                    {alert.cancelButtonText}
                  </button>
                )}

              </div>
            </div>
          </div>,
          document.body
        )}
    </AlertContext.Provider>
  );
};

export const useAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
};
