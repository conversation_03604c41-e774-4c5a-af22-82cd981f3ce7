
@use "sass:color";
@use '/app/styles/variables.scss' as *; 

.customer-details-container
{
    position: fixed;
     top: 0;
     bottom: 0;
     left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
    z-index: 5000;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease, z-index 0s linear 0.3s;
    overflow-y: auto;
    //padding-bottom: 15px;
    //border-radius: 8px;
        &.show{
            opacity: 1;
            z-index: 1000;
        }
 

    .customerDetailsHeader{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px 25px;

        @media(max-width: $breakpoint-lg) {
            flex-wrap: wrap;
        

        }
       
        p{
            display: flex;
            flex-direction: row;
            gap: 10px;
            color: $white_color;
            font-size: 12px;

            .active{
                cursor: pointer;
                color: $primary_color;
            }

            @media(max-width: $breakpoint-sm){
                font-size: 9px;


            }
        }
    }

    .user-details-transaction-details-container{
      width: 100%; 
      padding: 0 25px 0 25px; 
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    
      
      @media(max-width: $breakpoint-lg) {
        flex-direction: column;
        gap: 30px;
        padding: 0 25px 25px 25px; 

      }


    

      .user-details{
        width: 22%;
        background-color: $white_color;
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        height: calc(100dvh - 62px);
        //padding-bottom: 20px;

        @media (max-width: $breakpoint-lg){
            width: 100%;
            height: auto;

        }

        .user-profile{
            width: 100%;
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid $black_color4;
            padding: 15px 0 16px 0;
            .profile-picture-container{
                position: relative;
                width: 90px;
                height: 90px;
            }
            .profilePicture{
                width: 90px;
                height: 90px;
                background-color: $black_color3;
                border-radius: 50%;
                overflow: hidden;
                position: relative;

                .profileImage{
                    object-fit: cover;
                    width: 100%;
                    height: 100%;
                }
            }

            .username{
                font-weight: 600;
                margin: 8px 0 5px 0;
                font-size: 16px;
                text-align: center;
                width: 100%;
                padding: 0 15px;
                position: relative;

                .editBtn{
                    cursor: pointer;
                    position: absolute;
                    top: 1px;
                    right: 15px;
                    font-size: 15px;
                    color: $black_color3;

                    &:hover{
                       
                            //color: darken($black_color3, 20%);
                            color: color.adjust($black_color3, $lightness: -20%);

                    }
                }
            }

            .address{
                padding: 0 40px;
                text-align: center;
                font-size: 10px;
                color: $black_color2;
            }

            .rating{
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                gap: 3px;
                padding: 6px 0 5px 0;
                margin-bottom: 13px;

                .star{
                    color: $rating_color;
                    font-size: 18px;
                }
                .star-unfilled{
                    color: $black_color2;
                    font-size: 18px;
                }
            }

            .wallet{
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                gap: 25px;
         
             
        

                p{
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;



                    .walletIcon{
                       color: $primary_color;
                       margin-right: 7px;
                       font-size: 15px;

                    }
                    .txt{
                        font-size: 12px;
                        font-weight: 600;
                    }

            
               

                }

                .rupees{
                    font-size: 14px;
                    font-weight: 800;
                    color: $green_color2;
                }


                
                
            }

          

            

        }

        .user-purchase{
            margin: 12px;
            padding: 9px;
            border-radius: 8px;
            background-color: $primary_color;

            h3{
                font-size: 14px;
                color: $white_color;
                font-weight: 500;
            }

            .totalPayment{
                padding-top: 11px;
               
                .amount{
           
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;

                    .totalpaid{
                        font-size: 11px;
                        font-weight: 700;
                        color: $white_color;
                   
                    }

                    .total{
                        font-size: 11px;
                        font-weight: 500;
                        color: $white_color;

                    }
                }

                .paymentBar{
                    width: 100%;
                    height: 7px;
                    background-color: $white_color;
                    border-radius: 6px;
                    margin-top: 3px;
                }

             
            }
        }

       .user-additional-details-border{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        

        .border{
            width: 300px;
            border-bottom: 1px solid $black_color4;
           
    

        }
       }


        .user-additional-details{
            display: flex;
            flex-direction: column;
            padding: 13px 25px 0px 25px; 
            height: calc(100dvh - 420px);
            overflow-x: auto;

            &::-webkit-scrollbar {
                width: 8px; // Width of the scrollbar
                height: 8px; // Height for horizontal scrollbar (if any)
              }
            
              &::-webkit-scrollbar-track {
                background: #f0f0f0; // Background color of the scrollbar track
                border-radius: 10px; // Rounded corners for the track
              }
            
              &::-webkit-scrollbar-thumb {
                background: linear-gradient(45deg, $black_color2, $black_color3); // Gradient thumb
                border-radius: 10px; // Rounded corners for the thumb
                border: 2px solid #f0f0f0; // Adds padding between track and thumb
              }
            
              &::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(45deg, $black_color, $black_color2); // Thumb color on hover
              }
           

            .detail{
                display: flex;
                flex-direction: row;
                align-items: center;
                gap: 15px;
                margin-bottom: 12px;

                .icon{
                    width: 35px;
                    height: 35px;
                    background-color: $white_color1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: $primary_color;
                    border-radius: 50%;

                    span{
                        font-size: 18px;
                    }

                }



                .content{
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .label{
                   display: block;
                   font-size: 10px;
                   color: $black_color2;
                    }

                    .permanentAddress{
                        padding: 0 15px 0 0;
                    }

                    .value{
                    font-size: 11px;
                    font-weight: 500;
                
                    }

                    .more{
                        font-size: 13px;
                        color: $link_color;
                        text-decoration: underline $link_color;
                        text-underline-offset: 2px;
                        cursor: pointer;

                        &:hover{
                            color: darken($link_color, 20%);
                        }
                    }

                    .less{
                        font-size: 13px;
                        color: $red_color;
                        text-decoration: underline $red_color;
                        text-underline-offset: 2px;
                        cursor: pointer;

                        &:hover{
                            color: darken($red_color, 20%);
                        }
                    }

                    .hideMore{
                        display: none;
                    }

                    
               
                }


              
            }

            .detail4{
                .icon{
                    width: 35px;
                    height: 35px;
                }
            }

            // .detail1{
            //     padding-top: 15px;
            // }

            .detail5{
                margin-bottom: 28px;
            }


            .detail3{
                .textfield-buttons{
                    display: flex;
                    flex-direction: column;

                    .text-field{
                        margin:6px 0 8px 0;
                        input{
                            border: 1px solid #C6C6C6;
                            border-radius: 4px;
                            padding: 4px 0 4px 10px;


                            &::placeholder{
                                color: #D4D4D4;
                                font-size: 12px;
                            }
                        
                        }
                    }

                    .buttons{
                        display: flex;
                        flex-direction: row;
                        gap: 15px;
                        button{
                            border: 0;
                            color: #5A6CF8;
                            text-decoration: underline #5A6CF8;
                            font-size: 13px;
                            background: none;
                            transition: text-decoration .2s ease, color .2s ease;

                            &:hover{
                                color: darken(#5A6CF8, 20%);
                                text-decoration: underline darken(#5A6CF8, 20%);

                            }
                        }
                    }
                }
                
            }

            .detail4{
                .textfield-buttons{
                    display: flex;
                    flex-direction: column;

                    .text-field{
                        margin:6px 0 8px 0;
                        input{
                            border: 1px solid #C6C6C6;
                            border-radius: 4px;
                            padding: 4px 0 4px 10px;


                            &::placeholder{
                                color: #D4D4D4;
                                font-size: 12px;
                            }
                        
                        }
                    }

                    .buttons{
                        display: flex;
                        flex-direction: row;
                        gap: 15px;
                        button{
                            border: 0;
                            color: #5A6CF8;
                            text-decoration: underline #5A6CF8;
                            font-size: 13px;
                            background: none;
                            transition: text-decoration .2s ease, color .2s ease;

                            &:hover{
                                color: darken(#5A6CF8, 20%);
                                text-decoration: underline darken(#5A6CF8, 20%);

                            }
                        }
                    }
                }
                
            }

            .attachments-container{
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 7px;
               
                h5{
                    color: $black_color;
                    font-weight: 400;
                    font-size: 12px;
                    margin-bottom: 3px;
                }
    
                .attachment{
                    display: flex;
                    flex-direction: row;
                    padding: 10px;
                    border: 1px solid $black_color4;
                    border-radius: 4px;
                    justify-content: space-between;
                    align-items: center;
                    
    
                    .image-title-date-time{
                        display: flex;
                        flex-direction: row;
    
                        .image{
                            position: relative;
                            height: 26px;
                            width: 30px;
                            margin-right: 2px;
    
                            img{
                                object-fit: contain;
                            }
                        }
    
    
                        .title-date-time{
                            display: flex;
                            flex-direction: column;
    
                            .title{
                                font-size: 11px;
                                color: $black_color;
                                font-weight: 600;
                                margin-bottom: 1px;
                            }
    
                            .date-time{
                                font-size: 10px;
                                color: $black_color2;
                                height: fit-content;
                            }
                        }
    
                    }
    
                    .viewBtn{
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        gap: 5px;
                        .view-bttn{
                            font-size: 10px;
                            font-weight: 600;
                            padding: 2px 4px;
                            border-radius: 3px;
                            border: 1px solid $white_color1;
                            background-color: $white_color1;
                            cursor: pointer;
                            transition: background-color 0.2s ease;
    
                            &:hover{
                                background-color: darken($white_color1, 5%);
                            }
                        }
                    }
    
                    
                }
    
                .addDocumentBtn{
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 13px 0 5px 0;

                    &.hide{
                        display: none;
                    }
                    
    
                    button{
                        padding: 0px 8px;
                        font-size: 10px;
                        display: flex;
                        align-items: center;
                        border: 1px solid $black_color3;
                        color: $black_color2;
                        border-radius: 4px;
                        font-weight: 700;
                        background-color: $white_color;
                        transition: background-color 0.2s ease;
    
                        &:hover{
                            background-color: darken($white_color, 5%);
                        }
    
                        span{
                            font-size: 23px;
                            color: $black_color2;
                            font-weight: 300;
                            margin-right: 5px;
                        }
                    }


                }

                .drag-drop-documents-container{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;

                    .drag-drop-documents{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        align-items: center;
                        padding: 20px 0 15px 0;
                        

                        .image-label{
                            display: flex;
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;
                            width: 190px;
                            padding: 5px 0;
                            border-radius: 5px;
                            border: 2px dotted $black_color4;
                            gap: 3px;

                            .image{
                                position: relative;
                                height: 26px;
                                width: 30px;
                                margin-right: 2px;
        
                                img{
                                    object-fit: contain;
                                }
                            }
    
                            .label{
                                font-size: 14px;
                            }
                        }
    
                        

                        .buttons{
                            padding: 15px 0 0 0;
                            display: flex;
                            flex-direction: row;
                            gap: 20px;
                          
    
                            .acceptButton{
                                background-color: $primary_color;
                                color: $white_color1;
                                font-size: 12px;
                                font-weight: 600;
                                width: 75px;
                                height: 25px;
                                border-radius: 3px;
                                border: 1px solid $primary_color;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                            
                                &:hover{
                                    background-color: darken($primary_color, 10%);     
                                }
                            
                            
                              
                            }
                            .rejectButton{
                                color: #71757D;
                                font-size: 12px;
                                font-weight: 600;
                                width: 75px;
                                height: 25px;
                                border: 1px solid #9D9D9D;
                                border-radius: 3px;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                            
                                &:hover{
                                    background-color: darken($white_color, 5%);     
                                }
                            
                            
                              
                            }
                        }
    
                    }

                    
                }
            }

            .userDetailsEndBorder{
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                
        
                .border{
                    width: 240px;
                    border-bottom: 1px solid $black_color4;
                   
            
        
                }
               }


            

        }

       

       

   
      }

      .transaction-details{
            
            width: 78%;
            display: flex;
            flex-direction: column;
            padding: 0 0 0 18px;
            //display: none;

            @media(max-width: $breakpoint-lg) {
                padding: 0;
                width: 100%;
            }

            .user-overview-container{
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-bottom: 15px;
                gap: 20px;

                //for horizontal scroll
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 10px;

                &::-webkit-scrollbar {
                    width: 8px; // Width of the scrollbar
                    height: 5px; // Height for horizontal scrollbar (if any)
                  }
                
                  &::-webkit-scrollbar-track {
                    background: #f0f0f0; // Background color of the scrollbar track
                    border-radius: 10px; // Rounded corners for the track
                  }
                
                  &::-webkit-scrollbar-thumb {
                    background: linear-gradient(45deg, $black_color2, $black_color3); // Gradient thumb
                    border-radius: 10px; // Rounded corners for the thumb
                    border: 1px solid #f0f0f0; // Adds padding between track and thumb
                  }
                
                  &::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(45deg, $black_color, $black_color2); // Thumb color on hover
                  }

                .userOverview{
                    width: calc(100% - 80px);
                    height: 110px;
                    background-color: $white_color;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    border-radius: 7px;

                    .no-emi-due-date-available{
                        margin: 0;
                        padding: 0;
                       

                        h3{
                        margin: 0;
                        padding: 0;

                        }
                    }

                    //for horizontal scroll
                    min-width: 250px;

                    @media (max-width: $breakpoint-md) {
                        min-width: 350px;
                    }

                    @media (max-width: $breakpoint-sm) {
                        min-width: 320px;
                    }
                    
                    
                }

               

                @media (max-width: $breakpoint-lg) {
                    gap: 15px;
                }
            
                @media (max-width: $breakpoint-md) {
                    gap: 10px;
                    
                }
            
                @media (max-width: $breakpoint-sm) {
                    gap: 8px;
                    overflow-x: auto; 
                    flex-wrap: nowrap; 
                }
            
                @media (max-width: $breakpoint-xs) {
                    gap: 5px;
                    overflow-x: auto; 
                    flex-wrap: nowrap;
                }

                .userOverview1{
                    padding: 22px 0 20px 0;

                    h3,h1{
                        padding: 0 15px;
                    }

                    h3{
                      font-size: 16px;
                     
                      //margin-bottom: 12px;
                    
                    }

                    h1{
                        color: $red_color;
                        font-weight: 800;
                        font-size: 28px;
                    }
                }

                .userOverview2, .userOverview3, .userOverview4{
                    //gap: 5px;
                    padding: 12px 0 17px 0;

                    
                  

                    .icon{
                        padding: 0 15px;
    
                    }
                    p,h3{

                        padding: 0 15px;

                    }

                    h3{
                        font-size: 16px;
                        font-weight: 600;
                        
                    }

                    p{
                        color: $black_color2;
                        font-size: 11px;
                        //margin-bottom: 5px;
                    }
                }

              

                .userOverview2{
                    .icon {
                        margin-left: 13px;
                        //margin-bottom: 8px;
                        width: 35px;
                        height: 35px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #FFF2DD;
                        border-radius: 50%;
                    
                        span {
                            padding: 3px;
                            background-color: #FFE5BF; 
                            color: #FF8000; 
                            border-radius: 4px;
                            border-radius: 50%;
                            font-size: 20px;
                        }
                    }
                    
                    
                }

                .userOverview3{
                    .icon {
                        margin-left: 13px;
                        //margin-bottom: 8px;
                        width: 35px;
                        height: 35px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #E8F7E6;
                        border-radius: 50%;
                    
                        span {
                            padding: 3px;
                            background-color: #D4F1D1; 
                            color: $green_color2; 
                            border-radius: 4px;
                            border-radius: 50%;
                            font-size: 22px;
                        }
                    }
                    
                    
                }

                .userOverview4{
                    .icon {
                        margin-left: 13px;
                        //margin-bottom: 8px;
                        width: 35px;
                        height: 35px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #E7F5FF;
                        border-radius: 50%;
                    
                        span {
                            padding: 3px;
                            background-color: #D0EBFF; 
                            color: #38A1F3; 
                            border-radius: 4px;
                            border-radius: 50%;
                            font-size: 22px;
                        }
                    }

                    h3{
                        color: $green_color2;
                    }
                    
                    
                }
            }

            .purchase-order-container{
                height: calc(100dvh - 85px);
                background-color: $white_color;
                border-radius: 6px;

                @media(max-width : $breakpoint-lg){
                    height: auto;
                }
             
                

                .table{
                    max-height: calc(100dvh - 348px);
                    table{
                        tbody{
                            tr{
                                .item{
                                    display: flex;
                                    flex-direction: row;
                                    align-items: center;
                                    gap: 7px;
                                    width: 150px;
                
                                    input[type="checkbox"] {
                                        transform: scale(1.1);
                                      }
                                      
                
                                    .itemPicture{
                                        height: 28px;
                                        width: 28px;
                                        border-radius: 6px;
                                        background-color: $black_color3;
                                    }
                
                                    .itemDetails{
                                        display: flex;
                                        flex-direction: column;
                
                                        h5{
                                            font-size: 12px;
                                            font-weight: 500;
                                            margin-bottom: 3px;
                
                                        }
                
                                        p{
                                            font-size: 10px;
                                            color: $black_color2;
                                        }
                                    }
                                }
                
                                .totalAmount{
                                    color: $red_color;
                                    font-weight: 600;
                                    font-size: 12px;
                                }
                
                                .emiAmount{
                                    color: $green_color2;
                                    font-weight: 600;
                                    font-size: 12px;
                                }
                
                                .category{
                                  color: $black_color3;
                                  font-weight: 600;
                                  font-size: 12px;
                              }
                
                                
                
                                .purchaseAmount, .discount{
                                    color: $black_color3;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                          
                                .actions {
                                  display: flex;
                                  gap: 5px;
                          
                                  .material-icons {
                                    font-size: 15px;
                                    color: $black_color5;
                                    cursor: pointer;
                                    transition: color 0.2s ease;
                          
                                    &:hover {
                                      color: darken($black_color5, 20%);
                                    }
                                  }
                                }
                            }
                        }
                    }
                }
            }



            .emi-note-container{
                height: calc(100dvh - 85px);
                background-color: $white_color;
                //border-radius: 6px;
                overflow: hidden;

                @media( max-width: $breakpoint-lg){
                    height: auto;
                }


                .table2{
                    max-height: calc(100dvh - 366px);
                    table{
                        thead{
                            tr{
                                th{
                                    font-size: 13px;
                                    color: $black_color;
                                    font-weight: 500;
                                }

                                .attachments{
                                    display: flex ;
                                    justify-content: center;
                                }
                            }

                        }
                        tbody{

                            tr{

                                
                                
                                .checkbox-name{
                                    display: flex;
                                    flex-direction: row;
                                    align-items: center;
                                    gap: 5px;
                                    white-space:  nowrap;

                                    input[type="checkbox"] {
                                        transform: scale(1.1);
                                      }

                                      .name{
                                        color: $black_color;
                                        font-size: 12px;
                                        font-weight: 600;
                                      }

                                    
                                }

                                .date-time{
                                    display: flex;
                                    flex-direction: row;
                                    white-space: nowrap;
                        
                                      .date, .time{
                                        font-size: 12px;
                                        color: $black_color5;
                                        font-weight: 500;
                                        white-space: nowrap;
                                        
                                      }
                      
                                      .date{
                                        height: fit-content;
                                        border-right:  2px solid $black_color3;
                                        padding-right: 5px;
                                      }
                      
                                      .time{
                                        padding-left: 5px;
                                      }
                      
                                    
                              
                   
                                }

                                .date{
                                    font-size: 13px;
                                    color: var(--Neutral-Gray-3, $black_color5);
                                    font-weight: 600;
                                }

                                .remark{
                                    font-size: 12px;
                                    color: $black_color5;
                                    font-weight: 400;
                                    white-space: wrap;
                                }

                                .attachments{
                                    display: flex;
                                    justify-content: center;
                            
                                    .fa-file{
                                    color: $primary_color;
                                    font-size: 22px;
                                    cursor: pointer;
                                    transition: color 0.2s ease;

                                    &:hover{
                                        color: darken($primary_color, 10%);
                                    }

                                    }

                                    .noAttachments{
                                        font-size: 13px;
                                        color: $black_color5;
                                    }
                                    
                                }
                                

                                .actionIcons{
                                    .material-icons{
                                        font-size: 15px;
                                        color: $black_color5;
                                        margin-right: 5px;
                                    }

                                    .eyeIcon, .editIcon, .deleteIcon{
                                        transition: color .2s ease;
                                        cursor: pointer;

                                        &:hover{
                                            color: darken($black_color5, 20%);
                                        }

                                    }

                                }
                            }
                            }
                     } }
            }


          

            

           
            .table{
                table{
                    tbody{
                        .curser-pointer{
                            cursor: pointer;
                        }
                    }
                }

            }

            .transaction-additional-details{
                width: 100%;
                background-color: #EFF2FB;
                border-radius: 0 0 5px 5px;


                .remark{
                  
                    p{
                        font-size: 11px;
                        font-weight: 700;
                        padding-bottom: 5px;
                    }

                    .remark-text-field {
                        input {
                            width: 100%;
                            height: 105px;
                            border: 1px solid $black_color4;
                            border-radius: 8px;
                            padding: 0 0 50px 10px;

                            &::placeholder{
                                color: $black_color3;
                                font-size: 11px;
                            }
                        }

                      
                    }
                    
                    

                }
            }


        
              
            .showData {
                opacity: 1;
                visibility: visible;
                // height: auto; /* Automatically adjusts height */
                max-height: 500px; /* Adjust this value to suit your content size */
                // overflow: hidden;
                // padding: 10px; /* Optional padding for smoothness */
                transition: opacity 0.3s ease-in-out, max-height 0.3s ease-in-out;
              }
              
              .hideData {
                opacity: 0;
                visibility: hidden;
                max-height: 0; /* Collapse height */
                overflow: hidden;
                pointer-events: none;
            
                transition:  max-height .3s ease-in-out , visibility 0.3s ease-in-out , opacity 0.3s ease-in-out;
              }
              
              
              
              
              
              
              

           

           
            
      }



    }
}

.address-item{
    .label{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}