@import '/app/styles/variables.scss';

.emiCollectionOverviewList{
    //margin-top: 20px;
    
    .user-list-table-container{
        
        .tableBorder{
            .table-header{
                justify-content: end;

                @media (max-width: 640px) {
                    justify-content: start;
                  }
            }

            table{
                tbody{
                    tr{
                        background-color: $white_color;

                        .date-time-invoiceNumber{
                            display: flex;
                            flex-direction: column;
                            white-space: nowrap;
                            
              
                            .dateAndTime{
                              display: flex;
                              flex-direction: row;
                              margin-bottom: 3px;
                              white-space: nowrap;
                              
                        
                              .date, .time{
                                font-size: 13px;
                                color: $black_color;
                                font-weight: 600;
                                white-space: nowrap;
                                
                              }
              
                              .date{
                                border-right:  2px solid $black_color;
                                padding-right: 5px;
                              }
              
                              .time{
                                padding-left: 5px;
                              }
              
                            
                             }
              
                             .invoiceNumber{
                              font-size: 10px;
                              color: $black_color3;
                              white-space: nowrap;
                            }
                           }
                        
                        .gpay{
                            font-size: 12px;
                            font-weight: 600;
                            color: $black_color2;
                        }

                        .cash{
                            font-size: 12px;
                            font-weight: 600;
                            color: $green_color2;
                        }

                        .customerName{
                            .name{
                                font-size: 11px;
                                font-weight: 600;
                                color: $black_color;
                            }

                            .careOf{
                                font-size: 12px;
                                color: $black_color;
                                display: flex;
                                flex-direction: row;
                            }
                        }

                        .transactionMode{
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            gap: 10px;

                            .mode{
                                font-size: 12px;
                                color: $black_color2;
                                font-weight: 600;

                                
                            }


                            .fa-paperclip{
                                color: #1E90FF;
                                font-size: 12px;
                                cursor: pointer;

                            }

                            
                        }

                        .invoiceNumber{
                            font-size: 10px;
                            color: $black_color3;
                            white-space: nowrap;
                          }

                        .agent{
                            font-size: 12px;
                            color: $black_color;
                            font-weight: 600;
                        }

                        .branchName{
                            font-size: 12px;
                            color: $black_color;
                            font-weight: 600;
                        }

                        .remark{
                            font-size: 12px;
                            color: $black_color2;
                            padding-right: 10px;
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }
}