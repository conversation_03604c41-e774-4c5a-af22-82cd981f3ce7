import axiosInstance from "@/app/api/axiosInstance";
import { create_route, customerList, route_details, routeEdit, routeList } from "./route.model";

export const getRouteList = async (
skip: number,
itemsPerPage: number,
search: string,
day?: string
) : Promise<routeList> =>{
    try{
        const queryParams = new URLSearchParams({
            skip : skip.toString(),
            limit : itemsPerPage.toString(),
            search : search,
        })

        if (day) {
            queryParams.append('day', encodeURIComponent(day));
        }

        const response = await axiosInstance.get<routeList>(`dashboard/route_list/?${queryParams.toString()}`);
        return response.data;
    }catch(error){
        throw new Error("Failed to fetch route list");
        console.error(error);
        
    }
}

export const getRouteDetails = async (id:number | null) :Promise<route_details> => {
    try{
        const response = await axiosInstance.get(`dashboard/route_detail/${id}`)
        return response.data
    }catch(error){
        throw error;
    }
}

//create route
export const createRoute = async (body: create_route) => {
    try{
        console.log("api called");
        
        const response = await axiosInstance.post("create_route", body);
        return response.data;
        
    }catch(error){
        console.error("Error creating new route", error);
        throw new Error("Failed to create new route") 
    }
}

//fetch route by id
export const fetchRouteById = async (route_id:number) => {
    try{
        const response = await axiosInstance.get(`//${route_id}`);
        return response.data;

    }catch(error){
        console.error("Error fetching route by id", error);
        throw new Error("Failed to route by id");
    }
}

//edit route by id
export const editRoute = async ( route_id:number | null, body:routeEdit ) => {
    try{
       const response = await axiosInstance.put(`update_route/${route_id}`, body);
       return response;
    }catch(error){
        throw error
        
    }
}

//fetch branchlist
export const fetchBranchList = async () => {
    try{

        const response = await axiosInstance.get("/branches/?pagination=false");
        return response.data;

    }catch(error){

        console.error("Error fetching branch list", error);
        throw new Error("Failed to fetch branch list")

    }
}

//fetch customerlist
export const fetchCustomerList = async () => {
    try{

        const response = await axiosInstance.get<customerList>("dashboard/customers/?pagination=false");
        return response.data;

    }catch(error){

        console.error("Error fetching customer list", error);
        throw new Error("Failed to fetch customer list")

    }
}

//agent customerlist
export const fetchAgentList = async () => {
    try{

        const response = await axiosInstance.get("agents/?pagination=false");
        return response.data;

    }catch(error){

        console.error("Error fetching agent list", error);
        throw new Error("Failed to fetch agent list")

    }
}

//delete route
export const deleteRoute = async ( route_id:number ) => {
    try{
        const response = await axiosInstance.delete(`/delete_route/${route_id}`);
        return response;

    }catch(error){
        console.error("error deleting route", error);
        throw new Error("Failed to delete the route")
        
    }
}

export const getAgentsList = async () => {
    try{
        const response = await axiosInstance.get("agents/?pagination=false");
        return response.data
    }catch(error){
        throw error
    }
}






