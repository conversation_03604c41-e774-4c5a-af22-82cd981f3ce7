'use client'
import React, { useCallback, useEffect, useRef, useState } from "react";
import "./Header.scss";
import { useCommonContext } from "@/app/contexts/commonContext";
import { usePathname, useRouter } from "next/navigation";
import { getUserDetails } from "@/app/api/commonService";
import { userDetails } from "@/app/models/common.models";
import ChangePassword from "@/app/pages/users/components/ChangePassword/ChangePassword";

function Header() {
  const [dropDownVisible, setIsDropDownVisible] = useState<boolean>(false);
  const [showChangePwd, setShowChangePwd] = useState<boolean>(false);
  const userIconRef = useRef<HTMLDivElement>(null); // Reference for the user icon
  const arrowRef = useRef<HTMLSpanElement>(null); // Reference for the arrow-down icon

  const { setIsMenuExpanded } = useCommonContext();
  const { setCurrentPage , setUserData, userData, screenSize, setScreenSize } = useCommonContext();
  const [ visible, setVisible] = useState<boolean>(false);
  const { currentPage } = useCommonContext();
  const currentRoute = usePathname();
  const navigate = useRouter();
  const handleCreateChangePassword = () => {
    setShowChangePwd(true);
  };
  const handleCloseChangePassword = () => {
    setShowChangePwd(false);
  };

  useEffect(()=> {
    const timer = setTimeout(()=> {
      setVisible(true);

    }, 500);

    return () => clearTimeout(timer);

  }, []);

  const handleCurrentPage = useCallback(() => {
    if (currentRoute == "/pages/customer") {
      setCurrentPage("Customers");
    } else if (currentRoute == "/pages/agent") {
      setCurrentPage("Agent");
    } else if (currentRoute == "/pages/route") {
      setCurrentPage("Route");
    } else if (currentRoute == "/pages/home") {
      setCurrentPage("Home");
    } else if (currentRoute == "/pages/emiCollection") {
      setCurrentPage("EMI Collection Overview");
    } else if (currentRoute == "/pages/purchaseOrder") {
      setCurrentPage("Purchase Order Overview");
    } else if (currentRoute == "/pages/branch") {
      setCurrentPage("Branch");
    } else if (currentRoute == "/pages/users") {
      setCurrentPage("Users");
    } else if (currentRoute == "/pages/report") {
      setCurrentPage("Report");
    } else {
      setCurrentPage("");
    }
  }, [currentRoute,setCurrentPage]);
  
  useEffect(() => {
    handleCurrentPage();
  }, [handleCurrentPage]);

  useEffect(() => {
    const updateScreenWidth = () => {
      setScreenSize(window.innerWidth);
    };
    //console.log(window.innerWidth,"width");
    
    // Listen for window resize event
    window.addEventListener("resize", updateScreenWidth);

    // Cleanup listener on unmount
    return () => window.removeEventListener("resize", updateScreenWidth);
  }, [setScreenSize]);

  // Toggle the dropdown
  const handleDropDown = () => {
    setIsDropDownVisible((prev) => !prev);
  };



  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userIconRef.current &&
        !userIconRef.current.contains(event.target as Node) &&
        arrowRef.current &&
        !arrowRef.current.contains(event.target as Node) &&
        !document.querySelector(".dropdown")?.contains(event.target as Node)
      ) {
        setIsDropDownVisible(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const getUserData = useCallback(async () => {
    try {
      const response: userDetails = await getUserDetails();
      setUserData(response);
    } catch (error) {
      console.log("error", error);
    }
  }, [setUserData]);
  
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      navigate.push("/");
    } else {
      getUserData();
    }
  }, [navigate,getUserData]);


  

  const handleLogout = () => {
    localStorage.setItem("token", "");
    navigate.push("/");
  };
  return (
    <div className="navbar">
      <div className="navbar-item1">
        <span className="userType">{currentPage}</span>
      </div>

      <div className="navbar-item2">
        {/* <div className="searchBar">
          <div className="search-box">
            <span className="material-icons search-icon">search</span>
            <input type="text" placeholder="Search" />
          </div>
          <div>
            <div className="notificationIcon">
              <span className="material-icons">notifications_active</span>
              <span className="notificationCount">3</span>
            </div>
          </div>
        </div> */}

        <div className={`userProfile ${visible ? 'visible' : ''}`}>
          {/* User Icon for mobile screens */}

          {
            screenSize < 500 && (<div className="menuIcon" onClick={() => setIsMenuExpanded((prev) => !prev)}>
              <i className="fa-solid fa-bars"></i>
              </div>)
          }

          


          <div
            ref={userIconRef}
            className="userIcon"
            onClick={ handleDropDown} // Only activate on mobile
          >
            <span className="material-icons">person</span>
          </div>

          <div className="userDetails">
            <span className="username">{userData?.name}</span>
            <span className="email">{userData?.email}</span>
          </div>

          {/* Arrow-down icon for showing the logout option on all screen sizes */}
          <div
            className="arrow-down-icon"
            onClick={handleDropDown} // Only activate on larger screens
          >
            <span ref={arrowRef} className="material-icons">
              keyboard_arrow_down
            </span>
          </div>

          

          {/* Dropdown for logout and username/email based on screen size */}
          {dropDownVisible && (
            <div
              className="dropdown"
              style={{
                position: "absolute",
                top: 50, // Use userIcon or arrow position
                right:  10, // Adjust left position for both
              }}
            >
              {/* Logout item visible only on larger screens */}
              {/* {isMobile && ( */}
                <>
                  <div className="dropdown-item" onClick={handleLogout}>
                    <i className="fa-solid fa-right-from-bracket logoutIcon"></i>
                    <p className="logout" >
                      Logout
                    </p>{" "}
                  </div>{" "}
                  <div className="dropdown-item">
                    <i className="fa-solid fa-key logoutIcon"></i>
                    <p className="logout" onClick={handleCreateChangePassword}>
                      Change password
                    </p>{" "}
                  </div>
                </>
              {/* )} */}
            </div>
          )}

          
      
        </div>

      </div>
      <ChangePassword
        showChangePwd={showChangePwd}
        handleCloseChangePassword={handleCloseChangePassword}
        selectedUserId={null}
        handleLogout={handleLogout}
      />
    </div>
  );
}

export default Header;
