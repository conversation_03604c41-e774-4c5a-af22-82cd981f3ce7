import axiosInstance from "@/app/api/axiosInstance";
import { customerPurchaseOrder, order_create, Purchase_Order_Overview_List } from "./purchaseOrder.model";

export const fetchPurchaseOrderOverviewList = async (skip: number,
itemsPerPage: number,
search: string,
date?: string) : Promise<Purchase_Order_Overview_List> => {
    try {
        const queryParams = new URLSearchParams({
          skip : skip.toString(),
          limit : itemsPerPage.toString(),
          search : search,
        });
   
        if (date) {
          queryParams.append('date', encodeURIComponent(date));
        }
   
        const response = await axiosInstance.get<Purchase_Order_Overview_List>(
          `order_overview/?${queryParams.toString()}`
        );
   
        return response.data;
      }catch(error){
        console.error("Error fetching purchase order overview list", error);
        throw new Error("Failed to fetch purchase order overview list")
    }
}

export const fetchCustomerList = async () => {
    try{

        const response = await axiosInstance.get<customerPurchaseOrder>("dashboard/customers/?pagination=false");
        return response.data;

    }catch(error){

        console.error("Error fetching customer list", error);
        throw new Error("Failed to fetch customer list")

    }
}

export const createOrder = async (body: order_create) => {
    try {
        console.log("API called");

        const response = await axiosInstance.post("/order/", body);

        return response.data;
    } catch (error) {
        console.error("Error creating new order", error);
        throw new Error("Failed to create new order");
    }
};



// export const createOrder = async (body:order_create) => {
//     try{
//         console.log("api called");
        
//         const response = await axiosInstance.post("/order/", body);
//         return response.data
        
//     }catch(error){
//         console.error("Error creating new order", error);
//         throw new Error("Failed to create new order") 
//     }
// }

export const postOrderAttachments = async (body:FormData): Promise<{id:number}> => {
    try{
        const response = await axiosInstance.post('formData/create_order_attachment/',body)
        return response.data
    }catch(error){
        throw error;
    }
}