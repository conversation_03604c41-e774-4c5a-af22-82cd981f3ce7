@use '/app/styles/variables' as *;


.route-details-container{
    max-width: 100%;
    background-color: $white_color;
    margin: 0 25px;
    padding: 15px 20px 10px 25px;
    border: 1px solid $black_color4;
    border-radius: 4px;
    

    .routeDetailsHeader{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 20px;
        
        .routeListHead{           
        h5{
            color: $black_color2;
            font-weight: 500;
            margin-bottom: 2px;
            font-size: 13px;
        }

        h3{
            color: $black_color;
            font-size: 16px;
            font-weight: 700;
            
        }
        }

    }

    .routeMapCard-filter-container{
        width: 100%;
        display: flex;
        flex-direction: row;
        
    

        .routeMapCardContainer{
            width: 70%;
            height: auto;
            display: flex;
            flex-direction: row;
            gap: 30px;
            padding-top: 15px;

            .routeMapColumn{
                display: flex;
                flex-direction: column;
                gap: 8px;

                &.two-columns {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr); // Two equal columns
                    gap: 20px; // Adjust spacing
                  } 
                .verticalLine-routeMapCard{
                    display: flex;
                    flex-direction: row;
            
    
                    .circleShape-verticalLine{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: fit-content;
    
                     
    
                        .circleShape{
                            width: 13px;
                            height: 14px;
                            background-color: $white_color;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border: 1px solid $blue_color;
                            border-radius: 50%;
                            margin-bottom: 2px;
    
                            .shape{
                                width: 10px;
                                height: 10px;
                                background-color: $blue_color;
                                color: $white_color;
                                border-radius: 50%;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-size: 8px;
                                font-weight: 600;
    
                            }
                        }
    
                        // .line{
                        //     width: 1px;
                        //     height: 100%;
                        //     border-right: 2px dotted 
                        //     #4169E1;
                        // }
                    }
    
                    .routeMapCard{
                        position: relative;
                        width: 350px;
                        height: 110px;
                        background-color: $white_color1;
                        border-radius: 13px;
                        padding: 5px;
                        .recatangle-indicater-container{
                            position: absolute;
                            right: 3px;
                            top: 37px;
                            z-index: 5;
                        }
                        .delete-button-container-route{
                          position: absolute;
                          right: 6px;
                          top: 6px;  
                          width: fit-content;
                          height: fit-content;
                          .locationIcon{
                            padding: 3px;
                            background-color: darken($white_color1, 5%);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 6px;
                            color: $black_color2;
                            cursor: pointer;
                            transition: background-color 0.2s ease, color 0.2 ease;
                            span{
                                font-size: 16px;
                            }
        
                            &:hover{
                                background-color: darken($white_color1, 10%);
                                color: $black_color;
                            }
        
        
        
                        }
                        }
                        .profilePic-name-rating{
                            display: flex;
                            flex-direction: row;
                            align-items: center;
        
                        .profile-pic{
                            position: relative;
                            width: 35px;
                            height: 35px;
                            background-color: $black_color2;
                            border-radius: 50%;
                            margin-right: 8px;
                            overflow: hidden;
                        
                            img{
                                object-fit: contain;
                            }
            
                        }
        
                        .name-rating{
                            .name{
                                color: $black_color;
                                font-size: 13px;
                                font-weight: 700;
                                margin-bottom: 2px;
                            }
                
                            .rating{
                        
                                display: flex;
                                flex-direction: row;
                                flex-wrap: nowrap;
                                gap: 3px;
                    
                         
                                .star{
                                color: $rating_color;
                                font-size: 13px;
                                   }
                                .star-unfilled{
                                color: $black_color2;
                                font-size: 13px;
                                          }
                                      }
                           }
            
                    }
        
                       
        
                      .parent-detail{
                        margin-bottom: 3px;
                        margin-top: 1px;
                        p{
                            font-size: 11px;
                            color: $black_color;
                            font-weight: 500;
                            padding-right: 80px;
                        }
                      }
        
                      .location-detail{
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        padding-right: 4px;
                        .location{
                            font-size: 11px;
                            font-weight: 600;
                        }
        
                        .locationIcon{
                            padding: 3px;
                            background-color: darken($white_color1, 5%);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 6px;
                            color: $black_color2;
                            cursor: pointer;
                            transition: background-color 0.2s ease, color 0.2 ease;
                            span{
                                font-size: 16px;
                            }
        
                            &:hover{
                                background-color: darken($white_color1, 10%);
                                color: $black_color;
                            }
        
        
        
                        }
                      }
    
                      
        
        
                        }
                }
    
                .verticalLine-routeMapCard.dragging {
                    opacity: 0.5;
                  }

            }

            .routeMapColumn .verticalLine-routeMapCard:not(:last-child) .line {
                width: 1px;
                height: 100%;
                border-right: 2px dotted #4169E1;
            }
            
         

           
              

            
            }
        }

        .filter{
            width: 30%;
            height: auto;
            .divider{
                width: 100%;
                // margin-top: 10px;
                padding: 20px;
                span{
                    display: flex;
                    border-bottom: 1px dashed $primary_color;
                }
            }
            .link-customer-div{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 20px;
                width: 100%;
                .input{
                    padding: 8px 18px 8px 5px;
                    border: 1px solid $black_color4;
                    border-radius: 8px;
                    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    
                    select{      
                        width: 100%;        
                        // font-size: 11px;
                        // padding: 2px 10px;
                        background-color: $white_color;
                        // color: $black_color3;
                        border: 0;
                        outline: none;
                        
                        
    
                      
        
                        
                    }
                }
            }

            .input-dropDown{
                width: 100%;
                padding: 0 0 0 0;
                display: flex;
                flex-direction: column;
                background-color: $white_color;
                margin-bottom: 15px;

                label{
                    font-size: 12px;
                    color: $black_color2;
                    margin-bottom: 5px;
                    font-weight: 600;
                }

            .input{
                padding: 8px 18px 8px 5px;
                border: 1px solid $black_color4;
                border-radius: 8px;
                box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);

                select{      
                    width: 100%;        
                    // font-size: 11px;
                    // padding: 2px 10px;
                    background-color: $white_color;
                    // color: $black_color3;
                    border: 0;
                    outline: none;
                    
                    

                  
    
                    
                }
            }

            

           

           
            }

            .saveBtn{
                display: flex;
                justify-content: end;
                padding-top: 15px;
            }
            
            
        }



        .no-details-found{
            width: 70%;
            display: flex;
            justify-content: center;
            align-items: center;

            h3{
                font-size: 16px;
                font-weight: 600;
                color: #6c757d;
            }
    

        }
    }

//media queries
@media(max-width: $breakpoint-md ){

    .route-details-container{
        .routeMapCard-filter-container{
            flex-direction: column;
        }

        .routeMapColumn{
            padding-bottom: 30px;
        }

        .filter{
            width:100%;
        }

        .routeMapCard-filter-container .routeMapCardContainer .routeMapColumn .verticalLine-routeMapCard .routeMapCard{
            width: 250px;
    
        }

    }

}

@media(max-width: 555px ){

    .route-details-container{
        margin: 
        0;

    // .routeMapCard-filter-container .routeMapCardContainer .routeMapColumn .verticalLine-routeMapCard .routeMapCard{
    //     width: 250px;

    // }
}

}
    
