import React, { useCallback, useEffect, useState } from "react";
import "./wallet-transactions.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import {
  agentWalletApprovalUpdate,
  getAgentWalletTransaction,
} from "../../../agent-service";
import { agentWalletTransaction } from "../../../agent.model";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { useCommonContext } from "@/app/contexts/commonContext";
import { debounce } from "@mui/material";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";

interface prop {
  agentId: number | null;
}

interface ErrorResponseData {
  detail?: string;
}

function WalletTransactions({ agentId }: prop) {
  const { fire } = useAlert();
  const [list, setList] = useState<agentWalletTransaction[]>([]);
  const { setIsLoading , maxDate } = useCommonContext();
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [totalPage, setTotalPage] = useState<number>(10);
  const [searchValue, setSearchValue] = useState<string>("");

  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
      getList(page, itemsPerPage, searchValue, selectedDate, value);
    } else if (item === "date") {
      setSelectedDate(value);
      getList(page, itemsPerPage, searchValue, value, status);
    }
  };

  const handlePageChange = (pageNo: number) => {
    console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue, selectedDate, status);
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(1, value, searchValue, selectedDate, status);
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPage: number,
      search: string,
      date: string,
      status: string
    ) => {
      setIsTableLoading(true);
      const skip = (pageNo - 1) * itemsPerPage;
      try {
        const api = await getAgentWalletTransaction(
          agentId,
          skip,
          itemsPerPage,
          search,
          date,
          status
        );
        const modifiedList = api.results.map(
          (item: agentWalletTransaction) => ({
            ...item,
            status: item.status === "in_progress" ? "inprogress" : item.status,
          })
        );
        setList(modifiedList);
        setPage(api.page);
        setTotalPage(api.total_pages);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [agentId] // Only re-create when `agentId` changes
  );

  const handleRest = () => {
    setSearchValue("");
    setSelectedDate("");
    setStatus("");
    getList(1, itemsPerPage, "", "", "");
  };

  useEffect(() => {
    if (typeof agentId === "number" && !isNaN(agentId)) {
      getList(1, 10, "", "", "");
    }
  }, [agentId, getList]);

  const handleShowAcceptAlert = (transaction_id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: "Are you Sure?",
      text: "Are you sure to confirm the transaction.",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        const body = {
          approval_status: "approve",
          remarks: "",
        };

        try {
          setIsLoading(true);
          const response = await agentWalletApprovalUpdate(
            transaction_id,
            body
          );
          console.log("handle show reject result", response);
          fire({
            position: "top-right",
            icon: "success",
            title: "Transaction Confirmed",
            text: "The transaction confirmed successfully!",
            autoClose: 2000,
          });
          getList(1, itemsPerPage, searchValue, selectedDate, status);
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error(err);
          //setIsLoading(false);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleShowRejectAlert = (transaction_id: number) => {
    fire({
      position: "center",
      icon: "error",
      title: "Are you Sure?",
      text: "Are you sure to reject the transaction.",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      initialValue: "ഈ തുക ബാങ്ക് അക്കൗണ്ടിൽ ക്രെഡിറ്റ് ആയിട്ടില്ല",
      onConfirm: async (value: string) => {
        console.log("Input Value:", value);
        const body = {
          approval_status: "reject",
          remarks: value,
        };
        try {
          setIsLoading(true);
          const response = await agentWalletApprovalUpdate(
            transaction_id,
            body
          );
          console.log("handle show reject result", response);
          fire({
            position: "top-right",
            autoClose: 2000,
            icon: "success", // Use success icon
            title: "Transaction Rejected",
            text: "The transaction rejected successfully!",
          });
          getList(1, itemsPerPage, searchValue, selectedDate, status);
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error(err);
          //setIsLoading(false);
        } finally {
          setIsLoading(false);
        }
      },
      onCancel: () => {
        console.log("Cancelled");
      },
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, selectedDate, status),
    300
  );

  return (
    <div className="wallet-transaction-container">
      <div className="table-header-details-page" style={{ height: "60px" }}>
        <div className="filter-search-container">
          <div className="filterButton" onClick={handleToggleFilter}>
            <button>
              <i className="fa-solid fa-filter"></i>
            </button>
          </div>

          <div
            className={`filter-options-select-box ${
              isFilterOpen ? "show" : ""
            }`}
          >
            <div className="filterOption">
              <span>Status {status ? `(${status})` : ""}</span>
              <select
                className="dropdown"
                value={status}
                onChange={(e) => handleFilterSelect(e, "status")}
              >
                <option value="">None</option>
                <option value="in_progress">In Progress</option>
                <option value="failed">Failed</option>
                <option value="success">Success</option>
              </select>
              <span className="material-icons">keyboard_arrow_down</span>
            </div>
            <input
              className="date-picker"
              type="date"
              max={maxDate}
              value={selectedDate}
              onChange={(e) => handleFilterSelect(e, "date")}
            />
          </div>

          <SearchBox
            value={searchValue}
            onChange={handleSearchChange}
            placeholders={[`Search "transaction id"`]}
          />
        </div>
      </div>

      <div className="table2">
        <table>
          <thead>
            <tr>
              <th className="th-first">
                <div className="tableHead">
                  <span className="heading">Date & Time</span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Amount</span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Status</span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Mode of transaction</span>
                </div>
              </th>
              <th className="th-last">
                <div className="tableHeadLast">
                  <span className="heading">Remark</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              {/* <th className="th-last">
         <div className="tableHead">
         <span className="heading">Action</span>
         <span className="material-icons arrowdown">keyboard_arrow_down</span>
         </div>
       </th> */}
            </tr>
          </thead>
          <tbody>
            {isTableLoading ? (
              <tr>
                <td colSpan={6}>
                  <TableWithShimmer
                    no_of_cols={6}
                    no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                  />
                </td>
              </tr>
            ) : list && list.length > 0 ? (
              list.map((data, index) => (
                <tr key={index}>
                  <td>
                    <div className="date-time-invoiceNumber">
                      <div className="dateAndTime">
                        <span className="date">
                          {new Date(data.created_at).toLocaleDateString()}
                        </span>
                        <span className="time">
                          {new Date(data.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="invoiceNumber">{data.transaction_id}</p>
                    </div>
                  </td>
                  <td className="amount2">{data.amount}</td>
                  <td
                    style={{ fontSize: "12px", fontWeight: "600" }}
                    className={`status ${data.status} `}
                  >
                    {data.status}
                  </td>
                  <td>
                    {data.approved === "pending" ? (
                      <div className="modeOfTransactionButtons">
                        <div className="rejectBtn">
                          <button
                            className={`rejectButton`}
                            onClick={() => handleShowRejectAlert(data.id)}
                          >
                            Reject
                          </button>
                        </div>
                        <div className="acceptBtn">
                          <button
                            className={`acceptButton`}
                            onClick={() => handleShowAcceptAlert(data.id)}
                          >
                            Accept
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className={`status ${data.approved} `}>
                        {data.approved}
                      </div>
                    )}
                  </td>
                  <td className="remark">
                    {data.remarks ? data.remarks : "N/A"}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="no-data-emiCollectionHistory">
                  <h5>
                    {status &&
                      !selectedDate &&
                      !searchValue &&
                      `There are no transactions with the status "${status}".`}
                    {selectedDate &&
                      !status &&
                      !searchValue &&
                      `There are no transactions on the date "${selectedDate}".`}
                    {!selectedDate &&
                      !status &&
                      searchValue &&
                      `There are no transactions with the search "${searchValue}".`}
                    {!selectedDate &&
                      !status &&
                      !searchValue &&
                      "It seems you don't have any transactions yet."}
                    {selectedDate &&
                      status &&
                      !searchValue &&
                      `No transactions with the status "${status}" match the selected "${selectedDate}".`}
                    {selectedDate &&
                      status &&
                      searchValue &&
                      `No transactions with the status '${status}' match the selected date '${selectedDate}' and search term '${searchValue}'.`}
                    {!selectedDate &&
                      status &&
                      searchValue &&
                      `No transactions with the status "${status}" match the search "${searchValue}".`}{" "}
                    {selectedDate &&
                      !status &&
                      searchValue &&
                      `No transactions with the search "${searchValue}" match the selected "${selectedDate}".`}
                  </h5>
                  {(selectedDate || searchValue || status) && (
                    <button
                      onClick={handleRest}
                      style={{ marginLeft: "auto", marginRight: "auto" }}
                      className="submitButton"
                    >
                      <span className="material-icons">restart_alt</span>Reset
                    </button>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {list && list.length > 0 && (
        <div className="pagination-table-container">
          <Pagination
            totalPages={totalPage}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={page}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
}

export default WalletTransactions;
