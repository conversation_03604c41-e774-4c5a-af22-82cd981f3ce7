@use '/app/styles/variables' as *;

// .route-create-container{

//         .input-field-container{
//             display: flex;
//             flex-direction: row;
//             justify-content: center;
//             margin-bottom: 8px;
      
       

//             .input-field{
//                 display: flex;
//                 flex-direction: column;
//                 margin-bottom: 4px;
               
//                 label{
//                     font-size: 11px;
//                     color: $black_color2;
//                     font-weight: 600;
//                     margin-bottom: 5px;
//                 }

//                 input{
//                     padding: 8px;
//                     border: 2px solid $black_color4;
//                     border-radius: 6px;
                                       
//                    &::placeholder{
//                     color: $black_color4;
//                     font-size:11px;
//                    }
//                 }

//                 .input{
//                     padding: 0 8px 0 5px;
//                     border: 2px solid $black_color4;
//                     border-radius: 6px;
//                     background-color: #fff;
//                     // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    
//                     select{      
//                         width: 100%;        
//                         font-size: 11px;
//                         background-color: #fff;
//                         color: $black_color4;
//                         border: 0;
//                         outline: none;
//                         padding: 8px 0px;

    
//                     }
//                 }
//             }
//         }

        

//         .SubmitBtn{
//             width: 100%;
//             display: flex;
//             justify-content: end;
//             align-items: center;
//             padding-top: 10px;
//         }

    

//     }
//    }


    
