import React from "react";
import "./RouteOverview.scss";

function RouteOverview() {
  return (
    <div className="route-overview-container">
      <div className="route-overview overview1">
        <div className="header">
          <h3>
            <span className="material-icons groups ">groups</span>Total Customer
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2>470</h2>

          <div className="raise">
            3.90%
            <span className="material-icons arrow-upward">arrow_upward</span>
          </div>

          <p>from last week</p>
        </div>
      </div>
      <div className="route-overview overview2">
        <div className="header">
          <h3>
            <span className="material-icons person">person</span>New Customers
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2> 17</h2>
        </div>
      </div>
      <div className="route-overview overview3">
        <div className="header">
          <h3>
            <span className="material-icons group">group</span>Top Customers
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2>63</h2>

          <div className="raise">
            3.90%
            <span className="material-icons arrow-upward">arrow_upward</span>
          </div>

          <p>from last week</p>
        </div>
      </div>
      <div className="route-overview overview4">
        <div className="header">
          <h3>
            <span className="material-icons group2">local_activity</span>Least
            Customers
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2>4</h2>

          <div className="fall">
            2.90%
            <span className="material-icons arrow-upward">arrow_upward</span>
          </div>

          <p>from last week</p>
        </div>
      </div>
    </div>
  );
}

export default RouteOverview;
