@use "sass:color";
@use "/app/styles/variables.scss" as *;

//@import '/app/styles/variables.scss';

.addUser {
  width: 100%;
  background-color: $white_color1;
  display: flex;
  justify-content: end;
  gap: 10px;
  height: 30px;
  margin: 10px 0;

  @media (max-width: $breakpoint-lg) {
    height: 25px;
  }

  @media (max-width: $breakpoint-md) {
    height: 20px;
  }

  // @media(max-width: 560px){
  //     justify-content: center;
  //     padding: 25px 5px 12px 5px;
  // }

  a {
    text-decoration: none;
  }
}

.user-list-table-container {
  max-width: 100%;
  display: flex;
  flex-direction: column;
  background-color: $white_color1;
 

  .tableBorder {
    border: 1px solid $black_color4;
    border-radius: 8px;
    z-index: 2;
    overflow: hidden;
  }

  .table-header {
    max-width: 100%;
    height: fit-content;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    //border-bottom: 1px solid $black_color4;
    background-color: $white_color;
    z-index: 1;
    flex-wrap: wrap;
    gap: 10px;

    @media (max-width: 640px) {
      justify-content: start;
    }


    @media (max-width: $breakpoint-sm) {
      padding: 20px 18px;    
    }

    h5 {
      //min-width: 135px;
      margin-right: 10px;
      font-size: 22px;
      font-weight: 600;
      color: #2d364f;
  
      @media (max-width: $breakpoint-lg) {
        font-size: 18px;
        text-align: center;
      }
  
      @media (max-width: $breakpoint-md) {
        font-size: 16px;
      }
  
      @media (max-width: $breakpoint-sm) {
        font-size: 12px;
      }
    }

    .filter-search-container {
      display: flex;
      flex-direction: row;
      gap: 5px;
      height: 100%;
      align-items: flex-end;
      position: relative;



      .filter {
        //min-height: 30px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 1px 14px;
        border: 1px solid $black_color4;
        color: $black_color;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          //background-color: darken($white_color1, 10%);
          background-color: color.scale(
            $white_color1,
            $lightness: -10.5371900826%
          );
        }

        .closeIcon {
          font-size: 12px;
          padding: 0;
          margin: 2px 0 0 0;
          color: $black_color3;
          margin-left: 4px;
        }

        .filterIcon {
          color: $black_color;
          font-size: 18px;
          margin: 0;
          padding: 0;
          margin-right: 4px;
        }

        @media (max-width: $breakpoint-lg) {
          font-size: 10px;
          padding: 1px 10px;
        }

        @media (max-width: $breakpoint-md) {
          font-size: 8px;
          padding: 1px 8px;
        }

        @media (max-width: 880px) {
          .moreFiltersTxt {
            display: none;
          }

          .filtersCount {
            color: $red_color;
          }
        }
      }


    

     
    }
  }

  .table-style {
    overflow-x: auto;
    

    

    &::-webkit-scrollbar {
      width: 8px; // Width of the scrollbar
      height: 8px; // Height for horizontal scrollbar (if any)
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0; // Background color of the scrollbar track
      border-radius: 10px; // Rounded corners for the track
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(
        45deg,
        $black_color2,
        $black_color3
      ); // Gradient thumb
      border-radius: 10px; // Rounded corners for the thumb
      border: 2px solid #f0f0f0; // Adds padding between track and thumb
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(
        45deg,
        $black_color,
        $black_color2
      ); // Thumb color on hover
    }
    
    table {
      width: 100%;
      background-color: $white_color;
      border-collapse: separate;
      border-spacing: 0;

      thead {
        position: sticky;
        top: 0;
        z-index: 2;
        background-color: $white_color;
        box-shadow: inset 0 -2px 0 $black_color4, inset 0 2px 0 $black_color4;

        @media(max-width : $breakpoint-sm){
          top: -1px;

        }

        tr {
          th {
            font-size: 11px;
            color: $black_color2;
            padding: 15px 0px;
            text-align: left;
            white-space: nowrap;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;


            @media (max-width: $breakpoint-md) {
              font-size: 10px;
              padding: 10px 15px;
            }
          }
        }
      }

      thead tr th:first-child {
        padding-left: 30px;
      }


      tbody tr td:first-child {
        padding-left: 30px;
      }

      tbody tr {
        border-bottom: 1px solid $black_color4;
      
        &:last-child {
          border-bottom: none;
        }
      }

      tbody {
        td{
          min-width: 100px;
        }
        .name-profilePic {
          display: flex;
          flex-direction: row;
          align-items: center;
          min-width: 140px;

          .name {
            color: $black_color;
            font-size: 12px;
            font-weight: 700;
          }

          .profile-pic {
            position: relative;
            width: 40px;
            height: 40px;
            background-color: $black_color2;
            border-radius: 50%;
            margin-right: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            img {
              object-fit: contain;
            }
          }
        }
        tr {
          transition: background-color 0.3s ease;
          padding: 0 0 0 10px;

          &:nth-child(odd) {
            background-color: $white_color1;
          }

          &:nth-child(even) {
            background-color: $white_color;
          }

          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
          }

          td {
            padding: 10px 0px;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            //border-bottom: 1px solid $black_color4;

            @media (max-width: $breakpoint-lg) {
              font-size: 12px;
              padding: 14px 25px;
            }

            @media (max-width: $breakpoint-md) {
              font-size: 11px;
              padding: 12px 15px;
            }
          }
        }
      }
    }

    
  }
}

.table-header {
  max-width: 100%;
  display: flex;
  justify-content: end;
  padding: 15px 16px;
  background-color: $white_color;
  z-index: 1;

  @media (max-width: 660px) {
    justify-content: start;
  }

  h5 {
    margin-right: 10px;
    font-size: 22px;
    font-weight: 600;
    color: #2d364f;

    @media (max-width: $breakpoint-lg) {
      font-size: 18px;
      text-align: center;
    }

    @media (max-width: $breakpoint-md) {
      font-size: 16px;
    }

    @media (max-width: $breakpoint-sm) {
      font-size: 14px;
    }
  }

  .filter-search-container {
    display: flex;
    flex-direction: row;
    gap: 5px;
    height: 100%;
    align-items: flex-end;
    position: relative;

    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;


      @media (max-width: 880px) {
        display: none;
      }
    }

    .filter {
      height: auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 1px 14px;
      border: 1px solid $black_color4;
      color: $black_color2;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        //background-color: darken($white_color1, 10%);
        background-color: color.scale(
          $white_color1,
          $lightness: -10.5371900826%
        );
      }

      .closeIcon {
        font-size: 12px;
        padding: 0;
        margin: 2px 0 0 0;
        color: $black_color3;
        margin-left: 4px;
      }

      .filterIcon {
        color: $black_color2;
        font-size: 18px;
        margin: 0;
        padding: 0;
        margin-right: 4px;
      }

      @media (max-width: $breakpoint-lg) {
        font-size: 10px;
        padding: 1px 10px;
      }

      @media (max-width: $breakpoint-md) {
        font-size: 8px;
        padding: 1px 8px;
      }

      @media (max-width: 880px) {
        .moreFiltersTxt {
          display: none;
        }

        .filtersCount {
          color: $red_color;
        }
      }
    }

   

    .searchBox {
      display: flex;
      align-items: center;
      border: 1px solid $black_color4;
      padding: 0 8px;
      border-radius: 6px;
      height: 35px;
      

      .searchIcon {
        color: #2d364f99;
        margin-right: 3px;
        font-size: 20px;

        @media (max-width: $breakpoint-sm) {
          font-size: 17px;
        }
      }

      input {
        border: 0;
        background-color: transparent;
        width: 150px;
        //padding: 5px;
      }
      input[type="text"]:focus {
        border: 0;
        outline: none;
      }

      input::placeholder {
        color: #2d364f99;
        font-weight: 600;
        font-size: 9px;
      }

    }

   
  }
}

.table-header-details-page {
  max-width: 100%;
  display: flex;
  justify-content: end;
  padding: 15px 16px;
  border-radius: 0 6px 0 0;
  background-color: $white_color;
  z-index: 1;

  @media (max-width: 660px) {
    justify-content: start;
  }

  h5 {
    min-width: 135px;
    margin-right: 10px;
    font-size: 22px;
    font-weight: 600;
    color: #2d364f;

    @media (max-width: $breakpoint-lg) {
      font-size: 18px;
      text-align: center;
    }

    @media (max-width: $breakpoint-md) {
      font-size: 16px;
    }
  }

  .filter-search-container {
    display: flex;
    flex-direction: row;
    gap: 5px;
    height: 100%;
    align-items: flex-end;
    position: relative;

    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;


      @media (max-width: 880px) {
        display: none;
      }
    }

    .filter-options-walletTransaction {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;


      @media (max-width: 880px) {
        display: none;
      }
    }

    .filter {
      height: auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 1px 14px;
      border: 1px solid $black_color4;
      color: $black_color2;
      border-radius: 6px;
      font-size: 11px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        //background-color: darken($white_color1, 10%);
        background-color: color.adjust($white_color1, $lightness: -10%);
      }

      .closeIcon {
        font-size: 12px;
        padding: 0;
        margin: 2px 0 0 0;
        color: $black_color3;
        margin-left: 4px;
      }

      .filterIcon {
        color: $black_color2;
        font-size: 16px;
        margin: 0;
        padding: 0;
        margin-right: 4px;
      }

      @media (max-width: $breakpoint-lg) {
        font-size: 10px;
        padding: 1px 10px;
      }

      @media (max-width: $breakpoint-md) {
        font-size: 8px;
        padding: 1px 8px;
      }

      @media (max-width: 880px) {
        .moreFiltersTxt {
          display: none;
        }

        .filtersCount {
          color: $red_color;
        }
      }
    }


    // .searchBox {
    //   display: flex;
    //   align-items: center;
    //   border: 1px solid $black_color4;
    //   padding: 0 8px;
    //   border-radius: 6px;
    //   height: 35px;

    //   .searchIcon {
    //     color: #2d364f99;
    //     margin-right: 3px;
    //     font-size: 20px;

    //     @media (max-width: $breakpoint-sm) {
    //       font-size: 17px;
    //     }
    //   }

    //   input {
    //     border: 0;
    //     background-color: transparent;
    //     //padding: 5px;
    //   }
    //   input[type="text"]:focus {
    //     border: 0;
    //     outline: none;
    //   }

    //   input::placeholder {
    //     color: #2d364f99;
    //     font-weight: 600;
    //     font-size: 11px;

    //     @media (max-width: $breakpoint-sm) {
    //       font-size: 9px;
    //     }
    //   }

    //   @media (max-width: $breakpoint-lg) {
    //     padding: 4px 6px;

    //     input {
    //       font-size: 10px;
    //     }
    //   }

    //   @media (max-width: $breakpoint-md) {
    //     padding: 2px 4px;

    //     input {
    //       font-size: 8px;
    //     }
    //   }

    //   // @media(max-width : 880px){
    //   //     display: none;
    //   // }
    // }

    .searchBox {
      display: flex;
      align-items: center;
      border: 1px solid $black_color4;
      padding: 0 8px;
      border-radius: 6px;
      height: 35px;
      

      .searchIcon {
        color: #2d364f99;
        margin-right: 3px;
        font-size: 20px;

        @media (max-width: $breakpoint-sm) {
          font-size: 17px;
        }
      }

      input {
        border: 0;
        background-color: transparent;
        width: 150px;
        //padding: 5px;
      }
      input[type="text"]:focus {
        border: 0;
        outline: none;
      }

      input::placeholder {
        color: #2d364f99;
        font-weight: 600;
        font-size: 9px;
      }

    }

    
  }
}


.view-detail-table-container {
  width: 100%;
  height: calc(100dvh - 197px);
  overflow: hidden;
  border-radius: 0 6px 6px 6px;

  @media (max-width: $breakpoint-lg) {
    height: auto;
  }

  .view-detail-table-menu {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 1px;

    .menuItem {
      width: 290px;
      background-color: $white_color;
      color: $black_color;
      font-size: 16px;
      font-weight: 500;
      padding: 8px 16px 8px 24px;
      position: relative;
      cursor: pointer;
      white-space: nowrap;

      opacity: 0.6;
      transition: opacity 0.3s, background-color 0.3s;

      &.active {
        opacity: 1;
        background-color: $white_color;
        color: $black_color;
      }

      @media (max-width: $breakpoint-sm) {
        font-size: 9px;
        padding: 8px 5px 8px 8px;
      }
    }

    .menuItem1 {
      border-radius: 6px 6px 0 0;
    }

    .menuItem2,
    .menuItem3 {
      border-radius: 6px 6px 0 0;
    }

    .menuItem:not(.active):hover {
      opacity: 0.9;
    }
  }

  .table {
    //display: flex;
    display: block;
    background-color: $white_color;
    overflow-y: auto;
    overflow-x: auto;
    width: 100%;

    &::-webkit-scrollbar {
      width: 8px; // Width of the scrollbar
      height: 8px; // Height for horizontal scrollbar (if any)
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0; // Background color of the scrollbar track
      border-radius: 10px; // Rounded corners for the track
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(
        45deg,
        $black_color2,
        $black_color3
      ); // Gradient thumb
      border-radius: 10px; // Rounded corners for the thumb
      border: 2px solid #f0f0f0; // Adds padding between track and thumb
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(
        45deg,
        $black_color,
        $black_color2
      ); // Thumb color on hover
    }

    table {
      width: 100%;
      //border-collapse: collapse;
      border-collapse: separate;
      border-spacing: 0;

      thead {
        background-color: $white_color1;
        position: sticky;
        top: 0;
        z-index: 2;

        tr {
          th {
            white-space: nowrap;
            padding: 11px 10px;
            text-align: left;

            &:first-child {
              padding-left: 20px;
            }

            &:last-child {
              padding-right: 0;
            }

            .tableHead {
              display: flex;
              align-items: center;

              .heading {
                font-size: 13px;
                margin-right: 10px;
                display: inline-block;
                font-weight: 500;
              }

              .arrowdown {
                font-size: 20px;
                font-weight: 400;
                color: $black_color3;
                cursor: pointer;
                transition: color 0.3s ease;

                &:hover {
                  //color: darken($black_color3, 20%);
                  color: color.adjust($black_color3, $lightness: -20%);
                }
              }
            }

            .order {
              justify-content: space-between;
            }
          }
        }
      }

      tbody {
        tr {
          td {
            padding: 12px 10px;

            //text-align: left;

            &:first-child {
              padding-left: 20px;
            }

            &:last-child {
              padding-right: 20px;
            }
          }
        }
      }
    }
  }

  .table2 {
    background-color: $white_color;
    overflow-y: auto;
    overflow-x: auto;

    &::-webkit-scrollbar {
      width: 8px; // Width of the scrollbar
      height: 8px; // Height for horizontal scrollbar (if any)
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0; // Background color of the scrollbar track
      border-radius: 10px; // Rounded corners for the track
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(
        45deg,
        $black_color2,
        $black_color3
      ); // Gradient thumb
      border-radius: 10px; // Rounded corners for the thumb
      border: 2px solid #f0f0f0; // Adds padding between track and thumb
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(
        45deg,
        $black_color,
        $black_color2
      ); // Thumb color on hover
    }

    table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background-color: $white_color1;

        tr {
          // .dateAndTime{
          //   margin-right: 100px;

          // }

          th {
            padding: 15px 50px 15px 0;
            text-align: left;
            white-space: nowrap;

            &:first-child {
              padding-left: 20px;
            }

            // &:last-child {
            //   padding-right: 0;
            // }

            .tableHead {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .heading {
                font-size: 13px;
                margin-right: 10px;
                display: inline-block;
                font-weight: 500;
                white-space: no-wrap;
              }

              .arrowdown {
                font-size: 20px;
                font-weight: 400;
                color: $black_color3;
                cursor: pointer;
                transition: color 0.3s ease;

                &:hover {
                  //color: darken($black_color3, 20%);
                  color: color.adjust($black_color3, $lightness: -20%);
                }
              }
            }
            .tableHeadLast {
              display: flex;
              align-items: center;

              .heading {
                font-size: 13px;
                margin-right: 10px;
                display: inline-block;
                font-weight: 500;
                white-space: no-wrap;
              }

              .arrowdown {
                font-size: 20px;
                font-weight: 400;
                color: $black_color3;
                cursor: pointer;
                transition: color 0.3s ease;

                &:hover {
                  //color: darken($black_color3, 20%);
                  color: color.adjust($black_color3, $lightness: -20%);
                }
              }
            }
          }
        }
      }

      tbody {
        tr {
          td {
            padding: 6px 50px 6px 0;
            border-bottom: 1px solid $black_color4;
            text-align: left;
            white-space: nowrap;

            &:first-child {
              padding-left: 20px;
            }

            &:last-child {
              padding-right: 20px;
              
            }
          }

          &:last-child {
            td {
              border-bottom: none;
            }
          }

          
        }
      }
    }
  }
}

.filter-options-select-box {
  display: flex;
  gap: 5px;

  .filterOption {
    position: relative;
    width: 180px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    border: 1px solid $black_color4;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    color: $black_color3;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;

    span {
      z-index: 1;
      pointer-events: none;
    }

    .material-icons {
      font-size: 18px;
      color: $black_color3;
    }

    .dropdown {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }

    &:hover {
      //background-color: darken($white_color1, 10%);
      background-color: color.adjust($white_color1, $lightness: -10%);

      color: $black_color2;

      .material-icons {
        color: $black_color2;
      }
    }
  }

  @media (max-width: $breakpoint-lg) {
    .filterOption {
      font-size: 10px;
      padding: 1px 10px;

      .material-icons {
        font-size: 16px;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    .filterOption {
      font-size: 8px;
      padding: 1px 8px;
      width: 120px;

      .material-icons {
        font-size: 14px;
      }
    }
  }

  @media (max-width: 660px) {
    .filterOption {
      width: 100%;
    }
  }

  @media (max-width: 660px) {
    flex-wrap: wrap;
    position: absolute;
    top: 35px;
   
    left: 0;
    opacity: 0;
    pointer-events: none;
    &.show {
      opacity: 1;
      pointer-events: all;
      z-index: 1010;
      background-color: $white_color;
      padding: 10px;
      border: 1px solid $black_color4;
      border-radius: 5px;
      width: 157px;
    }
  }
}

.filterButton {
  position: relative;
  display: none;

  button {
    background-color: $white_color1;
    color: $black_color;
    height: auto;
    width: 35px;
    border: 1px solid $black_color3;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    //display: flex;
    justify-content: center;
    align-items: center;
    height: 35px;
    position: relative;
    font-size: 10px;

    &:hover {
      //background-color: darken($white_color1, 10%);
      background-color: color.adjust($white_color1, $lightness: -10%);
    }
  }

  @media(max-width: 660px){
    display: block;
  }
}

.no-data {
  text-align: center;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  //height: 270px;
  height: calc(100dvh - 390px);
}

.no-data2 {
  text-align: center;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  height: calc(100dvh - 319px);
  
}

.no-data-bankTransactionList{
  width: 100%;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  height: calc(100dvh - 415px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  
  h5{
    text-align: center;
  }

}

.no-data-customerList{
  text-align: center;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  height: calc(100dvh - 322px); 

}

.no-data-emiCollection {
  text-align: center;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  height: calc(100dvh - 270px); 
}

.no-data-emiCollectionHistory{
  text-align: center !important;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  //height: 270px;
  height: calc(100dvh - 394px);
}

.no-data-collectionRoute{
  text-align: center !important;
  color: #6c757d;
  font-size: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  //border-radius: 8px;
  //height: 270px;
  height: calc(100dvh - 398px);

}

.no-data h5 {
  margin: 0;
  font-weight: 400;
  font-style: italic;
  text-align: center;
}

.pagination-table-container {
  width: 100%;
  //border-top: 2px solid $black_color4;
  // border-bottom: 2px solid $black_color4;
}

.table-actions{
  .material-icons{
    font-size: 18px;
    color: $black_color3;
    cursor: pointer;
  }
}