'use client'
import React, { useEffect, useState } from "react";
import "./OverallOverview.scss";
import { Header_Items } from "../home.model";
import { fetchHeaderItems } from "../home-service";
import { useCommonContext } from "@/app/contexts/commonContext";

function OverallOverview({ handleHeaderReceived , isHeaderReceived}: { handleHeaderReceived: () => void, isHeaderReceived : boolean }) {
  const [ headerItems, setHeaderItems ] = useState<Header_Items>();

  const { setIsLoading} = useCommonContext();

  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true); // Show the component after 1 second
    }, 500); // Adjust the time as needed

    return () => clearTimeout(timer);
  }, []);



  useEffect(() => {
    const getHeaderItems = async () => {
      try {
        setIsLoading(true);
        const res = await fetchHeaderItems(); // ✅ No need to include in deps
        handleHeaderReceived();
        setHeaderItems(res);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
  
    if (isHeaderReceived) {
      getHeaderItems();
    }
  }, [isHeaderReceived, handleHeaderReceived, setIsLoading]); // ✅ Removed fetchHeaderItems
  

  return (
    <div className="overall-overview-container">
      <div className={`overall-overview overview1 ${visible ? 'visible' : ''}`}>
        <div className="header">
          <h3>
            <span className="material-icons groups">groups</span>Total Customers
          </h3>
        </div>
        <div className="content">
          <h2>{headerItems?.total_customer}</h2>
        </div>
      </div>

      <div className={`overall-overview overview2 ${visible ? 'visible' : ''}`}>
        <div className="header">
          <h3>
            <span className="material-icons person">person</span>Pending Approvals
          </h3>
        </div>
        <div className="content">
          <h2>{headerItems?.total_pending_approvel}</h2>
        </div>
      </div>

      <div className={`overall-overview overview3 ${visible ? 'visible' : ''}`}>
        <div className="header">
          <h3>
            <span className="material-icons group">group</span>Total Agents
          </h3>
        </div>
        <div className="content">
          <h2>{headerItems?.total_agents}</h2>
        </div>
      </div>

      <div className={`overall-overview overview4 ${visible ? 'visible' : ''}`}>
        <div className="header">
          <h3>
            <span className="material-icons group2">local_activity</span>Total Routes
          </h3>
        </div>
        <div className="content">
          <h2>{headerItems?.total_routes}</h2>
        </div>
      </div>
    </div>
  );
}

export default OverallOverview;
