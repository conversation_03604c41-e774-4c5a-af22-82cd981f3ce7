'use client'
import './styles/variables.scss';
import './login.scss';
import { useForm, SubmitHandler } from 'react-hook-form';
import { loginRes } from './models/common.models';
import { login } from './api/commonService';
import { useRouter } from 'next/navigation';
import { useAlert } from './components/utilities/Alert/Alert';
import { useState } from 'react';
import { AxiosError } from 'axios';
import { useCommonContext } from './contexts/commonContext';
import Loader from './components/Loader/Loader';

type LoginFormInputs = {
  username:string;
  password:string;
};

interface ErrorResponseData {
  detail?: string;
}

export default function Page() {
    const { fire } = useAlert();
    const {setIsLoading} = useCommonContext()
  const navigate = useRouter();
    const { register, handleSubmit, formState: { errors } } = useForm<LoginFormInputs>();
    const [isPasswordVisible, setIsPasswordVisible] = useState(false); // State for password visibility

    const  onSubmit: SubmitHandler<LoginFormInputs> =async (data) => {
      setIsLoading(true)
      try{
        const response: loginRes = await login(data)
        localStorage.setItem("token",response.access_token)
        navigate.push('/pages/home')
      }catch(error){
        setIsLoading(false);
        const axiosError = error as AxiosError<ErrorResponseData>;
        fire({
          position: "center",
          icon: "error",
          title: "Login Failed",
          text: axiosError?.response?.data?.detail || axiosError?.message || "An unknown error occurred",
          confirmButtonText: "Ok",
          // cancelButtonText: "No",
          onConfirm: () => {
            // console.log("Confirmed:");
          },
        })
      } finally {
        // setIsLoading(false)
      }
    };
  return (
    <div className="login-container">
      <div className="login-box">
        <h1 className="login-title">Login to Your Account</h1>
        <form className="login-form" onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              {...register('username', { required: 'Username is required' })}
              placeholder="Enter your username"
              className="form-input"
            />
            {errors.username && <p className="error">*{errors.username.message}</p>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="password-wrapper">
              <input
                type={isPasswordVisible ? 'text' : 'password'} // Toggle password visibility
                id="password"
                {...register('password', { required: 'Password is required' })}
                placeholder="Enter your password"
                className="form-input"
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setIsPasswordVisible(!isPasswordVisible)} // Toggle visibility
                aria-label="Toggle password visibility"
              >
                <span className="material-icons">
                  {isPasswordVisible ? 'visibility_off' : 'visibility'}
                </span>
              </button>
            </div>
            {errors.password && <p className="error">*{errors.password.message}</p>}
          </div>
          <button type="submit" className="login-button">
            Login
          </button>
        </form>
      </div>
      <Loader />
    </div>
  );
}
