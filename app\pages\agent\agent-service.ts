import axiosInstance from "@/app/api/axiosInstance";
import {
  ActivityLog,
  Agent_List,
  agentCollectionOverviewList,
  agentDetails,
  agentWalletTransactionList,
} from "./agent.model";
import { Branch_List } from "../branch/branch.model";
import { routeList } from "../route/route.model";

export const getAgentList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<Agent_List> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await axiosInstance.get(
      `agents/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getAgentDetail = async (
  id: number | null
): Promise<agentDetails> => {
  try {
    const response = await axiosInstance.get<agentDetails>(`agents/${id}/`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createAgentAPi = async (body: FormData) => {
  try {
    const response = await axiosInstance.post(
      "formData/agents/register/",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const editAgent = async (id: number, body: FormData) => {
  try {
    const response = await axiosInstance.put(`formData/agents/${id}/`, body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getBrachList = async () => {
  try {
    const response = await axiosInstance.get<Branch_List>(
      "branches/?pagination=false"
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getAgentEmiCollection = async (
  id: number | null,
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string,
  transaction_mode?: string,
  date?: string
): Promise<agentCollectionOverviewList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });
    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }
    if (transaction_mode) {
      queryParams.append(
        "transaction_mode",
        encodeURIComponent(transaction_mode)
      );
    }
    if (date) {
      queryParams.append("date", encodeURIComponent(date));
    }
    const response = await axiosInstance.get<agentCollectionOverviewList>(
      `dashboard/get_collection_history/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getAgentWalletTransaction = async (
  id: number | null,
  skip: number,
  itemsPerPage: number,
  search: string,
  date: string,
  status?: string
): Promise<agentWalletTransactionList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (date){
      queryParams.append('date', encodeURIComponent(date));
    }

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }
    const response = await axiosInstance.get<agentWalletTransactionList>(
      `dashboard/wallet_transaction/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error
  }
};

export const getAgentCollectionRoute = async (
  id: number | null,
  skip: number,
  itemsPerPage: number,
  search: string
): Promise<routeList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });
    const response = await axiosInstance.get<routeList>(
      `dashboard/route_list/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error
  }
};

export const agentWalletApprovalUpdate = async (
  id: number,
  body: { remarks: string; approval_status: string }
) => {
  try {
    const response = await axiosInstance.put(`approve_or_reject/${id}`,body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getAgentActivity = async (
  id:number | null,
  date: string
) : Promise<ActivityLog[]> => {
  try {
    const queryParams = new URLSearchParams({

    });
    if(date){
      queryParams.append('date', encodeURIComponent(date))
    }

    const response = await axiosInstance.get<ActivityLog[]>(
      `agents/dashboard/agent_activity/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

//delete Agent
export const deleteAgent = async ( agent_id:number ) => {
  try{
      const response = await axiosInstance.delete(`/agents/dashboard/delete/${agent_id}/`);
      return response;

  }catch(error){
      console.error("error deleting agent", error);
      throw new Error("Failed to delete the agent")
      
  }
}

//disable Agent
export const disableAgent = async ( agent_id:number,status_message:string ) => {
  try{
      const response = await axiosInstance.delete(`/agents/dashboard/disable/${agent_id}/?status_message=${status_message}`);
      return response;

  }catch(error){
      console.error("error disabling agent", error);
      throw new Error("Failed to disable the agent")
      
  }
}