@import '/app/styles/variables.scss';

.transaction-additional-details {
  width: 100%;
  background-color: #eff2fb;
  border-radius: 0 0 5px 5px;

  .t-a-d-row {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .t-a-d-col {
      padding: 16px 18px 5px 18px;
    }

    .col1 {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .attachedDocuments {
        //background-color: red;
        width: 15%;

        h5 {
          font-size: 11px;
          padding-bottom: 3px;
        }

        .docs-field-border {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          .docs-field {
            display: flex;
            flex-direction: column;

            ul {
              list-style: none;
              padding-right: 25px;

              li {
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                // border-bottom: 1px solid $black_color;
                a {
                  color: $link_color;
                  font-weight: 600;
                  font-size: 11px;
                  text-underline-offset: 2px;
                  transition: color 0.2s ease;

                  &:hover {
                    color: darken($link_color, 20%);
                  }
                }
              }
            }

            .addBtn {
              margin: 20px 0 0 3px;
            }
          }

          .t-a-d-border {
            display: flex;
            align-items: start;
            justify-content: center;

            .border {
              height: 145px;
              border-left: 1px solid $black_color4;
            }
          }
        }
      }

      .DisountHistory {
        width: 22%;
        //background-color: blue;
        display: flex;
        flex-direction: column;
        padding: 0 0 0 15px;

        .discountHistoryHead {
          width: 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-right: 11px;

          h5 {
            font-size: 11px;
            display: flex;
            align-self: center;
            padding-bottom: 3px;
          }

          .moreVertButton {
            span {
              font-size: 16px;
            }
          }
        }

        .checkbox-date-time-discount-border-box {
          display: flex;
          flex-direction: row;
          padding: 0 0 0 10px;

          .checkbox-date-time-discount-container {
            display: flex;
            flex-direction: column;
            margin-right: 30px;
            padding-top: 8px;

            .checkbox-date-time-discount-box {
              display: flex;
              flex-direction: row;
              margin-bottom: 6px;

              input[type="checkbox"] {
                transform: scale(1);
                margin-bottom: 12px;
                margin-right: 10px;
              }

              .date-time {
                width: 90px;
                display: flex;
                flex-direction: column;

                .date {
                  color: $black_color;
                  font-size: 11px;
                  font-weight: 700;
                  margin-bottom: 2px;
                }

                .time {
                  color: $black_color;
                  font-size: 11px;
                  font-weight: 400;
                }
              }

              .hyphen {
                padding: 0 15px;
                font-size: 12px;
                font-weight: 700;
              }

              .discount {
                color: $black_color;
                font-size: 11px;
                font-weight: 700;
              }
            }
          }

          .t-a-d-border {
            display: flex;
            align-items: start;
            justify-content: center;

            .border {
              height: 145px;
              border-left: 1px solid $black_color4;
            }
          }
        }
      }

      .col2 {
        width: 57%;
        //background-color: green;
        display: flex;
        flex-direction: column;

        .advAmt-payMode-dueDate {
          display: flex;
          flex-direction: row;
          padding-bottom: 15px;

          .advAmt,
          .payMode,
          .dueDate {
            margin-right: 20px;

            h5 {
              font-size: 11px;
              margin-bottom: 3px;
            }
          }

          .advAmt {
            h4 {
              font-size: 13px;
              color: $green_color2;
              font-weight: 800;
            }
            h6{
              font-weight: 500;
              color: $black_color3;
              font-size: 12px;
            }
          }

          .payMode {
            select{
              display: flex;
              //flex-direction: column;
              justify-content: center;
              background-color: $white_color;
              border: 1px solid #ccc;
              box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
              // padding: 10px;
              border-radius: 5px;
              width: 100px;
              z-index: 10;
              gap: 10px;
              font-size: 13px;
              padding: 5px 3px;
            }
            .plan {
              display: flex;
              flex-direction: row;
              justify-content: center;
              align-items: center;
              color: $black_color3;
              font-size: 12px;

              

              span {
                color: $black_color3;
                font-size: 14px;
                margin-left: 6px;
                cursor: pointer;
                transition: color 0.2s ease;

                &:hover {
                  color: darken($black_color3, 20%);
                }
              }
            }
          }

          .dueDate {
            h4 {
              font-size: 12px;
              color: $black_color3;
              font-weight: 500;
            }
          }
        }
      }
    }

    // .col2{
    //     width: 50%;
    //     display: flex;
    //     flex-direction: column;

    //     .advAmt-payMode-dueDate{
    //         display: flex;
    //         flex-direction: row;
    //         padding-bottom: 15px;

    //         .advAmt, .payMode, .dueDate{
    //         margin-right: 20px;

    //             h5{
    //                 font-size: 11px;
    //                 margin-bottom: 3px;
    //             }
    //         }

    //         .advAmt{
    //          h4{
    //             font-size: 13px;
    //             color: $green_color2;
    //             font-weight: 800;
    //          }

    //         }

    //         .payMode{
    //             .plan{
    //                 display: flex;
    //                 flex-direction: row;
    //                 justify-content: center;
    //                 align-items: center;
    //                 color: $black_color3;
    //                 font-size: 12px;

    //                 span{
    //                     color: $black_color3;
    //                     font-size: 14px;
    //                     margin-left: 6px;
    //                     cursor: pointer;
    //                     transition: color 0.2s ease;

    //                     &:hover{
    //                         color: darken($black_color3,20%);
    //                     }
    //                 }

    //             }
    //          }

    //          .dueDate{

    //             h4{
    //                 font-size: 12px;
    //             color: $black_color3;
    //             font-weight: 500;
    //             }
    //          }
    //     }
    // }
  }

  .remark {
    p {
      font-size: 11px;
      font-weight: 700;
      padding-bottom: 5px;
    }

    .remark-text-field {
      textarea {
        width: 100%;
        height: 105px;
        border: 1px solid $black_color4;
        border-radius: 8px;
        padding: 0 0 50px 10px;
        max-height: 130px;
        &::placeholder {
          color: $black_color3;
          font-size: 11px;
        }
      }
    }
  }
  @media screen and (max-width: 1200px) {
    .t-a-d-row{
      .col1{
        .attachedDocuments{
          width: 50%;
        }
        .DisountHistory{
          width: 50%;
          .checkbox-date-time-discount-border-box{
            .t-a-d-border{
              display: none;
            }
          }
        }
        .col2{
          width: 100%;
          .advAmt-payMode-dueDate{
            width: 80%;
            gap: 20px;
          }
        }
      }
    }
  }
}

.showData {
  opacity: 1;
  visibility: visible;
  // height: auto; /* Automatically adjusts height */
  max-height: 1000px; /* Adjust this value to suit your content size */
  // overflow: hidden;
  // padding: 10px; /* Optional padding for smoothness */
  transition: opacity 0.3s ease-in-out, max-height 0.3s ease-in-out;
}

.hideData {
  opacity: 0;
  visibility: hidden;
  max-height: 0; /* Collapse height */
  overflow: hidden;
  pointer-events: none;

  transition: max-height 0.3s ease-in-out, visibility 0.3s ease-in-out,
    opacity 0.3s ease-in-out;
}
