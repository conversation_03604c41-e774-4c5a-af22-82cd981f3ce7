"use client";
import React, { useCallback, useEffect, useState } from "react";
import Link from "next/link";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import AgentDetails from "./components/AgentDetails/AgentDetails";
import "./agentList.scss";
import Image from "next/image";
import dummyprofile from "../../../public/images/profile.png";
import AgentCreate from "./components/AgentCreate/AgentCreate";
// import { useCommonContext } from "@/app/contexts/commonContext";
import { deleteAgent, disableAgent, getAgentList } from "./agent-service";
import { Agent_List_Item } from "./agent.model";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { DropdownItem } from "../users/page";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { useCommonContext } from "@/app/contexts/commonContext";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";

export interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const [agentList, setAgentList] = useState<Agent_List_Item[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [SelectedUserId, setSelectedUserId] = useState<number>(1);
  const [visible, setVisible] = useState<boolean>(false);

  const [isTableLoading, setIsTableLoading] = useState<boolean>(true);
  const [showCreate, setShowCreate] = useState<boolean>(false);

  //filters
  // const [status, setStatus] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;
  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(1, itemsPerPage, "");
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleShowDetails = (id: number) => {
    setShowDetails(true);
    if (id) {
      setSelectedUserId(id);
    }
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    getList(currentPage, itemsPerPage, searchValue);
  };

  const getList = useCallback(
    async (pageNo: number, itemsPerPageNo: number, searchKey: string) => {
      setIsTableLoading(true);
      const skip = (pageNo - 1) * itemsPerPageNo;
      try {
        const response = await getAgentList(skip, itemsPerPageNo, searchKey);
        setAgentList(response.results);
        setTotalPages(response.total_pages);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    getList(1, 10, "");
  }, [getList]);

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value),
    300
  );

  const handleDeleteAgent = (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure you want to delete this agent?`,
      text: "This action cannot be undone!",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        try {
          setIsLoading(true);
          await deleteAgent(id);
          getList(currentPage, itemsPerPage, searchValue);
          fire({
            icon: "success", // Use success icon
            title: "Agent deleted",
            text: "The agent is deleted successfully!",
            autoClose:2000
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error("Failed to delete the agent:", err);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleDisableAgent = (item: DropdownItem, id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure?`,
      text: `To ${
        item.id === "disable-agent" ? "disable" : "enable"
      } the agent .`,
      confirmButtonText: "yes",
      cancelButtonText: "No",
      ...(item.id === "disable-agent"
        ? { initialValue: "Currently Unavailable" }
        : {}),
      onConfirm: async (value: string) => {
        try {
          setIsLoading(true);
          await disableAgent(id, value);
          getList(currentPage, itemsPerPage, searchValue);
          fire({
            position: "top-right",
            icon: "success", // Use success icon
            title: `Agent ${
              item.id == "disable-agent" ? " disabled" : "enabled"
            } successfully!`,
            autoClose: 2000,
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error("Failed to disable the agent", err);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    //console.log(item, id);
    if (item.id == "delete-agent") {
      handleDeleteAgent(id);
    } else {
      handleDisableAgent(item, id);
    }
  };

  return (
    <>
      <div
        className={`agentList overall-list-padding ${visible ? "visible" : ""}`}
      >
        {/* <AgentOverview /> */}
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Agent +
          </div>
        </div>

        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <h5>Agent List</h5>

              <div className="filter-search-container">
                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[`Search "name"`,
                    `Search "phone"`,
                    `Search "code"`,
                  ]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={6}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Agent Name</th>
                      <th>CURRENT STATUS</th>
                      <th>Employee ID</th>
                      <th>Wallet Amount</th>
                      <th>Contact</th>
                      <th>Details</th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    {agentList && agentList.length > 0 ? (
                      agentList.map((data, index) => {
                        const menuItems = [
                          {
                            id: data.is_active
                              ? "disable-agent"
                              : "enable-agent",
                            label: data.is_active
                              ? "Disable Agent"
                              : "Enable Agent",
                          },
                          { id: "delete-agent", label: "Delete Agent" },
                        ];
                        return (
                          <tr
                            key={index}
                            onClick={() => handleShowDetails(data.id)}
                          >
                            <td>
                              <div className="name-profilePic">
                                <div className="profile-pic">
                                  {/* <Image
                                  src={
                                    data.profile_photo
                                      ? mediaUrl + data.profile_photo
                                      : dummyprofile
                                  }
                                  alt="Profile Picture"
                                  fill
                                  className="profileImage"
                                  style={{ objectFit: "cover" }}
                                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                ></Image> */}

                                  {mediaUrl && data.profile_photo ? (
                                    <Image
                                      src={
                                        data.profile_photo
                                          ? mediaUrl + data.profile_photo
                                          : dummyprofile
                                      }
                                      alt="Profile Picture"
                                      fill
                                      className="profileImage"
                                      style={{ objectFit: "cover" }}
                                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                      loading="lazy"
                                    />
                                  ) : (
                                    <p>Unavailable</p>
                                  )}
                                </div>
                                <div className="name">{data.name}</div>
                              </div>
                            </td>

                            <td>
                              {data.is_active ? (
                                <div className="available">
                                  <span>Available</span>
                                </div>
                              ) : (
                                <div className="offline">
                                  <span>offline</span>
                                </div>
                              )}
                            </td>

                            <td>
                              <span className="empId">{data.agent_code}</span>
                            </td>
                            <td>
                              <span className=" walletAmount">
                                {data.wallet_amount}
                              </span>
                            </td>
                            <td>
                              <span className="contact">{data.phone}</span>
                            </td>
                            <td>
                              {data.is_active ? (
                                <div className="viewDetails">
                                  <Link
                                    href=""
                                    onClick={() => handleShowDetails(data.id)}
                                  >
                                    View Details
                                  </Link>
                                </div>
                              ) : (
                                <div className="user-not-available">
                                  <p style={{ textAlign: "left" }}>
                                    {data.status_message
                                      ? data.status_message
                                      : "User Not Available"}
                                  </p>
                                </div>
                              )}
                            </td>
                            <td onClick={(e) => e.stopPropagation()}>
                              <TableMenuTwo
                                items={menuItems}
                                onClick={handleMenuItemClick}
                                id={data.id}
                              />
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={6} className="no-data2">
                          <h5>
                            {!searchValue &&
                              `It looks like you dont have an agent yet.`}
                            {searchValue &&
                              `No agent match your search for "${searchValue}".`}
                          </h5>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {agentList && agentList.length > 0 && (
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </div>

      <AgentDetails
        showDetails={showDetails}
        agentId={SelectedUserId}
        handleClose={handleCloseDetails}
      />
      <AgentCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
      />
    </>
  );
}

export default Page;
