@import '/app/styles/variables.scss';

.agent-details-container
{
    position: fixed;
     top: 0;
     bottom: 0;
     left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
    z-index: 5000;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease, z-index 0s linear 0.3s;
    overflow-y: auto;
    //padding-bottom: 15px;
    //border-radius: 8px;
        &.show{
            opacity: 1;
            z-index: 500;
        }
 

    .agentDetailsHeader{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px 25px;
       
        p{
            display: flex;
            flex-direction: row;
            gap: 10px;
            color: $white_color;
            font-size: 12px;

            .active{
                cursor: pointer;
                color: $primary_color;
            }
        }
    }

    .user-details-transaction-details-container{
        width: 100%; 
        padding: 0 25px 0 25px;  
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        @media(max-width: $breakpoint-lg){
            flex-direction: column;
            gap: 30px;
            padding: 0 25px 25px 25px; 

        }
  
  
        .user-details{
          height: calc(100dvh - 60px) ;
          width: 22%;
          background-color: $white_color;
          display: flex;
          flex-direction: column;
          border-radius: 8px;
          padding-bottom: 20px;

          @media (max-width: $breakpoint-lg){
            width: 100%;
            height: auto;
          }
  
          .user-profile{
              width: 100%;
              height: auto;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              border-bottom: 1px solid $black_color4;
              padding: 20px 0 15px 0;
  
              .profilePicture{
                  width: 90px;
                  height: 90px;
                  background-color: $black_color3;
                  border-radius: 50%;
                  overflow: hidden;
                  position: relative;
                  margin-bottom: 8px;
      
  
                  .profileImage{
                      object-fit: cover;
                      width: 100%;
                      height: 100%;
                  }
              }

              .editBtn{
                width: 100%;
                padding-right: 15px;
                display: flex;
                justify-content: end;
                cursor: pointer;
            

                span{
                    font-size: 15px;
                    color: $black_color3;
                    
                }

                &:hover{
                    span{
                        color: darken($black_color3, 20%);
                    }
                }
              }
  
              .username{
                  font-weight: 600;
                  margin: 0 0 3px 0;
                  font-size: 16px;
                  text-align: center;
                  width: 100%;
                  padding: 0 15px;
  
              }
  
              .address{
                  padding: 0 40px;
                  text-align: center;
                  font-size: 12px;
                  color: $black_color2;
              }
  
            //   .rating{
            //       display: flex;
            //       flex-direction: row;
            //       flex-wrap: nowrap;
            //       gap: 3px;
            //       padding: 6px 0 5px 0;
            //       margin-bottom: 13px;
  
            //       .star{
            //           color: $rating_color;
            //           font-size: 18px;
            //       }
            //       .star-unfilled{
            //           color: $black_color2;
            //           font-size: 18px;
            //       }
            //   }
  
            //   .wallet{
            //       display: flex;
            //       flex-direction: row;
            //       justify-content: center;
            //       align-items: center;
            //       gap: 25px;
           
               
          
  
            //       p{
            //           display: flex;
            //           flex-direction: row;
            //           justify-content: center;
            //           align-items: center;
  
  
  
            //           .walletIcon{
            //              color: $primary_color;
            //              margin-right: 7px;
            //              font-size: 15px;
  
            //           }
            //           .txt{
            //               font-size: 12px;
            //               font-weight: 600;
            //           }
  
              
                 
  
            //       }
  
            //       .rupees{
            //           font-size: 14px;
            //           font-weight: 800;
            //           color: $green_color2;
            //       }
  
  
                  
                  
            //   }
  
            
  
              
  
          }
  
        //   .user-purchase{
        //       margin: 12px;
        //       padding: 9px;
        //       border-radius: 8px;
        //       background-color: $primary_color;
  
        //       h3{
        //           font-size: 14px;
        //           color: $white_color;
        //           font-weight: 500;
        //       }
  
        //       .totalPayment{
        //           padding-top: 11px;
                 
        //           .amount{
             
        //               display: flex;
        //               flex-direction: row;
        //               justify-content: space-between;
  
        //               .totalpaid{
        //                   font-size: 11px;
        //                   font-weight: 700;
        //                   color: $white_color;
                     
        //               }
  
        //               .total{
        //                   font-size: 11px;
        //                   font-weight: 500;
        //                   color: $white_color;
  
        //               }
        //           }
  
        //           .paymentBar{
        //               width: 100%;
        //               height: 7px;
        //               background-color: $white_color;
        //               border-radius: 6px;
        //               margin-top: 3px;
        //           }
  
               
        //       }
        //   }
  
         .user-additional-details-border{
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          
  
          .border{
              width: 300px;
              border-bottom: 1px solid $black_color4;
             
      
  
          }
         }
  
  
          .user-additional-details{
              display: flex;
              flex-direction: column;
              padding: 10px 0 0 15px;
              //padding: 13px 25px 20px 25px; 
              
              //overflow-x: auto;
  
            //   &::-webkit-scrollbar {
            //       width: 8px; // Width of the scrollbar
            //       height: 8px; // Height for horizontal scrollbar (if any)
            //     }
              
            //     &::-webkit-scrollbar-track {
            //       background: #f0f0f0; // Background color of the scrollbar track
            //       border-radius: 10px; // Rounded corners for the track
            //     }
              
            //     &::-webkit-scrollbar-thumb {
            //       background: linear-gradient(45deg, $black_color2, $black_color3); // Gradient thumb
            //       border-radius: 10px; // Rounded corners for the thumb
            //       border: 2px solid #f0f0f0; // Adds padding between track and thumb
            //     }
              
            //     &::-webkit-scrollbar-thumb:hover {
            //       background: linear-gradient(45deg, $black_color, $black_color2); // Thumb color on hover
            //     }
             
  
              .detail{
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  gap: 15px;
                  margin-bottom: 12px;
  
                  .icon{
                      width: 35px;
                      height: 35px;
                      background-color: $white_color1;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      color: $primary_color;
                      border-radius: 50%;
  
                      span{
                          font-size: 18px;
                      }
  
                  }
  
  
  
                  .content{
                      display: flex;
                      flex-direction: column;
                      gap: 4px;
  
                      .label{
                     font-size: 10px;
                     color: $black_color2;
                      }
  
                      .permanentAddress{
                          padding: 0 30px 0 0;
                      }
  
                      .value{
                      font-size: 11px;
                      font-weight: 500;
                  
                      }

                      .lastLogin{
                        display: flex;
                        justify-content: row;
                        font-size: 11px;
                        font-weight: 500;

                        .date{
                            height: fit-content;
                            border-right: 2px solid $black_color2;
                            padding-right: 4px;
                        }
                        .time{
                            height: fit-content;
                            padding-left: 4px;
                            font-size: 11px;
                            font-weight: 500;
                        }
                      }
                    
  
                    //   a{
                    //       font-size: 11px;
                    //       color: $link_color;
                    //       text-underline-offset: 2px;
  
                    //       &:hover{
                    //           color: darken($link_color, 20%);
                    //       }
                    //   }
  
                      
                 
                  }
  
  
                
              }
  
              .detail4{
                  .icon{
                      width: 35px;
                      height: 35px;
                  }
              }
  
              // .detail1{
              //     padding-top: 15px;
              // }
  
              
  
          }
  
          // .loadMore{
          //     display: flex;
          //     justify-content: center;
          //     align-items: center;
          //     padding-top: 3px;
  
          //     .arrowDownButton{
          //         span{
          //             font-size: 24px;
          //             color: $black_color3;
          //             cursor: pointer;
  
          //             &:hover{
          //                 color: $black_color2;
          //             }
          //         }
  
          //     }
          // }
  
        }

        .transaction-details{
            
            width: 78%;
            display: flex;
            flex-direction: column;
            padding: 0 0 0 18px;
            //display: none;

            @media (max-width: $breakpoint-lg){
                padding: 0;
                width: 100%;
            }


          

            .user-overview-container{
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-bottom: 15px;
                gap: 20px;

                //for horizontal scroll
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 10px;

                &::-webkit-scrollbar {
                    width: 8px; // Width of the scrollbar
                    height: 5px; // Height for horizontal scrollbar (if any)
                  }
                
                  &::-webkit-scrollbar-track {
                    background: #f0f0f0; // Background color of the scrollbar track
                    border-radius: 10px; // Rounded corners for the track
                  }
                
                  &::-webkit-scrollbar-thumb {
                    background: linear-gradient(45deg, $black_color2, $black_color3); // Gradient thumb
                    border-radius: 10px; // Rounded corners for the thumb
                    border: 1px solid #f0f0f0; // Adds padding between track and thumb
                  }
                
                  &::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(45deg, $black_color, $black_color2); // Thumb color on hover
                  }

                .userOverview{
                    width: calc(100% - 80px);
                    height: 110px;
                    background-color: $white_color;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    border-radius: 7px;

                    //for horizontal scroll
                    min-width: 250px;

                    @media (max-width: $breakpoint-md) {
                        min-width: 350px;
                    }

                    @media (max-width: $breakpoint-sm) {
                        min-width: 320px;
                    }
                    
                    
                }

                @media (max-width: $breakpoint-lg) {
                    gap: 15px;
                }
            
                @media (max-width: $breakpoint-md) {
                    gap: 10px;
                }
            
                @media (max-width: $breakpoint-sm) {
                    gap: 8px;
                    overflow-x: auto; 
                    flex-wrap: nowrap; 
                }
            
                @media (max-width: $breakpoint-xs) {
                    gap: 5px;
                    overflow-x: auto; 
                    flex-wrap: nowrap;
                }

                .userOverview1{
                    padding: 22px 0 20px 0;

                    h3,h1{
                        padding: 0 15px;
                    }

                    h3{
                      font-size: 16px;
                     
                      //margin-bottom: 12px;
                    
                    }

                    h1{
                        color: $green_color2;
                        font-weight: 800;
                        font-size: 28px;
                    }
                }

                .userOverview2, .userOverview3, .userOverview4{
                    //gap: 5px;
                    padding: 12px 0 17px 0;
                  

                    .icon{
                        padding: 0 15px;
    
                    }
                    p,h3{

                        padding: 0 15px;

                    }

                    h3{
                        font-size: 16px;
                        font-weight: 600;
                        
                    }

                    p{
                        color: $black_color2;
                        font-size: 11px;
                        //margin-bottom: 5px;
                    }
                }

                .userOverview2{
                    .icon {
                        margin-left: 13px;
                        //margin-bottom: 8px;
                        width: 35px;
                        height: 35px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #FFF2DD;
                        border-radius: 50%;
                    
                        span {
                            padding: 3px;
                            background-color: #FFE5BF; 
                            color: #FF8000; 
                            border-radius: 4px;
                            border-radius: 50%;
                            font-size: 20px;
                        }
                    }
                    
                    
                }

                .userOverview3{
                    .icon {
                        margin-left: 13px;
                        //margin-bottom: 8px;
                        width: 28px;
                        height: 28px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #F8D7DA;
                        border-radius: 8px;
                        border: 2px solid #F1B0B7;
                    
                        span {
                            padding: 3px;
                            //background-color: #FADBD8;
                            color: #B71C1C; 
                            border-radius: 4px;
                            border-radius: 50%;
                            font-size: 20px;
                            
                        }
                    }
                    
                    
                }

                // .userOverview4{
                //     .icon {
                //         margin-left: 13px;
                //         //margin-bottom: 8px;
                //         width: 35px;
                //         height: 35px;
                //         display: flex;
                //         justify-content: center;
                //         align-items: center;
                //         background-color: #E7F5FF;
                //         border-radius: 50%;
                    
                //         span {
                //             padding: 3px;
                //             background-color: #D0EBFF; 
                //             color: #38A1F3; 
                //             border-radius: 4px;
                //             border-radius: 50%;
                //             font-size: 22px;
                //         }
                //     }

                //     h3{
                //         color: $green_color2;
                //     }
                    
                    
                // }
            }

            



            .collection-route-container{
                height: calc(100dvh - 85px);
                background-color: $white_color;

                @media (max-width: $breakpoint-lg){
                    height: auto;
                }

                .table2{
                    max-height: calc(100dvh - 349px);

                    table{
                        thead{
                            th{
                                font-size: 12px;
                                font-weight: 500;
                                color: $black_color2;
                            }
                        }
    
                        tbody{
                            .routeDetails{
                                display: flex;
                                flex-direction: column;
                
                                .day{
                                    font-size: 12px;
                                    color: $black_color3;
                                    font-weight: 500;
                                
                                }
                
                                .route{
                                    font-size: 12px;
                                    color: $black_color;
                                    font-weight: 600;
                                }
                
                                .viewDetails{
                                    a{
                                        font-size: 11px;
                                        font-weight: 600;
                                        color: $link_color;
                                        cursor: pointer;
                                        text-decoration: none;
                                       
                               
                                       &:hover{
                                            color: darken($link_color, 10%);
                               
                                        }
                                    }
                                }
                                }
                
                                .branchName{
                                font-size: 12px;
                                font-weight: 600;
                                }
                
                               .createdBy{
                                display: flex;
                                flex-direction: column;
                                
                                .name{
                                font-size: 12px;
                                font-weight: 600;
                                color: $black_color;
                                margin-bottom: 1px;
                                }
                
                                .position{
                                    font-size: 10px;
                                    font-weight: 500;
                                    color: $black_color3;
                
                                }
                                }
                
                                .createdDate{
                                    font-size: 12px;
                                    font-weight: 600;
                                    }
                        }
                    }
                }
            }

            .wallet-transaction-container{
                height: calc(100dvh - 85px);
                background-color: $white_color;

                @media (max-width: $breakpoint-lg){
                    height: auto;
                }

                .table2{
                    max-height: calc(100dvh - 344px);

                    table{
                        tbody{
                            tr{

                                .date-time-invoiceNumber{
                                    display: flex;
                                    flex-direction: column;
                                    white-space: nowrap;
                                    
                      
                                    .dateAndTime{
                                      display: flex;
                                      flex-direction: row;
                                      margin-bottom: 3px;
                                      white-space: nowrap;
                                      
                                
                                      .date, .time{
                                        font-size: 12px;
                                        color: $black_color;
                                        font-weight: 600;
                                        white-space: nowrap;
                                        
                                      }
                      
                                      .date{
                                        border-right:  2px solid $black_color;
                                        padding-right: 5px;
                                      }
                      
                                      .time{
                                        padding-left: 5px;
                                      }
                      
                                    
                                     }
                      
                                     .invoiceNumber{
                                      font-size: 10px;
                                      color: $black_color3;
                                      white-space: nowrap;
                                    }
                                   }
                                
                                   .amount2{
                                    color: $black_color;
                                    font-size: 12px;
                                    font-weight: 700;
                                }

                                .pending{
                                    color: $black_color3;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                
                                .inProcess{
                                    color: orange;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                
                                .approved{
                                    color: $green_color2;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                
                                .rejected{
                                    color: $red_color;
                                    font-size: 12px;
                                    font-weight: 600;
                                }

                                .modeOfTransactionButtons{
                                    display: flex;
                                    flex-direction: row;
                                    gap: 5px;
                
                                    .rejectButton{
                                      background-color: $white_color;
                                     }
                                }
                                  
                                .remark{
                                    white-space: wrap;
                                    font-size: 12px;
                                    font-weight: 600;
                                    color: $black_color3;
                    
                                    .remark-text-field {
                                        input {
                                            width: 100%;
                                            height: 30px;
                                            border: 1px solid $black_color4;
                                            border-radius: 3px;
                                            padding: 0 0 0 5px;
                    
                                            &::placeholder{
                                                color: $black_color3;
                                                font-size: 11px;
                                            }
                                        }
                    
                                      
                                    }
                                    }
                            }
                        }
                    }

                }
            }

            .activity-card-container{
                max-width: 100%;
                background-color: $white_color;
                display: flex;
                flex-direction: column;
                height: 511px;
                overflow-y: auto;

                .activity-card{
                    display: flex;
                    flex-direction: column;
                    padding: 0 20px;
                    

                    .dayDate-viewDetails{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 10px;
                        width: 250px;

                        padding-left: 30px;

                        .dayDate{
                        display: flex;
                        flex-direction: row;
                        font-size: 13px;
                        color: $black_color;
                        font-weight: 600;

                        .day{
                            border-right: 2px solid $black_color;
                            padding-right: 5px;
                        }

                        .Date{
                            padding-left: 5px;
                        }
                        }

                        
                        .viewDetails{
                            a{
                                font-size: 13px;
                                font-weight: 600;
                                color: $link_color;
                                cursor: pointer;
                                text-decoration: underline;
                                text-underline-offset: 2px;
                               
                       
                               &:hover{
                                    color: darken($link_color, 10%);
                       
                                }
                            }
                        }

                    }

                    .timeline {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        width: 100%;
                        position: relative;
                        min-height: 1000px;
                      }
                      
                      .event {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        text-align: center;
                        position: relative;
                        flex: 1;
                       
                      }
                      
                      .time {
                        font-size: 12px;
                        font-weight: 500;
                        color: #555;
                        margin-bottom: 10px; /* Add space between time and dot */
                      }
                      
                      .dot {
                        width: 12px;
                        height: 12px;
                        background-color: $white_color; /* Blue color for the dot */
                        border-radius: 50%;
                        position: relative;
                        z-index: 2;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border: 1px solid $primary_color;
                      
                        .innerdot{
                          width: 6px;
                          height: 6px;
                          border-radius: 50%;
                          background-color: $primary_color;
                        }
                      }
                      
                      
                      
                      .description {
                        min-height: 65px;
                        width: 95px;
                        font-size: 12px;
                        color: #888;
                        margin-top: 10px; /* Add space between dot and description */
                        
                      }
                      
                      .dottedLine {
                        position: absolute;
                        top: 29px; /* Align with the dot */
                        height: 2px;
                        background: repeating-linear-gradient(
                          to right,
                          #00bcd4,
                          #00bcd4 5px,
                          transparent 5px,
                          transparent 10px
                        );
                        z-index: 1;
                      }
                      
                      .dottedLine.left {
                        left: 0;
                        width: 50%; /* Covers only the left half of the event */
                      }
                      
                      .dottedLine.right {
                        right: 0;
                        width: 50%; /* Covers only the right half of the event */
                      }




                }


            }

            


        }
    }

}