import React from "react";
import "./AgentOverview.scss";

function AgentOverview() {
  return (
    <div className="agent-overview-container">
      <div className="agent-overview overview1">
        <div className="header">
          <h3>
            <span className="material-icons wallet ">wallet</span>Total Agent
            Wallet
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h1>₹1,83,610.00</h1>
        </div>
      </div>
      <div className="agent-overview overview2">
        <div className="header">
          <h3>
            <span className="material-icons routes">route</span>Routes
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2> 17</h2>
        </div>
      </div>
      <div className="agent-overview overview3">
        <div className="header">
          <h3>
            <span className="material-icons groups">groups</span>Agents
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2>35</h2>
        </div>
      </div>
      <div className="agent-overview overview4">
        <div className="header">
          <h3>
            <span className="material-icons localActivity">local_activity</span>
            Least Customers
          </h3>
          {/* <span className="material-icons morehorizIcon">more_horiz</span> */}
        </div>
        <div className="content">
          <h2>4</h2>
        </div>
      </div>
    </div>
  );
}

export default AgentOverview;
