@import '/app/styles/variables.scss';

.purchase-order-create{
           .create-form-container{
            //width: 50dvw;
           }
            // .input-field-container{
            //     display: flex;
            //     flex-direction: row;
            //     justify-content: center;
            //     margin-bottom: 8px;
          
           
    
            //     .input-field{
            //         display: flex;
            //         flex-direction: column;
            //         margin-bottom: 4px;
                   
            //         label{
            //             font-size: 11px;
            //             color: $black_color2;
            //             font-weight: 600;
            //             margin-bottom: 5px;
            //         }
    
            //         input{
            //             padding: 8px;
            //             border: 2px solid $black_color4;
            //             border-radius: 6px;
                                           
            //            &::placeholder{
            //             color: $black_color4;
            //             font-size:11px;
            //            }
            //         }
    
            //         .input{
            //             padding: 0 8px 0 5px;
            //             border: 2px solid $black_color4;
            //             border-radius: 6px;
            //             background-color: #fff;
            //             // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
        
            //             select{      
            //                 width: 100%;        
            //                 font-size: 11px;
            //                 background-color: #fff;
            //                 color: $black_color4;
            //                 border: 0;
            //                 outline: none;
            //                 padding: 8px 0px;
            //             }
            //         }
            //     }
            // }

            // .items-table-container{
            //     padding-bottom: 20px;
            //     h5{
            //         font-weight: 600;
            //         color: #787988;
            //         font-size: 14px;
            //         margin-bottom:2px;
            //     }

            //     table{
            //         thead{
            //             tr{
            //                 th{
            //                     padding: 3px 5px;
            //                     font-size: 14px;
            //                     color: #3F3F3F;
            //                     font-weight: 700;
            //                     background-color: #D3D3D3;
            //                 }
            //             }
            //         }
            //         tbody{

            //             tr:nth-child(odd) { 
            //                 background-color: #F0F0F0; 
            //             }
            //             tr:nth-child(even) { 
            //                 background-color: #E8E8E8;
            //             }


            //             tr{
            //                 td{
                               
            //                     input{
            //                         height: 20px;
            //                         padding-left: 10px;
            //                         background: transparent;
            //                         border: 0;
            //                         &::placeholder{
            //                             font-size: 12px;
            //                             color: #D4D4D4;
                                        
            //                         }
            //                     }
            //                 }

            //                 .removeBtn{
            //                     display: flex;
            //                     justify-content: center;
            //                     align-items: center;
            //                     button{
            //                         cursor: pointer;
            //                         padding: 5px;
            //                         border: 0;
            //                         background: none;

            //                         span{
            //                             font-size: 15px;
            //                             color: $red_color;
                                    
            //                         }
            //                     }

                                
            //                 }
            //             }
            //         }
            //     }

            //     .addRowBtn{
            //         margin-top: 5px;
            //     }
            // }
    

              
    
            .name{
                gap: 13px;
            }
    
            .input-field-container-dotted{
                margin-bottom: 12px;
    
                button{
                    background-color: $white_color;
                    border: 2px dotted $black_color4;
                    padding: 10px 0;
                    font-size: 10px;
                    font-weight: 500;
                    color: $black_color3;
                    border-radius: 6px;
                }
            }
    
            .fileUpload{
                background-color: $white_color1;
                border: 2px dotted $black_color4;
                border-radius: 6px;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                padding: 1px 0 12px 0;
                margin-bottom: 30px;
    
                .uploadIcon{
                    color: $black_color2;
                    margin-bottom: 6px;
    
                    span{
                        font-size: 28px;
                        cursor: pointer;
                    }
                }
    
                .desc{
                    font-size: 9px;
                    color: $black_color;
                    font-weight: 600;
                    padding-bottom: 3px;
    
                    span{
                        color: $primary_color;
                        text-decoration: underline;
                        cursor: pointer;
    
                        &:hover{
                            color: darken($primary_color , 10%);
                        }
                    }
    
                }
    
                
                .fileFormat{
                    font-size: 8px;
                    color: $black_color3;
                    font-weight: 600;
                }
    
    
            }
    
            .SubmitBtn{
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
}


    

   