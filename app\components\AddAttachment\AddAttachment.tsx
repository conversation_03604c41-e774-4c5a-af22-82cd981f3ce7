import React, { useEffect, useState } from "react";
import "./AddAttachment.scss";
import FileUpload from "../utilities/Fileupload/FileUpload";

interface addAttachmentProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  onFileUpload: (uploadedFile: { file: File; label: string }) => void;
}

function AddAttachment({ showCreate, handleCloseCreate, onFileUpload }: addAttachmentProps) {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [label, setLabel] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  

  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png", "application/pdf"];

  const onFileChnage = (file: File | null) => {
    setUploadedFile(file)
  };

  const onSubmit = () =>{
    if (uploadedFile && label) {
      onFileUpload({ file: uploadedFile, label: label });
      handleCloseCreate();
    }else if(!label){
      setError("please give an label")
    }
  }

  useEffect(() => {
    if(!showCreate){
      setLabel("");
    }

  }, [showCreate])
  return (
    <div className={`add-attachment-container ${showCreate ? "show" : ""}`}>
      <div className="add-attachment-form">
        <h3>Add Attachment</h3>
        <span className="material-icons closeIcon" onClick={handleCloseCreate}>
          close
        </span>
        <form>
          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="label">Label</label>
              <input
                id="label"
                type="text"
                placeholder="Type..."
                value={label}
                onChange={(e) => setLabel(e.target.value)}
              />
            </div>
          </div>
          <FileUpload
            uploadedFile={uploadedFile}
            isOpen={showCreate}
            allowedTypes={ALLOWED_TYPES}
            maxSize={MAX_FILE_SIZE}
            onFileUpload={onFileChnage}
            label=""
            requiredMessage="Please upload a  ."
            maxFileSizeMessage="Profile image size is too large."
            invalidTypeMessage="Invalid Profile image type."
          />
          {error && <p className="error-message">{error}</p>}

          {/* <div className="uploaded-files">
            {uploadedFile && (
              <div className="file-item">
                <span>
                  <strong>File:</strong> {uploadedFile.file.name} | <strong>Label:</strong> {uploadedFile.label}
                </span>
                <button onClick={handleRemoveFile}>
                  <span className="material-icons">delete</span>
                </button>
              </div>
            )}
          </div> */}

          <div className="SubmitBtn">
            <button onClick={onSubmit} className="submitButton" type="button">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default AddAttachment;
