"use client";
import React, { useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import Modal from "react-modal";
import "./FileUpload.scss";
import { postAttachment } from "../../customer-service";
import { postOrderAttachments } from "@/app/pages/purchaseOrder/purchaseOrder-service";
import SmallLoader from "@/app/components/small-loader/SmallLoader";

// Set the modal's root
Modal.setAppElement("#__next");

interface UploadedFile {
  file: File | null;
  label: string | null;
  isLoading:boolean
}

interface Props {
  handleChange: (ids: number[]) => void;
  type: string;
  isOpen: boolean;
  handleLoadingChange:(state:boolean) => void
}



const MulitpleFileUpload = ({
  handleChange,
  type = "customer",
  isOpen,
  handleLoadingChange
}: Props) => {
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [label, setLabel] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [ids, setIds] = useState<number[]>([]);

  const openModal = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!label.trim()) {
      setError("Please enter a label before uploading files.");
      return;
    }
    setModalIsOpen(true);
    setError("");
  };

  const closeModal = () => {
    setModalIsOpen(false);
    setError("");
  };

  const onDrop = (acceptedFiles: File[]) => {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/webp",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ];
    let hasError = false;

    const fileWithLabel: UploadedFile[] = acceptedFiles
      .map((file) => {
        if (!allowedTypes.includes(file.type)) {
          setError(
            "Only PDF, JPG, PNG, WebP, XLS, and XLSX files are allowed."
          );
          hasError = true;
          return null;
        }
        if (file.size > maxSize) {
          setError("File size must not exceed 2MB.");
          hasError = true;
          return null;
        }
        return { file, label , isLoading: true };
      })
      .filter(Boolean) as UploadedFile[];

    if (hasError) return;
    fileWithLabel.forEach((file, index) => {
      setFiles((prev) => [...prev, file]);
      postFiles(file, files.length + index); // Pass index for error handling
    });
    setError("")
    closeModal();
    setLabel("");
  };

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
    setIds((prevIds) => prevIds.filter((_, i) => i !== index));
  };

  const postFiles = async (fileWithLabels: UploadedFile, fileIndex: number) => {
    console.log(fileWithLabels, "Uploading file with label");

    const formData = new FormData();
    if (fileWithLabels.file) {
      formData.append("uploded_file", fileWithLabels.file);
    }
    if (fileWithLabels.label) {
      formData.append("title", fileWithLabels.label);
    }

    try {
      const response =
        type === "customer"
          ? await postAttachment(formData)
          : await postOrderAttachments(formData);

      const responseId = response?.id;
      if (responseId) {
        setIds((prevIds) => [...prevIds, responseId]);
        setFiles((prevFiles) =>
          prevFiles.map((f, index) =>
            index === fileIndex ? { ...f, isLoading: false } : f
          )
        );
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (err) {
      console.error("Error uploading file:", err);
      setFiles((prevFiles) =>
        prevFiles.filter((_, index) => index !== fileIndex)
      );
      setError("Failed to upload file. Please try again.");
    }
  };

  useEffect(() => {
    handleChange(ids);
  }, [ids, handleChange]);

  useEffect(() => {
    if (!isOpen) {
      setFiles([]);
      setLabel("");
      setError("");
      setIds([]);
    }
  }, [isOpen]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: false,
  });

  useEffect(() => {
    const isAnyFileLoading = files.some((file) => file.isLoading);
    handleLoadingChange(isAnyFileLoading);
  }, [files, handleLoadingChange]);
  

  return (
    <>
      {files.length === 0 ? (
        <div className="input-field-insertButton">
          <div className="input-field w-50">
            <label htmlFor="label">File Name</label>
            <input
              id="label"
              type="text"
              placeholder="Type..."
              value={label}
              onChange={(e) => setLabel(e.target.value)}
            />
            {error && <p className="error-message">{error}</p>}
          </div>
          <div className="input-field w-50 addButton-container">
            <button className="closeButton" type="button" onClick={openModal}>
              Insert file
            </button>
          </div>
        </div>
      ) : (
        <div className="uploadedFiles-inputField">
          <h5>Attached files</h5>
          <div className="uploaded-files">
            <ul>
              {files.map(({ label , isLoading }, index) => (
                <li key={index} className="uploaded-file-item w-50">
                  <div className="file-info">
                    <span className="file-name">{label}</span>
                  </div>
                  {isLoading ? (
                   <SmallLoader isLoading={isLoading} />
                   ):(
                    <button onClick={() => removeFile(index)} className="remove-file" type="button">
                      <i className="fa-solid fa-trash"></i>
                    </button>
                   )
                  }
                </li>
              ))}
            </ul>
          </div>
          <div className="inputField w-100">
            <div className="input-field w-50">
              <label htmlFor="label">File Name</label>
              <input
                id="label"
                type="text"
                placeholder="Type..."
                value={label}
                onChange={(e) => setLabel(e.target.value)}
              />
              {error && <p className="error-message">{error}</p>}{" "}
              {/* Display error */}
            </div>
            <div className="input-field w-50 addButton-container">
              <button type="button" className="closeButton" onClick={openModal}>
                Insert file
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal for file upload */}
      <Modal
        isOpen={modalIsOpen}
        onRequestClose={closeModal}
        className="modal-content"
        overlayClassName="modal-overlay"
      >
        <h3 className="modal-header">Upload Media Files</h3>
        <div {...getRootProps()} className="fileUpload w-100">
          <input {...getInputProps()} />
          <div className="uploadIcon">
            <span className="material-icons">upload</span>
          </div>
          <div className="desc">
            Drag & Drop <span>Choose File</span> upload
          </div>
          <div className="fileFormat">PDF, JPG or PNG</div>
        </div>
        {error && <p className="error-message">{error}</p>}{" "}
        {/* Display error */}
        <div className="SubmitBtn">
          <button className="submitButton" onClick={closeModal}>
            Cancel
          </button>
        </div>
      </Modal>
    </>
  );
};

export default MulitpleFileUpload;
