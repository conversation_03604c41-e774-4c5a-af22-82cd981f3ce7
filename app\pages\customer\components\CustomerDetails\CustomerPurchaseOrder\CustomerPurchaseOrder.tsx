"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./customer-purchase-order.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
// import Link from "next/link";
import { getCustomerPurchaseOrder } from "../../../customer-service";
import TableWithShimmer from "@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer";
import { debounce } from "@mui/material";
import { customer_purchase_order } from "../../../customer.model";
import PurchaseOrderDetail from "./PurcaseOrderDetail/PurchaseOrderDetail";
import SearchBox from "@/app/components/SearchBox/SearchBox";

interface props {
  customerId: number | null;
  isOpen: boolean;
}

function CustomerPurchaseOrder({ customerId, isOpen }: props) {
  const [searchValue, setSearchValue] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [list, setList] = useState<customer_purchase_order[]>([]);

  const handlePageChange = (pageNo: number) => {
    console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue);
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(1, value, searchValue);
  };

  const toggleAdditionalDetails = (index: number) => {
    const updatedList = [...list];
    updatedList[index].is_detail_open = !updatedList[index].is_detail_open;
    setList(updatedList);
  };

  const getList = useCallback(
    async (pageNo: number, itemsPerPage: number, search: string) => {
      setIsTableLoading(true);
      const skip = (pageNo - 1) * itemsPerPage;
      try {
        const api = await getCustomerPurchaseOrder(
          skip,
          itemsPerPage,
          search,
          customerId
        );
        const data = api.results.map((item) => ({
          ...item,
          is_detail_open: false,
        }));
        setList(data);
        setPage(api.page);
        setTotalPages(api.total_pages);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [customerId] // Dependencies
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value),
    300
  );

  useEffect(() => {
    if (customerId && isOpen) {
      getList(1, 10, "");
    }
  }, [isOpen, getList, customerId]);

  const handleRest = () => {
    setSearchValue("");
    getList(1, itemsPerPage, "");
  };

  return (
    <div className="purchase-order-container">
      <div className="table-header-details-page">
        <div className="filter-search-container">
          <SearchBox
            value={searchValue}
            onChange={handleSearchChange}
            placeholders={[`Search "invoice number"`]}
          />
        </div>
      </div>

      <div className="table">
        <table>
          <thead>
            <tr>
              <th className="th-first">
                <div className="tableHead order">
                  <span className="heading">Order</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Total amount</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Advance amount</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">EMI type</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">EMI Amount</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {isTableLoading ? (
              <tr>
                <td colSpan={5}>
                  <TableWithShimmer
                    no_of_cols={5}
                    no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                  />
                </td>
              </tr>
            ) : list && list.length > 0 ? (
              list.map((item, index) => (
                <React.Fragment key={index}>
                  <tr
                    className="curser-pointer"
                    onClick={() => toggleAdditionalDetails(index)}
                  >
                    <td>
                      <div className="item">
                        {/* <div
                          className="checkBox"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <input type="checkbox" />
                        </div> */}
                        <div className="itemPicture">
                          {/* <Image src={""} alt=""></Image> */}
                        </div>
                        <div className="itemDetails">
                          <h5>{item.title}</h5>
                          <p>{item.invoice_no}</p>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className="totalAmount">₹{item.total_amount}</span>
                    </td>
                    <td>
                      <span className="category">₹{item.advance_amount}</span>
                    </td>
                    <td>
                      <span className="discount">{item.emi_type}</span>
                    </td>
                    <td>
                      <span className="emiAmount">₹{item.emi_amount}</span>
                    </td>
                  </tr>

                  {/* <tr className={`${customer.isPopupOpen ?'showData' : 'hideData'} `}> */}
                  <td colSpan={5}>
                    <PurchaseOrderDetail
                      handleClose={() => toggleAdditionalDetails(index)}
                      isOpen={item.is_detail_open}
                      purchase_order_id={item.id}
                    />
                  </td>
                  {/* </tr>  */}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="no-data">
                  {searchValue
                    ? `No Purchase Order matches your search for '${searchValue}'.`
                    : `It looks like you don't have any Purchase Orders for this customer yet.`}
                  {searchValue && (
                    <button
                      onClick={handleRest}
                      style={{ marginLeft: "auto", marginRight: "auto" }}
                      className="submitButton"
                    >
                      <span className="material-icons">restart_alt</span>Reset
                    </button>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {list && list.length > 0 && (
        <div className="pagination-table-container">
          <Pagination
            totalPages={totalPages}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={page}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
}

export default CustomerPurchaseOrder;
