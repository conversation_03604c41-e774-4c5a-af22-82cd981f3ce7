export interface Branch_List{
    links:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: Branch_List_Item[]
}

export interface Branch_List_Item{
    name: string;
    address: string;
    email: string;
    phone_number: string;
    code: string;
    id: number;
    file: File | null;
    qr_code: string | null;
    is_active: boolean;
}

export interface branch_create{
    code:string
    name:string
    address:string
    email:string
    phone_number:string
    qr_code:File | null
}