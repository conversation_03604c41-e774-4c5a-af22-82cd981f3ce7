"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./purchaseOrder.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer";
import OrderCreate from "./components/orderCreate/OrderCreate";
import { fetchPurchaseOrderOverviewList } from "./purchaseOrder-service";
import { Purchase_Order_Overview_List_Item } from "./purchaseOrder.model";
import DateTimeDisplay from "@/app/components/utilities/DateTimeDisplay/DateTimeDisplay";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [purchaseOrderOverviewList, setPurchaseOrderOverviewList] = useState<
    Purchase_Order_Overview_List_Item[]
  >([]);
  const [visible, setVisible] = useState<boolean>(false);
  const [isTableLoading, setIsTableLoading] = useState<boolean>();
  // const moreFilterRef = useRef(null);
  //filters
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPageNo: number,
      searchKey: string,
      date: string
    ) => {
      const skip = (pageNo - 1) * itemsPerPageNo;
      setIsTableLoading(true);

      try {
        const response = await fetchPurchaseOrderOverviewList(
          skip,
          itemsPerPageNo,
          searchKey,
          date
        );
        setPurchaseOrderOverviewList(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setIsTableLoading(false);
      }
    },
    [setIsTableLoading]
  );

  useEffect(() => {
    getList(1, 10, "", "");
  }, [getList]);

  const handleReset = () => {
    setSearchValue("");
    setSelectedDate("");
    getList(1, itemsPerPage, "", "");
  };

  // useEffect(() => {
  //   console.log(isSearchBoxVisible); // This will log the updated state
  // }, [isSearchBoxVisible]);

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo,"page changed");
    getList(pageNo, itemsPerPage, searchValue, selectedDate);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, selectedDate);
  };

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(currentPage, itemsPerPage, searchValue, selectedDate);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const date = event.target.value;

    if (item === "date") {
      setSelectedDate(event.target.value);
      getList(1, itemsPerPage, searchValue, date);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, selectedDate),
    300
  );

  return (
    <>
      <div
        className={`purchaseOrderOverviewList overall-list-padding ${
          visible ? "visible" : ""
        }`}
      >
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add New Order +
          </div>
        </div>

        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>
                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <input
                    className="date-picker2"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                  />
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[`Search "name"`, `Search "invoice number"`]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={9}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Date & Time</th>
                      <th>Customer Name</th>
                      <th>Total Amount</th>
                      <th>Branch</th>
                      <th>Advance Amount</th>
                      <th>EMI Type</th>
                      <th>EMI Amount</th>
                      <th>EMI Period</th>
                      <th>EMI Period Type</th>
                    </tr>
                  </thead>

                  <tbody>
                    {purchaseOrderOverviewList &&
                    purchaseOrderOverviewList.length > 0 ? (
                      purchaseOrderOverviewList.map((data, index) => (
                        <tr key={index}>
                          <td>
                            <div className="date-invoiceNumber">
                              <DateTimeDisplay
                                created_at={data.created_at}
                                pageName=""
                              />
                              <p className="invoiceNumber">{data.invoice_no}</p>
                            </div>
                          </td>

                          <td>
                            <div className="customerName">
                              <p className="name">{data.customer_name}</p>

                              <p className="careOf">
                                {data.short_name} {data.care_of ? 'C/O ' + data.care_of : ''}
                              </p>
                              {/* <p className="careOf">സലാം ഗൾഫ് C/O ഫിറോസ് കോട്ടക്കൽ</p> */}
                            </div>
                          </td>

                          <td>
                            <p className="amount">{data.total_amount}</p>
                          </td>

                          <td>
                            <p className="branchName">{data.branch_name}</p>
                          </td>

                          <td>
                            <p className="amount">{data.advance_amount}</p>
                          </td>

                          <td>
                            <p className="emiType">{data.emi_type}</p>
                          </td>

                          <td>
                            <p className="emiAmount">{data.emi_amount}</p>
                          </td>

                          <td>
                            <p className="emiPeriod">{data.emi_period}</p>
                          </td>

                          <td>
                            <p className="emiType">{data.emi_period_type}</p>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={9} className="no-data2">
                          <h5>
                            {selectedDate &&
                              !searchValue &&
                              `There are no orders with the selected date "${selectedDate}".`}
                            {searchValue &&
                              !selectedDate &&
                              `No orders match your search for "${searchValue}".`}
                            {!searchValue &&
                              !selectedDate &&
                              "It looks like you don't have any orders yet."}
                            {searchValue &&
                              selectedDate &&
                              `No orders with the date "${selectedDate}" match your search for "${searchValue}".`}
                          </h5>
                          {(selectedDate || searchValue) && (
                            <button
                              onClick={handleReset}
                              style={{
                                marginLeft: "auto",
                                marginRight: "auto",
                              }}
                              className="submitButton"
                            >
                              <span className="material-icons">
                                restart_alt
                              </span>
                              Reset
                            </button>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {purchaseOrderOverviewList &&
              purchaseOrderOverviewList?.length > 0 && (
                <Pagination
                  totalPages={totalPages}
                  handlePage={handlePageChange}
                  itemsPerPage={itemsPerPage}
                  page={currentPage}
                  handleItemsPerPageChange={handleItemsPerPageChange}
                />
              )}
          </div>
        </div>
      </div>
      <OrderCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
      />
    </>
  );
}

export default Page;
