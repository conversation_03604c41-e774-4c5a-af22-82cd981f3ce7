import React, { useEffect, useState } from "react";
import styles from "./SearchBox.module.scss"; // Import SCSS styles

interface SearchBoxProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholders: string[]; // Parent will pass multiple placeholders
}

const SearchBox: React.FC<SearchBoxProps> = ({
  value,
  onChange,
  placeholders,
}) => {
  const [currentPlaceholder, setCurrentPlaceholder] = useState(placeholders[0]);
  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (placeholders.length > 1) {
      const interval = setInterval(() => {
        setIsTransitioning(true);
        setTimeout(() => {
          setPlaceholderIndex(
            (prevIndex) => (prevIndex + 1) % placeholders.length
          );
          setIsTransitioning(false);
        }, 500); // Animation duration
      }, 3000); // Change placeholder every 3s

      return () => clearInterval(interval);
    }
  }, [placeholders]);

  useEffect(() => {
    setCurrentPlaceholder(placeholders[placeholderIndex]);
  }, [placeholderIndex, placeholders]);

  return (
    <div className={`${styles.searchBox}`}>
      <span className={`material-icons ${styles.searchIcon} `}>search</span>
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={currentPlaceholder}
        className={isTransitioning ? styles.fadeOut : styles.fadeIn}
      />
    </div>
  );
};

export default SearchBox;
