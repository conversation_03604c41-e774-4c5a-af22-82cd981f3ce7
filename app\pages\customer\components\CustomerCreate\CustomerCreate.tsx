"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./CustomerCreate.scss";
import { useForm, SubmitHand<PERSON>, Controller } from "react-hook-form";
import { customer_create } from "../../customer.model";
import { createCustomer } from "../../customer-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import MulitpleFileUpload from "../FileUpload/MulitpleFileUpload";
import FileUpload from "@/app/components/utilities/Fileupload/FileUpload";
import { Autocomplete, TextField } from "@mui/material";
import { Branch } from "@/app/pages/users/users.model";
import { fetchBranchList } from "@/app/pages/users/users-service";
import { useCommonContext } from "@/app/contexts/commonContext";

interface customerCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
}

function CustomerCreate({
  showCreate,
  handleCloseCreate,
}: customerCreateProps) {
  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png"];
  const { fire } = useAlert();
  const [options, setOptions] = useState<Branch[]>([]);
  const { setIsLoading } = useCommonContext();
  const {
    register,
    control,
    getValues,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
    setValue,
  } = useForm<customer_create>();
  const [uploadFile, setUploadFile] = useState<string | File | null>(null);
  const [isFileUploading,setIsFileUploading] = useState<boolean>(false);

  const getBranchList = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await fetchBranchList();
      setOptions(res.results);

      if (!getValues("branch_id")) {
        const initialValue = res?.results[0]?.id;
        if (initialValue) {
          setValue("branch_id", initialValue);
        }
      }
    } catch (err) {
      console.error("error", err);
      fire({
        position: "center",
        icon: "error",
        title: "Some error occured",
        text: "Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
    } finally {
      setIsLoading(false);
    }
  }, [getValues, setValue, fire, setIsLoading]);

  useEffect(() => {
    getBranchList();
  }, [getBranchList]);

  const handleFileLoading = (state:boolean) => {
    setIsFileUploading(state);
  }

  const onSubmit: SubmitHandler<customer_create> = async (data) => {
    if(isFileUploading){
      fire({
        position: "center",
        icon: "error",
        title: "File is uploading",
        text: "Please wait until the file is uploaded.",
        confirmButtonText: "Ok",
        });
    }else{
      setIsLoading(true)
      const formData = new FormData();
      formData.append("name", data.name);
      formData.append("short_title", data.short_title);
      formData.append("city", data.city);
      formData.append("care_of", data.care_of);
      formData.append("address", data.address);
      formData.append("pincode", data.pincode.toString());
      formData.append("son_of", data.son_of);
      formData.append("code", data.code);
      formData.append("phone_number", data.phone_number);
      formData.append("whatsapp_number", data.whatsapp_number);
      formData.append("email", data.email);
      formData.append("phone_number2", data.phone_number2);
      formData.append("branch_id", String(data.branch_id));
      formData.append("credit_score", '500');
      if (data.profile_photo) {
        formData.append("profile_photo", data.profile_photo);
      }
      // if (data.attachments && Array.isArray(data.attachments)) {
      //   formData.append('attachment_ids', JSON.stringify(data.attachments));
      // }
  
      if (data.attachments && Array.isArray(data.attachments)) {
        data.attachments.forEach((id: number) => {
          formData.append("attachment_ids", id.toString()); // Send each ID separately
        });
      }
  
      createCustomer(formData)
        .then((res) => {
          if (res) {
            handleCloseCreate();
            //console.log(res);
            setIsLoading(false)
            fire({
              position: "top-right",
              icon: "success",
              title: "Created Successfully",
              autoClose: 2000,
            });
          }
        })
        .catch((error) => {
          setIsLoading(false)
          fire({
            position: "center",
            icon: "error",
            title: "Some error occured",
            text:
              error?.response?.data?.detail ||
              error?.message ||
              "An Error Occured Please Try Again",
            confirmButtonText: "Ok",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
        });      
    }

  };

  const handleFileUploadChange = (ids: number[]) => {
    setValue("attachments", ids);
    //console.log("Updated files:", ids);
  };

  const onFileUpload = (file: File | null) => {
    setUploadFile(file);
    setValue("profile_photo", file); // Update form value
  };

  useEffect(() => {
    if (!showCreate) {
      reset();
    }
  }, [showCreate, reset]);

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Add Customer</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="cName">Customer Name</label>
                <input
                  id="cName"
                  {...register("name", {
                    required: "Please enter the customer's name.",
                  })}
                  type="text"
                  placeholder="Type..."
                />
                {<p className="error-message">{errors.name?.message}</p>}
              </div>
              <div className="input-field wf-50">
                <label htmlFor="code">Customer Code</label>
                <input
                  {...register("code")}
                  id="code"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.code?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="sName">Short Name</label>
                <input
                  {...register("short_title", {
                    required: "Please enter a short name for the customer.",
                  })}
                  id="sName"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.short_title?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="s/o">S/o D/o W/o</label>
                <input
                  {...register("son_of")}
                  id="s/o"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.son_of?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="c/o">C/o</label>
                <input
                  {...register("care_of")}
                  id="c/o"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.care_of?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="email">Email</label>
                <input
                  {...register("email")}
                  id="email"
                  type="email"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.email?.message}</p>
              </div>
              <div className="input-field wf-100">
                <label htmlFor="address">Address</label>
                <input
                  {...register("address", {
                    required: "Please provide the customer's address.",
                  })}
                  id="address"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.address?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="city">City</label>
                <input
                  {...register("city")}
                  id="city"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.city?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="pin">Pin</label>
                <input
                  {...register("pincode", {
                    maxLength: {
                      value: 6,
                      message: "Pin Code cannot exceed 6 characters.",
                    },
                  })}
                  id="pin"
                  type="number"
                  placeholder="Type..."
                  max={999999} // Limits the number to 6 digits
                  onInput={(e) => {
                    if (e.currentTarget.value.length > 6) {
                      e.currentTarget.value = e.currentTarget.value.slice(0, 6);
                    }
                  }}
                />

                <p className="error-message">{errors.pincode?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="primaryContact">Primary Contact</label>
                <input
                  {...register("phone_number", {
                    required:
                      " Please enter the primary contact number for the customer. ",
                  })}
                  id="whatsappContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone_number?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="whatsappContact">Whatsapp Contact</label>
                <input
                  {...register("whatsapp_number", {
                    required:
                      "Please provide the customer's WhatsApp contact number. ",
                  })}
                  id="whatsappContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">
                  {errors.whatsapp_number?.message}
                </p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="SecondaryContact">Secondary Contact</label>
                <input
                  {...register("phone_number2")}
                  id="SecondaryContact"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone_number2?.message}</p>
              </div>

              <div className="select-input-field wf-100">
                <label htmlFor="branch">Branch</label>

                <Controller
                  name="branch_id"
                  control={control}
                  rules={{ required: "Please select a branch." }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="branch"
                      options={options}
                      getOptionLabel={(option) => option?.name}
                      value={
                        options.length > 0
                          ? options.find(
                              (option) => option.id === getValues("branch_id")
                            )
                          : null
                      } // Set initial value
                      onChange={(_, value) => {
                        setValue("branch_id", value?.id ?? 0);
                      }}
                      // Update selected branch or default to first option
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Branch"
                          error={!!errors?.branch_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />

                {/* Show error message if the field is empty and not selected */}
                {errors?.branch_id && (
                  <p className="error-message" style={{ color: "#f44336" }}>
                    {errors?.branch_id?.message}
                  </p>
                )}
              </div>

              <div className="wf-100">
                <MulitpleFileUpload
                  handleLoadingChange={handleFileLoading}
                  type="customer"
                  isOpen={showCreate}
                  handleChange={handleFileUploadChange}
                />
              </div>
              <div className="wf-100">
                <FileUpload
                  uploadedFile={uploadFile}
                  isOpen={showCreate}
                  allowedTypes={ALLOWED_TYPES}
                  maxSize={MAX_FILE_SIZE}
                  onFileUpload={onFileUpload}
                  label="Profile Image"
                  requiredMessage="Please upload a Profile Image."
                  maxFileSizeMessage="Profile image size is too large."
                  invalidTypeMessage="Invalid Profile image type."
                />
                {errors.profile_photo && (
                  <p className="error-message">
                    {errors.profile_photo.message}
                  </p>
                )}
              </div>
            </div>

            {/* <div className="input-field-container w-100 addMoreField">
   <p>+ Add More Address</p>
  </div> */}

            {/* <div className="input-field-container w-100 addMoreField">
   <p>+ Add More Contact</p>
  </div> */}
            {/* <div className="input-field-container-dotted w-100">
    <button className='w-100'>
    + Add More Contact
    </button>
   </div> */}
            {/* <div className="fileUpload w-100">
     <div className="uploadIcon">
     <span className="material-icons">
  upload
  </span>
  
     </div>
     <div className="desc">
       Drag & Drop <span>Choose File</span> upload
     </div>
     <div className="fileFormat">
       PDF, JPG or PNG
     </div>
  
   </div> */}

            <div style={{ marginTop: "0px" }} className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CustomerCreate;
