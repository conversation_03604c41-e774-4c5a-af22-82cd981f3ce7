import React, { useCallback, useEffect, useState } from "react";
import "./agent-emi-collection.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import { debounce } from "@mui/material";
import { getAgentEmiCollection } from "../../../agent-service";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import { agentCollectionOverview } from "../../../agent.model";
import EditEmiCollection from "@/app/pages/emiCollection/components/EditEmiCollection";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { useCommonContext } from "@/app/contexts/commonContext";

interface props {
  agentId: number | null;
  isOpen: boolean;
}
interface filter {
  date: string;
  mode_of_transaction: string;
  status: string;
}
function AgentEmiCollection({ agentId }: props) {
  const [searchValue, setSearchValue] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [list, setList] = useState<agentCollectionOverview[]>([]);
  const [selectedEmi, setSelectedEmi] = useState<number | null>(null);
  const [isEditEmi, setIsEditEmi] = useState<boolean>(false);
  const [filters, setFilter] = useState<filter>({
    date: "",
    mode_of_transaction: "",
    status: "",
  });
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const { maxDate } = useCommonContext()
  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handlePageChange = (pageNo: number) => {
    getList(
      pageNo,
      itemsPerPage,
      searchValue,
      filters.status,
      filters?.mode_of_transaction,
      filters?.date
    );
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(
      1,
      value,
      searchValue,
      filters.status,
      filters.mode_of_transaction,
      filters.date
    );
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPage: number,
      search: string,
      status: string,
      paymentMode: string,
      date: string
    ) => {
      setIsTableLoading(true);
      const skip = (pageNo - 1) * itemsPerPage;
      try {
        const api = await getAgentEmiCollection(
          agentId,
          skip,
          itemsPerPage,
          search,
          status,
          paymentMode,
          date
        );
        let data = api.results;
        data = data.map((item) => ({
          ...item,
          is_detail_open: false,
        }));
        setList(data);
        setPage(api.page);
        setTotalPages(api.total_pages);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [agentId]
  );

  const handleFilter = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    filterKey: keyof filter
  ) => {
    const value = event.target.value;
    setFilter((prevFilters) => {
      const updatedFilters = { ...prevFilters, [filterKey]: value };
      getList(
        page,
        itemsPerPage,
        searchValue,
        updatedFilters.status,
        updatedFilters.mode_of_transaction,
        updatedFilters.date
      );
      return updatedFilters;
    });
  };

  const handleReset = () => {
    setSearchValue("");
    setFilter({} as filter);
    getList(1, itemsPerPage, "", "", "", "");
  };

  const handleCloseEdit = () => {
    setIsEditEmi(false);
    getList(
      page,
      itemsPerPage,
      searchValue,
      filters.status,
      filters.mode_of_transaction,
      filters.date
    );
  };

  const handleEdit = (id: number) => {
    setSelectedEmi(id);
    setIsEditEmi(true);
  };

  useEffect(() => {
    if (typeof agentId === "number" && !isNaN(agentId)) {
      getList(1, 10, "", "", "", "");
    }
  }, [getList, agentId]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) =>
      getList(
        1,
        itemsPerPage,
        value,
        filters.status,
        filters.mode_of_transaction,
        filters.date
      ),
    300
  );

  return (
    <div className="emi-collection-history-container">
      <div className="table-header-details-page" style={{ height: "60px" }}>
        <div className="filter-search-container">
          <div className="filterButton" onClick={handleToggleFilter}>
            <button>
              <i className="fa-solid fa-filter"></i>
            </button>
          </div>

          <div
            className={`filter-options-select-box ${
              isFilterOpen ? "show" : ""
            }`}
          >
            <div className="filterOption">
              <span>
                Payment Mode{" "}
                {filters?.mode_of_transaction
                  ? `(${filters.mode_of_transaction})`
                  : ""}
              </span>
              <select
                className="dropdown"
                value={filters?.mode_of_transaction}
                onChange={(e) => handleFilter(e, "mode_of_transaction")}
              >
                <option value="">None</option>
                <option value="cash">Cash</option>
                <option value="bank">Bank</option>
                <option value="unavailable">Unavailable</option>
                {/* <option value="due">Due</option> */}
              </select>
              <span className="material-icons">keyboard_arrow_down</span>
            </div>
            <div className="filterOption">
              <span>Status {filters?.status ? `(${filters.status})` : ""}</span>
              <select
                className="dropdown"
                value={filters?.status}
                onChange={(e) => handleFilter(e, "status")}
              >
                <option value="">None</option>
                <option value="inprogress">InProgress</option>
                <option value="failed">Failed</option>
                <option value="success">Success</option>
              </select>
              <span className="material-icons">keyboard_arrow_down</span>
            </div>
            <input
              className="date-picker"
              type="date"
              max={maxDate}
              value={filters?.date || ''}
              onChange={(e) => handleFilter(e, "date")}
            />
          </div>

          <SearchBox
            value={searchValue}
            onChange={handleSearchChange}
            placeholders={[
              `Search "reference number"`,
              `Search "name"`,
              `Search "transaction id"`,
            ]}
          />
        </div>
      </div>

      <div className="table2">
        <table>
          <thead>
            <tr>
              <th className="th-first">
                <div className="tableHead">
                  <span className="heading">Date & Time</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Amount</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Status</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Customer Name</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Mode of transaction</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-mid">
                <div className="tableHead">
                  <span className="heading">Remark</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              <th className="th-last">
                <div className="tableHeadLast">
                  <span className="heading">Actions</span>
                  <span className="material-icons arrowdown">
                    keyboard_arrow_down
                  </span>
                </div>
              </th>
              {/* <th className="th-last">
         <div className="tableHead">
         <span className="heading">Action</span>
         <span className="material-icons arrowdown">keyboard_arrow_down</span>
         </div>
       </th> */}
            </tr>
          </thead>
          <tbody>
            {isTableLoading ? (
              <tr>
                <td colSpan={7}>
                  <TableWithShimmer
                    no_of_cols={7}
                    no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                  />
                </td>
              </tr>
            ) : list.length > 0 ? (
              list.map((data, index) => (
                <tr key={index}>
                  <td>
                    <div className="date-time-invoiceNumber">
                      <div className="dateAndTime">
                        <span className="date">
                          {new Date(data.created_at).toLocaleDateString()}
                        </span>
                        <span className="time">
                          {new Date(data.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="invoiceNumber">{data.transaction_id}</p>
                    </div>
                  </td>
                  <td
                    className={`${
                      data.transaction_mode == "Cash" ? "cash" : "gpay"
                    }`}
                  >
                    {data.emi_amount}
                  </td>
                  <td>
                    <div className="customerName">
                      <div className={`status ${data.status} `}>
                        {data.status}
                      </div>
                    </div>
                  </td>
                  <td className="customerName">
                    {data.customer_name} <br />
                    <div style={{ fontSize: "8px" }}>
                      {data.short_title}
                      {data.care_of ? `C/O ${data.care_of}` : ""}
                    </div>
                  </td>
                  <td>
                    <div className="modeOfTransaction">
                      <div className="mode">{data.transaction_mode}</div>
                      {data.transaction_mode === "bank" && (
                        <div className="transactionId">
                          {" "}
                          {data.payment_reference_no}
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="remark">
                    {data.remark ? data.remark : "N/A"}
                  </td>
                  <td className="table-actions">
                    <div
                      className="material-icons"
                      onClick={() => handleEdit(data.id)}
                    >
                      edit
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="no-data-emiCollectionHistory">
                  <h5>
                    {(() => {
                      const messages = [];

                      // Status filter
                      if (filters.status) {
                        messages.push(`status "${filters.status}"`);
                      }

                      // Search value filter
                      if (searchValue) {
                        messages.push(`search term "${searchValue}"`);
                      }

                      // Payment mode filter
                      if (filters.mode_of_transaction) {
                        messages.push(
                          `payment mode "${filters.mode_of_transaction}"`
                        );
                      }

                      // Date filter
                      if (filters.date) {
                        messages.push(
                          `date "${new Date(
                            filters.date
                          ).toLocaleDateString()}"`
                        );
                      }

                      // Combine messages
                      if (messages.length > 0) {
                        return `No Collection History found matching ${messages.join(
                          ", "
                        )}.`;
                      }

                      // Default message when no filters are applied
                      return "It looks like you don't have any Collection History yet.";
                    })()}
                  </h5>
                  {(searchValue ||
                    filters.status ||
                    filters.mode_of_transaction ||
                    filters.date) && (
                    <button
                      onClick={handleReset}
                      style={{ marginLeft: "auto", marginRight: "auto" }}
                      className="submitButton"
                    >
                      <span className="material-icons">restart_alt</span>Reset
                    </button>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {list && list.length > 0 && (
        <div className="pagination-table-container">
          <Pagination
            totalPages={totalPages}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={page}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
      <EditEmiCollection
        id={selectedEmi}
        handleCloseCreate={handleCloseEdit}
        showCreate={isEditEmi}
      />
    </div>
  );
}

export default AgentEmiCollection;
