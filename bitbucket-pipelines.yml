# image: node:20

# pipelines:
#   branches:
#     master:
#     - step:
#         name: "Setup & Deploy"
#         script:
#           - echo "🔍 Decoding SSH Key..."
#           - mkdir -p ~/.ssh
#           - echo "$SSH_PRIVATE_KEY_B64" | base64 -d > ~/.ssh/id_rsa
#           - chmod 600 ~/.ssh/id_rsa
#           - eval "$(ssh-agent -s)"
#           - ssh-add ~/.ssh/id_rsa || echo "❌ SSH Key failed to load!"

#           # Debugging: Show first 5 lines of decoded key
#           - head -n 5 ~/.ssh/id_rsa
          
#           # Check key format
#           - ssh-keygen -y -f ~/.ssh/id_rsa || echo "❌ Invalid SSH Key Format!"

#           # Verify SSH connection (replace with your actual server)
#           - |
#             ssh -o StrictHostKeyChecking=no root@************** << 'EOF'
#               echo "✅ SSH Connection Successful!"
#               cd apps/frontend/choice-emi-frontend/ || { echo "❌ Directory not found!"; exit 1; }
#               git pull origin master || { echo "❌ Git Pull Failed!"; exit 1; }
#               npm i || { echo "❌ NPM Install Failed!"; exit 1; }
#               npm run build || { echo "❌ Build Failed!"; exit 1; }
#               pm2 restart all || { echo "❌ PM2 Restart Failed!"; exit 1; }
#               echo "✅ Deployment Completed Successfully!"
#             EOF
