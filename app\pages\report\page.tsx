"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./report.scss";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import { useCommonContext } from "@/app/contexts/commonContext";
import { report } from "./report.model";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { getReportList } from "./report-service";
import { debounce } from "@mui/material";

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(10);
  const [reportList, setReportList] = useState<report[]>([]);
  const [selectedReportItem, setSelectedReportItem] = useState<string>("top_purchasing_customer");
  const [visible, setVisible] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const { isLoading, setIsLoading } = useCommonContext();

  const reportTypeLabels: Record<string, string> = {
    top_purchasing_customer: "Top Purchasing Customer",
    timely_emi: "Timely Emi",
    closed_emi: "Closed Emi",
    least_purchasing_customer: "Least Purchasing Customer",
  };
  //fetch reportlist
  const getList = useCallback(
    async (
      pageNo: number,
      itemsInPage: number,
      search: string,
      report_type: string
    ) => {
      const skip = (pageNo - 1) * itemsInPage;
      try {
        setIsLoading(true);
        const response = await getReportList(
          skip,
          itemsInPage,
          search,
          report_type
        );
        setReportList(response.results);
        setCurrentPage(response.page);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading]
  );

  useEffect(() => {
    getList(1, 10, "", "top_purchasing_customer");
  }, [getList]);

  const handlePageChange = (pageNo: number) => {
    console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue, selectedReportItem);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, selectedReportItem);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, selectedReportItem),
    300
  );

  const handleReportTypeChange = (value: string) => {
    setSelectedReportItem(value);
    getList(1, itemsPerPage, searchValue, value);
  };

  return (
    <div
      className={`reportList overall-list-padding ${visible ? "visible" : ""}`}
    >
      <div className="user-list-table-container">
        <div className="tableBorder">
          <div className="table-header">
            <div className="filter-search-container">
              <div className="filter-options-select-box">
                <div className="filterOption">
                  <span>Filter {selectedReportItem ? `(${reportTypeLabels[selectedReportItem]})` : ""}</span>
                  <select className="dropdown" onChange={(e) => handleReportTypeChange(e.target.value)}>
                    {Object.entries(reportTypeLabels).map(([value, label]) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                  <span className="material-icons">keyboard_arrow_down</span>
                </div>
              </div>

              <SearchBox
                value={searchValue}
                onChange={handleSearchChange}
                placeholders={[`Search`]}
              />

              {/* <button className="downloadButton">
                <span className="material-icons downloadIcon">download</span>
              </button> */}
            </div>
          </div>

          {isLoading ? (
            <TableWithShimmer
              no_of_rows={8}
              no_of_cols={6}
              colWidths={[1.5, 1.5, 1, 1]}
            />
          ) : (
            <div className="table-style table-vertical-scroll-fullHeight">
              <table>
                <thead>
                  <tr>
                    <th>SI No</th>
                    <th>Customer Name</th>
                    <th>Emi Type</th>
                    <th>Purchase Orders Count</th>
                    <th>Branches</th>
                    <th>Purchase date</th>
                  </tr>
                </thead>

                <tbody>
                  {reportList && reportList.length > 0 ? (
                    reportList.map((data, index) => (
                      <tr key={index}>
                        <td>{(currentPage - 1) * itemsPerPage + index + 1}</td>
                        <td>{data.customer_name}</td>
                        <td>{data.EMI_type ? data.EMI_type : 'N/A'}</td>
                        <td>{data.purchased_order_count ? data.purchased_order_count : 'N/A'}</td>
                        <td>{data.branch_name ? data.branch_name : 'N/A'}</td>
                        <td>{data.purchase_date ? (new Date(data?.purchase_date ?? "").toLocaleDateString()) : 'N/A'}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="no-data-emiCollection">
                        <h5>
                          {!searchValue
                            ? `There is no data available for the "${reportTypeLabels[selectedReportItem]}" report.`
                            : `No customers in the "${reportTypeLabels[selectedReportItem]}" report match your search for "${searchValue}".`}
                        </h5>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}

          {reportList && reportList.length>0 && 
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />         
          }
        </div>
      </div>
    </div>
  );
}

export default Page;
