"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import "./CustomerDetails.scss";
import Image from "next/image";
import pdfIcon from "../../../../../public/images/pdf.png";
import dummyProfile from "../../../../../public/images/profile.png";
import AddAddress from "@/app/components/AddAddress/AddAddress";
import AddAttachment from "@/app/components/AddAttachment/AddAttachment";
import CustomerCollectionHistory from "./CustomerCollection/CustomerCollectionHistory";
import {
  addAttachment,
  deleteAddress,
  deleteCustomerAttachment,
  getCustomerDetails,
} from "../../customer-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import CustomerEminNote from "./CustomerEmiNote/CustomerEminNote";
import CustomerPurchaseOrder from "./CustomerPurchaseOrder/CustomerPurchaseOrder";
import { customer_details } from "../../customer.model";
import RatingComponent from "@/app/components/Rating/Rating";
import EditCustomer from "../edit-customer/EditCustomer";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import TableMenuTwo, {
  DropdownItem,
} from "@/app/components/TableMenuTwo/TableMenuTwo";
import { useRouter } from "next/navigation";
import { AxiosError } from "axios";
import { getOrdinalSuffix } from "@/app/components/utilities/Pipes/getOrdinalSuffix";

interface customerDetailsProps {
  showDetails: boolean;
  handleClose: VoidFunction;
  customerId: number | null;
}

interface ErrorResponseData {
  detail?: string;
}

function CustomerDetails({
  showDetails,
  customerId,
  handleClose,
}: customerDetailsProps) {
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;
  const { fire } = useAlert();
  const route = useRouter();
  const { setIsLoading } = useCommonContext();
  const [customerDetails, setCustomerDetails] = useState<customer_details>();
  const [activeMenuIndex, setActiveMenuIndex] = useState<number>(0);
  const [isMorePhoneActive, setIsMorePhoneActive] = useState<boolean>(false);
  const [isMoreAddressActive, setIsMoreAddressActive] =
    useState<boolean>(false);
  const [isEditOpen, setIsEditOpen] = useState<boolean>(false);
  const [showAddAddress, setShowAddAddress] = useState<boolean>(false);
  const [showAddAttachment, setShowAddAttachment] = useState<boolean>(false);
  const [uploadedFiles, setUploadedFiles] = useState<{
    file: File | null;
    label: string;
  }>();
  const [paidAmount, setPaidAmount] = useState<number>();
  const [selectedAddress, setSelectedAddress] = useState<number | null>(null);
  const detailContainerRef = useRef<HTMLDivElement | null>(null);
  const addressMenuItem = [
    { id: "edit", label: "Edit Address" },
    { id: "delete", label: "Delete Address" },
  ];

  const handleMennuItemClick = (item: DropdownItem, id: number) => {
    setSelectedAddress(id);
    if (item.id === "delete") {
      fire({
        position: "center",
        icon: "info",
        title: "Are you sure?",
        text: "This action will permanently delete the address.",
        confirmButtonText: "Ok",
        cancelButtonText: "Cancel",
        onConfirm: () => {
          setIsLoading(true);
          deleteAddress(id)
            .then((res) => {
              console.log(res);

              getList();
              fire({
                position: "top-right",
                icon: "success",
                title: "Deleted successfully",
                autoClose:2000
              });
            })
            .catch((error) => {
              console.log(error, "error");
              setIsLoading(false);
            });
        },
      });
    } else if (item.id === "edit") {
      setSelectedAddress(id);
      setShowAddAddress(true);
    }
  };
  const handleFileUpload = (uploadedFile: { file: File; label: string }) => {
    setUploadedFiles(uploadedFile);
  };

  const handleAddAddress = () => {
    setShowAddAddress(true);
  };

  const handleCloseAddAddress = () => {
    setSelectedAddress(null);
    setShowAddAddress(false);
    getList();
  };

  const handleAddAttachment = () => {
    setShowAddAttachment(true);
  };

  const handleCloseAddAttachment = () => {
    setShowAddAttachment(false);
  };

  const handleDetailOverFlow = (state:boolean) => {
    if(detailContainerRef.current){
      if(state){
        detailContainerRef.current.style.overflow = 'hidden'
      }else{
        detailContainerRef.current.style.overflowY = 'auto'
      }
    }
  }

  useEffect(()=>{
   if(isEditOpen || showAddAddress || showAddAttachment){
    handleDetailOverFlow(true)
   }else{
    handleDetailOverFlow(false)
   }
  },[detailContainerRef,isEditOpen,showAddAddress,showAddAttachment])

  const getList = useCallback(async () => {
    if (!customerId || !showDetails) return;

    setIsLoading(true);
    try {
      const api = await getCustomerDetails(customerId);
      setCustomerDetails(api);
      const paidValue = api.total_amount - api.balance_amount;
      setPaidAmount(paidValue);
    } catch (error) {
      console.log(error, "error");
    } finally {
      setIsLoading(false);
    }
  }, [customerId, showDetails, setIsLoading]);

  useEffect(() => {
    getList();
  }, [customerId, getList]);

  const handleMenuClick = (index: number) => {
    setActiveMenuIndex(index);
  };

  const handleEditClose = () => {
    getList();
    setIsEditOpen(false);
  };

  const deleteAttachment = async (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: "Are you sure?",
      text: "This action will permanently delete the attachment.",
      confirmButtonText: "Ok",
      cancelButtonText: "Cancel",
      onConfirm: () => {
        setIsLoading(true);
        deleteCustomerAttachment(id)
          .then((res) => {
            console.log(res);

            getList();
            fire({
              position: "top-right",
              icon: "success",
              title: "Deleted Successfully",
              autoClose: 2000,
            });
          })
          .catch((error) => {
            const axiosError = error as AxiosError<ErrorResponseData>;
            fire({
              position: "center",
              icon: "error",
              title: "Something went wrong",
              text:
                axiosError?.response?.data?.detail ||
                axiosError?.message ||
                "An unknown error occurred",
              confirmButtonText: "Ok",
              // cancelButtonText: "No",
              onConfirm: () => {
                // console.log("Confirmed:");
              },
            });
            console.log(error, "error");
            setIsLoading(false);
          });
      },
    });
  };

  const openAttachment = (fileUrl: string | undefined) => {
    if (!fileUrl) {
      console.error("File URL is missing.");
      return;
    }
    window.open(mediaUrl + fileUrl, "_blank");
  };

  const handleUpload = async () => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("customer_id", String(customerId));

      if (uploadedFiles && uploadedFiles.file) {
        formData.append("title", uploadedFiles?.label);
        formData.append("uploded_file", uploadedFiles?.file);
      }

      const response = await addAttachment(formData);
      console.log("customer attacment create successful", response);
      getList();
      setUploadedFiles({ file: null, label: "" });
      fire({
        position: "top-right",
        icon: "success",
        title: "Created Successfully",
        autoClose: 2000,
      });
    } catch (err) {
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 700) return "green";
    if (score >= 400) return "orange";
    if (score > 0) return "red";
    return "black";
  };

  return (
    <>
      <div ref={detailContainerRef} className={`customer-details-container ${showDetails ? "show" : ""} `}>
        <div className="customerDetailsHeader">
          <p>
            {" "}
            <span className="active" onClick={() => route.push("/pages/home")}>
              Dashboard
            </span>
            {">"}
            <span className="active" onClick={handleClose}>
              Customer
            </span>
            {">"}
            <span>Customer Details</span>
          </p>
  
          <button className="closeButton" onClick={handleClose}>
            Close
          </button>
        </div>
  
        <div className="user-details-transaction-details-container">
          <div className="user-details">
            <div className="user-profile">
              <div className="profilePicture">
                <Image
                  src={
                    customerDetails?.profile_photo
                      ? `${mediaUrl + customerDetails.profile_photo}`
                      : dummyProfile
                  }
                  alt="profile picture"
                  fill
                  className="profileImage"
                ></Image>
              </div>
              {!customerDetails?.status && (
                <div className="offline-status-message">
                  <span>{customerDetails?.status_message}</span>
                </div>
              )}
              <div className="username">
                {customerDetails?.name}
                <span
                  className="material-icons-outlined editBtn"
                  onClick={() => setIsEditOpen(true)}
                >
                  edit
                </span>
              </div>
              <p className="address">
                {customerDetails?.short_title}{" "}
                {customerDetails?.care_of ? `c/o ${customerDetails.care_of}` : ""}
              </p>
              {customerDetails?.son_of && 
                <p className="address">S/o D/o W/o {customerDetails?.son_of}</p>              
              }
              <div className="rating">
                <RatingComponent
                  rating={customerDetails?.star_rating}
                  isReadOnly={true}
                  size="small"
                />
                {/* <span className="material-icons star">star</span>
  
                <span className="material-icons star">star</span>
  
                <span className="material-icons star">star</span>
  
                <span className="material-icons star">star</span>
                <span className="material-icons star-unfilled">star_outline</span> */}
              </div>
              <div className="wallet">
                <p>
                  {" "}
                  <span className="material-icons walletIcon">
                    account_balance_wallet
                  </span>{" "}
                  <span className="txt">Wallet</span>
                </p>{" "}
                <span className="rupees">₹{customerDetails?.wallet_amount}</span>
              </div>
            </div>
  
            <div className="user-purchase">
              <h3>
                Total purchase order count: {customerDetails?.total_order_count}
              </h3>
              <h3>Balance Amount: {customerDetails?.balance_amount}</h3>
  
              <div className="totalPayment">
                <div className="amount">
                  <span className="totalpaid">Total Paid ₹{paidAmount}</span>
                  <span className="total">
                    of ₹{customerDetails?.total_amount}
                  </span>
                </div>
  
                <div className="paymentBar">
                  <div
                    className="bar"
                    style={{
                      height: "7px",
                      width: `${
                        ((paidAmount ?? 0) /
                          (customerDetails?.total_amount ?? 1)) *
                        100
                      }%`,
                      backgroundColor: "#0a4213",
                      borderRadius: "6px",
                      transition: "width 0.3s ease-in-out", // Smooth animation
                    }}
                  ></div>
                </div>
              </div>
            </div>
  
            <div className="user-additional-details">
              <div className="detail detail1">
                <div className="icon">
                  <span className="material-icons-outlined">lock</span>
                </div>
                <div className="content">
                  <span className="label">User ID</span>
                  <span className="value">{customerDetails?.code && customerDetails.code != 'undefined' ? customerDetails.code : 'N/A'}</span>
                </div>
              </div>
              {customerDetails?.email && 
              <div className="detail detail2">
                  <div className="icon">
                    <span className="material-icons-outlined">mail</span>
                  </div>
                  <div className="content">
                    <span className="label">Email</span>
                    <span className="value">{customerDetails?.email}</span>
                  </div>
              </div>              
              }
              {customerDetails?.emi_type && 
               <div className="detail detail2">
                  <div className="icon">
                    <span className="material-icons-outlined">calendar_month</span>
                  </div>
                  <div className="content">
                    <span className="label">EMI</span>
                    <span className="value">{customerDetails?.emi_type} {customerDetails?.monthly_emi_due_date ? getOrdinalSuffix(customerDetails.monthly_emi_due_date) : customerDetails?.weekly_emi_due_date}</span>
                  </div>
                </div>             
              }

              <div className="detail detail3">
                <div className="icon">
                  <span className="material-icons-outlined">call</span>
                </div>
  
                <div className="content">
                  <span className="label">Phone Number</span>
                  {customerDetails?.phone_number && (
                    <span className="value">{customerDetails.phone_number}</span>
                  )}
  
                  {isMorePhoneActive && (
                    <>
                      {customerDetails?.whatsapp_number && (
                        <span className="value">
                          {customerDetails.whatsapp_number} (Whatsapp)
                        </span>
                      )}
                      {customerDetails?.phone_number2  &&  customerDetails.phone_number2 != 'null' &&(
                        <span className="value">
                          {customerDetails.phone_number2}
                        </span>
                      )}
                      {customerDetails?.phone_number3 &&  customerDetails.phone_number3 != 'null' && (
                        <span className="value">
                          {customerDetails.phone_number3}
                        </span>
                      )}
                      {customerDetails?.phone_number4 && customerDetails.phone_number4 != 'null' && (
                        <span className="value">
                          {customerDetails.phone_number4}
                        </span>
                      )}
                      {/* {!customerDetails?.phone_number4 && (
                        <div className="textfield-buttons">
                          <div className="text-field">
                            <input
                              type="text"
                              value={phoneNumberValue}
                              placeholder="Type..."
                              onChange={(e) =>
                                setPhoneNumberValue(e.target.value)
                              }
                            />
                          </div>
                          <div className="buttons">
                            <button onClick={handlePhoneSubmit}>Save</button>
                            <button onClick={() => setPhoneNumberValue("")}>
                              Cancel
                            </button>
                          </div>
                        </div>
                      )} */}
                    </>
                  )}
  
                  <p
                    onClick={() => setIsMorePhoneActive(!isMorePhoneActive)}
                    className={`${isMorePhoneActive ? "less" : "more"}`}
                  >
                    {isMorePhoneActive ? "less" : "more.."}
                  </p>
                </div>
              </div>
  
              <div style={{ alignItems: "start" }} className="detail detail4">
                <div className="icon">
                  <span className="material-icons-outlined">location_on</span>
                </div>
                <div className="content">
                  {customerDetails?.addresses &&
                    customerDetails.addresses.length > 0 && (
                      <>
                        {/* Show the first address */}
                        <div className="address-item">
                          <span className="label" style={{ display: "flex" }}>
                            {customerDetails.addresses[0].address_title}{" "}
                            {customerDetails.addresses[0].is_primary &&
                              "(Primary)"}
                            <TableMenuTwo
                              id={customerDetails.addresses[0].id}
                              onClick={handleMennuItemClick}
                              items={addressMenuItem}
                            />
                          </span>
                          <span className="value permanentAddress">
                            {customerDetails.addresses[0].address}
                          </span>
                        </div>
  
                        {/* Show additional addresses only if isMoreAddressActive is true */}
                        {isMoreAddressActive &&
                          customerDetails.addresses
                            .slice(1)
                            .map((item, index) => (
                              <div className="address-item" key={index + 1}>
                                <span
                                  className="label"
                                  style={{ display: "flex" }}
                                >
                                  {item.address_title}
                                  {item.is_primary && " (Primary)"}
                                  <TableMenuTwo
                                    id={item.id}
                                    onClick={handleMennuItemClick}
                                    items={addressMenuItem}
                                  />
                                </span>
                                <span className="value permanentAddress">
                                  {item.address}
                                </span>
                              </div>
                            ))}
  
                        {/* Toggle button */}
                        {customerDetails.addresses.length > 1 && (
                          <p
                            onClick={() =>
                              setIsMoreAddressActive(!isMoreAddressActive)
                            }
                            className={`${isMoreAddressActive ? "less" : "more"}`}
                          >
                            {isMoreAddressActive ? "less" : "more.."}
                          </p>
                        )}
                      </>
                    )}
                  <div className="textfield-buttons">
                    <div className="buttons">
                      <button onClick={handleAddAddress}>Add More Address</button>
                    </div>
                  </div>
                </div>
              </div>
  
              {/* <div className="detail detail5">
                <div className="icon">
                  <span className="material-icons-outlined">access_time</span>
                </div>
                <div className="content">
                  <span className="label">Latest Transaction</span>
                  <span className="value">12 December 2022</span>
                </div>
              </div> */}
  
              <div className="attachments-container">
                <h5>Attachments</h5>
                {customerDetails?.attachments &&
                  customerDetails.attachments.length > 0 &&
                  customerDetails.attachments.map((item, index) => (
                    <div className="attachment attachment1" key={index}>
                      <div className="image-title-date-time">
                        <div className="image">
                          {item.uploaded_file &&
                          /\.(jpg|jpeg|png|gif|webp)$/i.test(
                            item.uploaded_file
                          ) ? (
                            // If the file URL ends with an image extension, display the image
                            <Image
                              src={mediaUrl + item.uploaded_file} // URL of the image file
                              alt={item.title || "Uploaded Image"}
                              layout="fill"
                              objectFit="contain"
                            />
                          ) : (
                            // Otherwise, display the PDF icon
                            <Image
                              src={pdfIcon} // Default PDF icon
                              alt="PDF Icon"
                              layout="fill"
                              objectFit="contain"
                            />
                          )}
                        </div>
  
                        <div className="title-date-time">
                          <div className="title">{item.title}</div>
                          <div className="date-time">
                            {new Date(item.created_at).toLocaleDateString()} -{" "}
                            {new Date(item.created_at).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      <div className="viewBtn">
                        <button
                          className="view-bttn"
                          onClick={() => openAttachment(item.uploaded_file)}
                        >
                          View
                        </button>
                        <button className="delete-bttn">
                          <div
                            className="material-icons"
                            onClick={() => deleteAttachment(item.id)}
                          >
                            delete
                          </div>
                        </button>
                      </div>
                    </div>
                  ))}
                {!uploadedFiles?.label && (
                  <div className={`addDocumentBtn addDocumentBtn"`}>
                    <button onClick={handleAddAttachment}>
                      <span>+ </span>Add Document
                    </button>
                  </div>
                )}
                <div className="drag-drop-documents-container">
                  {uploadedFiles?.file && uploadedFiles?.label && (
                    <div className="drag-drop-documents">
                      <div className="image-label">
                        {/* <div className="image">
                          {uploadedFiles.file.type === "application/pdf" ? (
                            <Image
                              src={pdfIcon}
                              alt="PDF Icon"
                              fill
                              objectFit="contain"
                            />
                          ) : uploadedFiles.file.type === "image/jpeg" ||
                            uploadedFiles.file.type === "image/png" ? (
                            <Image
                              src={URL.createObjectURL(uploadedFiles.file)}
                              alt="Uploaded File"
                              fill
                              objectFit="contain"
                            />
                          ) : (
                            <span>Unsupported File Type</span>
                          )}
                        </div> */}
  
                        <div className="image">
                          {uploadedFiles.file.type === "application/pdf" ? (
                            <Image
                              src={pdfIcon}
                              alt="PDF Icon"
                              fill
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              style={{ objectFit: "contain" }}
                            />
                          ) : uploadedFiles.file.type === "image/jpeg" ||
                            uploadedFiles.file.type === "image/png" ? (
                            <Image
                              src={URL.createObjectURL(uploadedFiles.file)}
                              alt="Uploaded File"
                              fill
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              style={{ objectFit: "contain" }}
                            />
                          ) : (
                            <span>Unsupported File Type</span>
                          )}
                        </div>
  
                        <div className="label">{uploadedFiles.label}</div>
                      </div>
  
                      <div className="buttons">
                        <button
                          className="acceptButton"
                          onClick={() => {
                            handleUpload();
                          }}
                        >
                          Upload
                        </button>
                        <button
                          className="rejectButton"
                          onClick={() =>
                            setUploadedFiles({ file: null, label: "" })
                          } // Reset uploaded file
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
  
                <div className="userDetailsEndBorder">
                  <div className="border"></div>
                </div>
              </div>
            </div>
          </div>
  
          <div className="transaction-details">
            <div className="user-overview-container">
              <div className="userOverview userOverview1 ">
                <h3>Total due</h3>
                <h1>₹{customerDetails?.wallet_amount}</h1>
              </div>
              {/* <div className="userOverview userOverview2 ">
                <div className="icon">
                  <span className="material-icons">support_agent</span>
                </div>
                <p>Collection Agent Name</p>
                <h3>Mr. Alan</h3>
              </div> */}
              <div className="userOverview userOverview3">
                <div className="icon">
                  <span className="material-icons-outlined">access_time</span>
                </div>
                <p>Next EMI due date</p>
                <h3>
                  {
                    customerDetails?.next_emi_due_date ? (new Date(
                      customerDetails?.next_emi_due_date ?? ""
                    ).toLocaleDateString()) : (<div className="no-emi-due-date-available"><h3>Not available</h3></div>)
                  }
                </h3>
              </div>
              <div className="userOverview userOverview4">
                <div className="icon">
                  <span className="material-icons">verified</span>
                </div>
                <p>Customer Score</p>
                <h3
                  style={{
                    color: getScoreColor(
                      customerDetails?.credit_score
                        ? customerDetails.credit_score
                        : 0
                    ),
                  }}
                >
                  {customerDetails?.credit_score}
                </h3>
              </div>
            </div>
  
            <div className="view-detail-table-container">
              <div className="view-detail-table-menu">
                <div
                  className={`menuItem menuItem1 ${
                    activeMenuIndex === 0 ? "active" : ""
                  }`}
                  onClick={() => handleMenuClick(0)}
                >
                  Purchase order
                </div>
                <div
                  className={`menuItem menuItem2 ${
                    activeMenuIndex === 1 ? "active" : ""
                  }`}
                  onClick={() => {
                    handleMenuClick(1);
                  }}
                >
                  Collection History
                </div>
                <div
                  className={`menuItem menuItem3 ${
                    activeMenuIndex === 2 ? "active" : ""
                  }`}
                  onClick={() => {
                    handleMenuClick(2);
                  }}
                >
                  EMI Note
                </div>
              </div>
  
              {activeMenuIndex == 0 && (
                <CustomerPurchaseOrder
                  customerId={customerId}
                  isOpen={showDetails}
                />
              )}
  
              {activeMenuIndex == 1 && (
                <CustomerCollectionHistory customerId={customerId} />
              )}
  
              {activeMenuIndex == 2 && (
                <CustomerEminNote customerId={customerId} />
              )}
            </div>
          </div>
        </div>
  
      </div>
      <AddAddress
        customer_id={customerId}
        address_id={selectedAddress}
        showCreate={showAddAddress}
        handleCloseCreate={handleCloseAddAddress}
      />
      <AddAttachment
        showCreate={showAddAttachment}
        handleCloseCreate={handleCloseAddAttachment}
        onFileUpload={handleFileUpload}
      />
      <EditCustomer
        isOpen={isEditOpen}
        handleCloseCreate={handleEditClose}
        id={customerId}
      />
    </>
  );
}

export default CustomerDetails;
