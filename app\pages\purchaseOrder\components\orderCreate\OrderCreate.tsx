import React, { useCallback, useEffect, useState } from "react";
import "./OrderCreate.scss";
import { useF<PERSON>, SubmitH<PERSON><PERSON>, Controller, useFieldArray, useWatch,} from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import { order_create } from "../../purchaseOrder.model";
import { useCommonContext } from "@/app/contexts/commonContext";
import MulitpleFileUpload from "@/app/pages/customer/components/FileUpload/MulitpleFileUpload";
import { createOrder, fetchCustomerList } from "../../purchaseOrder-service";
import { customer } from "@/app/pages/customer/customer.model";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { getBrachList } from "@/app/pages/agent/agent-service";
import { Branch_List_Item } from "@/app/pages/branch/branch.model";
import { AxiosError } from "axios";
import { getOrdinalSuffix } from "@/app/components/utilities/Pipes/getOrdinalSuffix";

interface orderCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
}

interface ErrorResponseData {
  detail?: string;
}

type Row = {
  itemName: string;
  quantity: string;
  amount: string;
};

type RowError = {
  itemName: string;
  quantity: string;
  amount: string;
};

function OrderCreate({ showCreate, handleCloseCreate }: orderCreateProps) {
  const [rows, setRows] = useState<Row[]>([]);
  const [rowErrors, setRowErrors] = useState<RowError[]>([]);
  const [options, setOptions] = useState<customer[]>([]);
  const [branchList, setBranchList] = useState<Branch_List_Item[]>([]);
  const [dummyBranchList, setDummyBranchList] = useState<Branch_List_Item[]>([]);
  const [isMonthly, setIsMonthly] = useState<boolean>(false);
  const [isFileUploading,setIsFileUploading] = useState<boolean>(false);

  const [currentEmiData,setCurrentEmiData] = useState<{emi_date:string,emi_type:string}>()
  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<order_create>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "items",
  });

  const handleAddRow = useCallback(() => {
    append({ name: "", qty: 0, amount: 0 });
    setRowErrors([])
  }, [append]);

  const handleRemoveRow = (index: number) => {
    if(fields.length > 1){
      remove(index);
    }else{
      setRowErrors([{itemName:'Minimum One Item Is Needed',quantity:'',amount:''}])
    }
  };

  const getDayOptions = () => {
    const suffixes = ["th", "st", "nd", "rd"];
    return Array.from({ length: 31 }, (_, i) => { // Only up to 28
      const day = i + 1;
      const suffix =
        day % 10 < 4 && day % 10 > 0 && (day < 11 || day > 13)
          ? suffixes[day % 10]
          : "th";
      return `${day}${suffix}`;
    });
  };



  const dayOptions = getDayOptions();

  // const handleInputChange = (index: number, field: string, value: string) => {
  //   const updatedRows = rows.map((row, i) =>
  //     i === index ? { ...row, [field]: value } : row
  //   );
  //   setRows(updatedRows);
  // };

  const validateRows = () => {
    if (rows.length === 0) {
      // If no rows are added, return an error
      setRowErrors([
        { itemName: "At least one item is required", quantity: "", amount: "" },
      ]);
      return false;
    }

    const newErrors: RowError[] = rows.map((row) => ({
      itemName: !row.itemName ? "Item Name is required" : "",
      quantity: !row.quantity ? "Quantity is required" : "",
      amount: !row.amount ? "Amount is required" : "",
    }));
    setRowErrors(newErrors);

    // Return whether all rows are valid
    return newErrors.every(
      (error) => !error.itemName && !error.quantity && !error.amount
    );
  };

  const onSubmit: SubmitHandler<order_create> = async (data) => {
    //console.log("Form Data", data);
    if(isFileUploading){
      fire({
        position: "center",
        icon: "error",
        title: "File is uploading",
        text: "Please wait until the file is uploaded.",
        confirmButtonText: "Ok",
        });
      return; // Exit early if file is uploading
    }

    if(data.monthly_emi_due_date_inputs){
      data.monthly_emi_due_date = parseInt(String(data.monthly_emi_due_date_inputs).replace(/\D/g, ""), 10) || undefined;
    }

    try {
      setIsLoading(true);
      const result = await createOrder(data);
      if(result){
        handleCloseCreate();
        reset();

         fire({
          position: "top-right",
          icon: "success", // Use success icon
          title: "Order created",
          text: "The order is created successfully!",
          autoClose: 2000,
        });
      }
    } catch (err) {
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      //setIsLoading(false);
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getBranchLists = useCallback(async () => {
    setIsLoading(true);
    try {
      const api = await getBrachList();
      setBranchList(api.results);
      setDummyBranchList(api.results); // Store original data in dummy list
    } catch (error) {
      console.log("error", error);
      fire({
        position: "center",
        icon: "error",
        title: "Some error occurred",
        text: "Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {},
      });

      getBranchLists(); // Avoid infinite loop
    } finally {
      setIsLoading(false);
    }
  }, [fire,setIsLoading]);

  const getCustomerList = useCallback(async () => {
    try {
      const res = await fetchCustomerList();
      setOptions(res.results);
    } catch (error) {
      console.log("error", error);
      fire({
        position: "center",
        icon: "error",
        title: "Some error occurred",
        text: "Please reload the page.",
        confirmButtonText: "Ok",
        onConfirm: () => {},
      });

      getBranchLists(); // This is now stable due to useCallback
    }
  }, [getBranchLists,fire]);

  // Function to filter branches based on customer's branch_id
  const filterBranchesForCustomer = useCallback((customerBranchId: number | null) => {
    if (!customerBranchId || dummyBranchList.length === 0) {
      // If no customer branch ID or no dummy data, show all branches
      setBranchList(dummyBranchList);
      return;
    }

    // Find the customer's branch in dummy list
    const customerBranch = dummyBranchList.find(branch => branch.id === customerBranchId);

    if (customerBranch) {
      // If customer's branch exists in dummy list, show only that branch
      setBranchList([customerBranch]);
    } else {
      // If customer's branch not found in dummy list, show all branches
      // This shouldn't happen in normal cases, but it's a fallback
      setBranchList(dummyBranchList);
    }
  }, [dummyBranchList]);

  useEffect(() => {
    getCustomerList();
    getBranchLists();
  }, [getBranchLists,getCustomerList]);

  // Ensure all branches are shown when form opens and no customer is selected
  useEffect(() => {
    if (showCreate && dummyBranchList.length > 0 && !getValues("customer_id")) {
      setBranchList(dummyBranchList);
    }
  }, [showCreate, dummyBranchList, getValues]);



  const handleFileUploadChange = (ids: number[]) => {
    setValue("attachments_ids", ids);
    //console.log("Updated files:", ids);
  };
  // Handle checkbox change manually if needed
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setValue("is_accounted", isChecked); // Update the checkbox value manually
    console.log("ischecked", isChecked);
  };

  useEffect(() => {
    // Only set default branch when branchList is available and branch_id is not set
    // Don't auto-select customer to allow showing all branches initially
    if (branchList.length > 0 && !getValues("branch_id")) {
      setValue("branch_id", branchList[0].id);
    }
  }, [getValues, setValue, branchList]);

  useEffect(() => {
    if (!showCreate) {
      reset({
        title: "",
        invoice_no: "",
        reference_no: "",
        total_amount: null,
        advance_amount: null,
        customer_id: undefined, // Don't auto-select customer
        emi_amount: null,
        emi_type: "",
        emi_period: null,
        emi_period_type: "",
        branch_id: undefined,
        items: [],
        attachments_ids: [],
        is_accounted: false,
      });
      setRows([]);
      // Reset branch list to show all branches when form is closed
      setBranchList(dummyBranchList);
    } else {
      handleAddRow();
    }
    setCurrentEmiData({
      emi_date:'',
      emi_type:''
    })
  }, [showCreate, handleAddRow, reset, dummyBranchList]);

  const emiPeriodType = useWatch({ control, name: "emi_period_type" });
  const customer = useWatch({ control, name: "customer_id" });
  const emiperiod = useWatch({ control, name: "emi_period" });




  // Inside your component
  const handleCurrentEmiData = useCallback((item: customer | null) => {
    if(item){
      const date = new Date(item.next_emi_due_date);
      let formattedDate = "";

      // Filter branches for this customer
      filterBranchesForCustomer(item.branch_id);

      // Find the customer's branch in dummy list (since branchList might be filtered)
      const branch = dummyBranchList.find((branch) => branch.id === item.branch_id);

      if (item.emi_type === "weekly") {
        formattedDate = date.toLocaleDateString("en-US", { weekday: "long" }) ?? "";
      } else {
        const day = date.getDate();
        formattedDate = getOrdinalSuffix(day);
      }

      setCurrentEmiData({
        emi_date: formattedDate,
        emi_type: item.emi_type,
      });

      // Set the branch_id to customer's branch if it exists, otherwise use first available branch
      setValue("branch_id", branch?.id ? branch?.id : dummyBranchList[0]?.id);

      setValue(
        item.emi_type === "weekly"
          ? "weekly_emi_due_date"
          : "monthly_emi_due_date_inputs",
        formattedDate
      );
      if(item.emi_period_type && item.emi_type){
        setValue("emi_period_type",item.emi_period_type)
        setValue("emi_type", item.emi_type);
      }else{
        setValue("emi_period_type","weekly");
        setValue("emi_type", "weekly");
      }
    }else{
      // Reset to show all branches when no customer is selected
      setBranchList(dummyBranchList);
      setCurrentEmiData({
        emi_date:'',
        emi_type:''
      })
    }

    },
    [setCurrentEmiData, setValue, filterBranchesForCustomer, dummyBranchList]
  );

  const handleEmiPeriodValueChange = useCallback((period: number) => {
    const advAmmount = getValues("advance_amount");
    const totalAmount = getValues("total_amount");

    if (advAmmount && totalAmount) {
      const emiAmount = (totalAmount - advAmmount) / period;
      setValue("emi_amount", emiAmount);
    }
  }, [getValues, setValue]);

  const handleFileLoading = (state:boolean) => {
    setIsFileUploading(state);
  }

  useEffect(()=>{
    const period =  getValues("emi_period")
    if(period){
      handleEmiPeriodValueChange(period)
    }
  },[handleEmiPeriodValueChange,getValues,emiperiod])

  useEffect(() => {
    setIsMonthly(emiPeriodType !== "weekly");
  }, [emiPeriodType]);

  useEffect(()=>{
    if(customer && options.length>0){
      const customerData = options?.find((optionsCustomer) => optionsCustomer.id === customer);
      if(customerData){
        console.log(customerData,"customer data");

        handleCurrentEmiData(customerData)
      }else{
        // If customer ID doesn't match any customer, show all branches
        setBranchList(dummyBranchList);
        handleCurrentEmiData(null)
      }
    }else{
      // No customer selected or no options available - show all branches
      setBranchList(dummyBranchList);
      handleCurrentEmiData(null)
    }
  },[customer,options,handleCurrentEmiData,dummyBranchList])

  return (
    <div
      className={`create-form-overlay purchase-order-create ${
        showCreate ? "show" : ""
      }`}
    >
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Add New Order</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-100">
                <label htmlFor="title">Title</label>
                <input
                  {...register("title", {
                    required: "Please provide a title.",
                  })}
                  id="title"
                  type="text"
                  placeholder="Enter the title"
                />
                <p className="error-message">{errors.title?.message}</p>
              </div>

              <div className="select-input-field wf-100">
                <label htmlFor="customer_id">Select Customers</label>

                <Controller
                  name="customer_id"
                  control={control}
                  rules={{ required: "Please select a customer." }} // Adding required rule with custom message
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="customer_id"
                      options={options}
                      getOptionLabel={(option) => option?.name}
                      value={ options && options.length>0 &&
                        options?.find(
                          (option) => option.id === getValues("customer_id")
                        ) || null
                      }
                      onChange={(_, value) => {
                        setValue("customer_id", value?.id ?? 0);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Customer"
                          error={!!errors?.customer_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1, // Ensures the placeholder is fully visible
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6", // Default border color is black with 2px width
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6", // No color change on hover
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E", // No color change on focus
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6", // Prevent red border when there's an error
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E", // Border color on focus even when error=true
                              },
                              "& input": {
                                padding: "10px", // Adjust input padding as needed
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />

                {/* Show error message if the field is empty and not selected */}
                {errors?.customer_id && (
                  <p className="error-message" style={{ color: "#f44336" }}>
                    {errors?.customer_id?.message}
                  </p>
                )}
                {currentEmiData && currentEmiData.emi_type && (
                  <div className="hint-container">
                    <div className="hint-message">Emi Type: <span>{''}{currentEmiData.emi_type}</span></div> <div className="hint-message"> EMI Payment{!isMonthly ? ' Day' : ' Date'}:<span>{''}{currentEmiData.emi_date}</span>
                    </div>
                  </div>
                )}
              </div>
              <div className="select-input-field wf-100">
                <label htmlFor="branch_id">Branch</label>
                <Controller
                  name="branch_id"
                  control={control}
                  rules={{ required: "Please select a Branch." }} // Adding required rule with custom message
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="branch_id"
                      options={branchList}
                      getOptionLabel={(option) => option?.name}
                      value={branchList && branchList.length>0 &&
                        branchList?.find(
                          (option) => option.id === getValues("branch_id")
                        ) || null
                      }
                      onChange={(_, value) => {
                        setValue("branch_id", value?.id ?? 0); // Store the selected customer's `id`
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Customer"
                          error={!!errors?.branch_id}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                <p className="error-message">{errors.branch_id?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="invoiceNo">Invoice No</label>
                <input
                  {...register("invoice_no")}
                  id="invoiceNo"
                  type="text"
                  placeholder="Enter invoice number"
                />
                <p className="error-message">{errors.invoice_no?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="referenceNo">Reference No</label>
                <input
                  {...register("reference_no")}
                  id="referenceNo"
                  type="text"
                  placeholder="Enter reference number"
                />
                <p className="error-message">{errors.reference_no?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="totalAmount">Total Amount</label>
                <input
                  {...register("total_amount", {
                    required: "Please specify the total amount.",
                  })}
                  id="totalAmount"
                  type="text"
                  placeholder="Enter total amount"
                />
                <p className="error-message">{errors.total_amount?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="advanceAmount">Advance Amount</label>
                <input
                  {...register("advance_amount", {
                    required: "Advance amount is mandatory.",
                  })}
                  id="advanceAmount"
                  type="text"
                  placeholder="Enter advance amount"
                />
                <p className="error-message">
                  {errors.advance_amount?.message}
                </p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="emiType">EMI Type</label>
                <div className="input purchase-order">
                  <select
                    {...register("emi_type", {
                      required: "Please choose an EMI type.",
                    })}
                    id="emiType"
                  >
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
                <p className="error-message">{errors.emi_type?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="emiPeriodType">EMI Period Type</label>
                <div className="input purchase-order">
                  <select
                    {...register("emi_period_type", {
                      required: "Please select an EMI period type.",
                    })}
                    id="emiPeriodType"
                  >
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
                <p className="error-message">
                  {errors.emi_period_type?.message}
                </p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="emiPeriod">EMI Period</label>
                <input
                  {...register("emi_period", {
                    required: "EMI period is a required field.",
                  })}
                  id="emiPeriod"
                  type="text"
                  placeholder="Enter EMI period"
                />
                <p className="error-message">{errors.emi_period?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="emiAmount">EMI Amount</label>
                <input
                  {...register("emi_amount", {
                    required: "EMI amount is a required field.",
                  })}
                  id="emiAmount"
                  type="text"
                  placeholder="Enter EMI amount"
                />
                <p className="error-message">{errors.emi_amount?.message}</p>
              </div>
              {isMonthly ? (
                <div className="select-input-field wf-50">
                  <label htmlFor="monthly_emi_due_date_inputs">
                    Monthly Emi Payment Date
                  </label>
                  <Controller
                    name="monthly_emi_due_date_inputs"
                    control={control}
                    rules={{ required: "Please select Monthly Emi Date." }} // Adding required rule with custom message
                    render={({ field }) => (
                      <Autocomplete
                        {...field}
                        id="monthly_emi_due_date_inputs"
                        options={dayOptions}
                        getOptionLabel={(option) => option}
                        value={ dayOptions && dayOptions.length>0 &&
                          dayOptions?.find(
                            (option) =>
                              option === getValues("monthly_emi_due_date_inputs")
                          ) || null
                        }
                        onChange={(_, value) => {
                          setValue("monthly_emi_due_date_inputs", value ? value : ""); // Store the selected customer's `id`
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select Monthly Emi Date"
                            error={!!errors?.monthly_emi_due_date}
                            variant="outlined"
                            InputProps={{
                              ...params.InputProps,
                              style: {
                                padding: "2px",
                                borderRadius: "6px",
                                backgroundColor: "#ffffff",
                              },
                            }}
                            sx={{
                              "& .MuiInputBase-input::placeholder": {
                                opacity: 1,
                              },
                              "& .MuiOutlinedInput-root": {
                                "& fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&:hover fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                                "& input": {
                                  padding: "10px",
                                },
                                "&.Mui-error fieldset": {
                                  border: "2px solid #D6D6D6",
                                },
                                "&.Mui-focused.Mui-error fieldset": {
                                  border: "2px solid #1E1E1E",
                                },
                              },
                            }}
                          />
                        )}
                      />
                    )}
                  />
                  <p className="error-message">
                    {errors.monthly_emi_due_date_inputs?.message}
                  </p>
                </div>
              ) : (
                <div className="input-field wf-50">
                  <label htmlFor="weekly_emi_due_date">
                    Weekly EMI Payment Day
                  </label>
                  <div className="input purchase-order">
                    <select
                      {...register("weekly_emi_due_date", {
                        required: "Please select a Weekly EMI Payment Day.",
                      })}
                      id="weekly_emi_due_date"
                    >
                      <option value="Sunday">Sunday</option>
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                    </select>
                  </div>
                  <p className="error-message">
                    {errors.emi_period_type?.message}
                  </p>
                </div>
              )}

              <div className="isAccountedButton">
                <div className="content">
                  <input
                    id="isAccounted"
                    type="checkbox"
                    {...register("is_accounted")}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor="isAccounted" className="label">
                    Accounted
                  </label>
                </div>
                {/* <p className="error-message">{errors.is_accounted?.message}</p> */}
              </div>

              <div className="wf-100">
                <div className="table-input-container">
                  <h5>Items</h5>
                  <div className="table-container scroll-bar-1">
                    <table>
                      <thead>
                        <tr>
                          <th>Items Name</th>
                          <th>Quantity</th>
                          <th>Amount</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {fields.map((field, index) => (
                          <tr key={field.id}>
                            <td>
                              <input
                                type="text"
                                placeholder="Type..."
                                {...register(`items.${index}.name`, {
                                  required: "Item Name is required",
                                })}
                              />
                              {errors.items?.[index]?.name && (
                                <p className="error-message">
                                  {errors.items[index].name.message}
                                </p>
                              )}
                            </td>
                            <td>
                              <input
                                type="text"
                                placeholder="Type..."
                                {...register(`items.${index}.qty`, {
                                  required: "Quantity is required",
                                })}
                              />
                              {errors.items?.[index]?.qty && (
                                <p className="error-message">
                                  {errors.items[index].qty.message}
                                </p>
                              )}
                            </td>
                            <td>
                              <input
                                type="text"
                                placeholder="Type..."
                                {...register(`items.${index}.amount`, {
                                  required: "Amount is required",
                                })}
                              />
                              {errors.items?.[index]?.amount && (
                                <p className="error-message">
                                  {errors.items[index].amount.message}
                                </p>
                              )}
                            </td>
                            <td className="table-remove-button">
                              <button type="button" onClick={() => handleRemoveRow(index)}>
                                <span className="material-icons">delete</span>
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {rowErrors.length > 0 && (
                    <div className="global-error">
                      <p className="error-message">{rowErrors[0].itemName}</p>
                    </div>
                  )}
                  <div className="addRowBtn">
                    <button
                      className="closeButton"
                      onClick={handleAddRow}
                      type="button"
                    >
                      Add row
                    </button>
                  </div>
                </div>
              </div>

              <div className="wf-100">
                <MulitpleFileUpload
                 handleLoadingChange={handleFileLoading}
                  type="purchaseorder"
                  isOpen={showCreate}
                  handleChange={handleFileUploadChange}
                />
              </div>
            </div>
            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default OrderCreate;
