@use 'sass:color';
//@import '/app/styles/variables.scss';
@use '/app/styles/variables' as *;




   .routeList{
    .table-style{
        table{

            .routeDetails{
                display: flex;
                flex-direction: column;

                .day{
                    font-size: 12px;
                    color: $black_color3;
                    font-weight: 500;
                
                }

                .route{
                    font-size: 12px;
                    color: $black_color;
                    font-weight: 600;
                }

                .viewDetails{
                    a{
                        font-size: 11px;
                        font-weight: 600;
                        color: $link_color;
                        cursor: pointer;
                        text-decoration: none;
                       
               
                       &:hover{
                            //color: darken($link_color, 10%);
                            color: color.adjust($link_color, $lightness: -10%);

               
                        }
                    }
                }
                }

                .branchName{
                font-size: 12px;
                font-weight: 600;
                }

               .createdBy{
                display: flex;
                flex-direction: column;
                
                .name{
                font-size: 12px;
                font-weight: 600;
                color: $black_color;
                margin-bottom: 1px;
                }

                .position{
                    font-size: 10px;
                    font-weight: 500;
                    color: $black_color3;

                }
                }

                .createdDate{
                    font-size: 12px;
                    font-weight: 600;
                    }

                .notAssigned{
                    font-size: 12px;
                    color: $black_color3;
                    font-weight: 600;
                }

                .noData{
                    font-size: 12px;
                    color: $black_color3;
                    font-weight: 600;
                }

            
            
            
            
       }
       }
   }




