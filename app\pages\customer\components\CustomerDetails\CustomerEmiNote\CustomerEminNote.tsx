"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./customer-emi-note.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import ViewFileIcon from "../../../../../assets/svg/viewFileIcon.svg";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import { getCustomerEmiNote } from "../../../customer-service";
import { customer_emi_note } from "../../../customer.model";
import { useCommonContext } from "@/app/contexts/commonContext";

interface props {
  customerId: number | null;
}
interface filter {
  reminder_date?: string;
  created_date?: string;
}
function CustomerEminNote({ customerId }: props) {
  const [filter, setFilter] = useState<filter>();
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [totalPages, setTotalPages] = useState<number>(10);
  const [list, setList] = useState<customer_emi_note[]>([]);
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const {maxDate} = useCommonContext()
  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const { value } = event.target;
    setFilter((prev) => {
      const updatedFilter = { ...prev, [item]: value };
      getList(
        1,
        itemsPerPage,
        searchValue,
        updatedFilter.created_date || "",
        updatedFilter.reminder_date || ""
      );
      return updatedFilter;
    });
  };

  const handlePageChange = (pageNo: number) => {
    console.log(pageNo, "page changed");
    getList(
      pageNo,
      itemsPerPage,
      searchValue,
      filter?.created_date || "",
      filter?.reminder_date || ""
    );
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(
      1,
      value,
      searchValue,
      filter?.created_date || "",
      filter?.reminder_date || ""
    );
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPage: number,
      search: string,
      created_at: string,
      remainder_date: string
    ) => {
      const skip = (pageNo - 1) * itemsPerPage;
      setIsTableLoading(true);
      try {
        const api = await getCustomerEmiNote(
          skip,
          itemsPerPage,
          search,
          created_at,
          remainder_date,
          customerId
        );
        setList(api.results);
        setPage(api.page);
        setTotalPages(api.total_pages);
      } catch (error) {
        console.log("error", error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [customerId] // Adding customerId as a dependency
  );

  // const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const value = e.target.value;
  //   setSearchValue(value);
  //   debouncedGetList(value);
  // };

  // const debouncedGetList = debounce(
  //   (value: string) =>
  //     getList(
  //       1,
  //       itemsPerPage,
  //       value,
  //       filter?.created_date || "",
  //       filter?.reminder_date || ""
  //     ),
  //   300
  // );

  const handleReset = () => {
    setSearchValue("");
    setFilter({} as filter);
    getList(1, itemsPerPage, "", "", "");
  };

  useEffect(() => {
    if (typeof customerId === "number" && !isNaN(customerId)) {
      getList(1, 10, "", "", "");
    }
  }, [customerId, getList]);
  return (
    <div className="emi-note-container">
      <div className="table-header">
        <div
          className="filter-search-container"
          style={{ alignItems: "flex-end" }}
        >
          <div className="filterButton" onClick={handleToggleFilter}>
            <button>
              <i className="fa-solid fa-filter"></i>
            </button>
          </div>

          <div
            className={`filter-options-select-box ${
              isFilterOpen ? "show" : ""
            }`}
          >
            <div className="date-picker-container">
              <label htmlFor="created-date">Created Date</label>
              <input
                id="created-date"
                className="date-picker"
                type="date"
                max={maxDate}
                placeholder="Created Date"
                value={filter?.created_date || ""}
                onChange={(e) => handleFilterSelect(e, "created_date")}
              />
            </div>

            <div className="date-picker-container">
              <label htmlFor="reminder-date">Reminder Date</label>
              <input
                id="reminder-date"
                className="date-picker"
                type="date"
                max={maxDate}
                placeholder="Reminder Data"
                value={filter?.reminder_date || ""}
                onChange={(e) => handleFilterSelect(e, "reminder_date")}
              />
            </div>
          </div>

          {/* <SearchBox
            value={searchValue}
            onChange={handleSearchChange}
            placeholders={[`Search`]}
          /> */}
        </div>
      </div>

      <div className="table2">
        <table>
          <thead>
            <tr>
              <th>Agent Name</th>
              <th>Create Date And Time</th>
              <th>Reminder Date</th>
              <th>Note</th>
              <th>
                <div className="attachments">Attachments</div>
              </th>
              {/* <th>Action</th> */}
            </tr>
          </thead>
          <tbody>
            {isTableLoading ? (
              <tr>
                <td colSpan={6}>
                  <TableWithShimmer
                    no_of_cols={5}
                    no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                  />
                </td>
              </tr>
            ) : list.length > 0 ? (
              list.map((item, index) => (
                <tr key={index}>
                  <td>
                    <div className="checkbox-name">
                      {/* <input type="checkbox" /> */}
                      <div className="name">{item.agent_name}</div>
                    </div>
                  </td>
                  <td>
                    <div className="date-time">
                      <div className="date">
                        {new Date(item.created_at).toLocaleDateString()}
                      </div>
                      <div className="time">
                        {new Date(item.created_at).toLocaleTimeString()}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="date">
                      {new Date(item.reminder_time).toLocaleDateString()}
                    </div>
                  </td>
                  <td>
                    <div className="remark">
                      {item.note ? item.note : "N/A"}
                    </div>
                  </td>
                  <td>
                    <div className="attachments">
                      {item.attachment ? (
                        <ViewFileIcon className="view-file-icon" />
                      ) : (
                        <span className="noAttachments"> N/A</span>
                      )}
                    </div>
                  </td>
                  {/* <td>
                    <div className="actionIcons">
                      <span className="material-icons eyeIcon">visibility</span>
                      <span className="material-icons editIcon">edit</span>
                      <span className="material-icons deleteIcon">delete</span>
                    </div>
                  </td> */}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="no-data">
                  <h5>
                    {filter?.created_date &&
                      filter?.reminder_date &&
                      searchValue &&
                      `No EMI note found with the selected created date "${filter.created_date}", reminder date "${filter.reminder_date}", and search value "${searchValue}".`}
                    {filter?.created_date &&
                      filter?.reminder_date &&
                      !searchValue &&
                      `No EMI note found with the selected created date "${filter.created_date}" and reminder date "${filter.reminder_date}".`}
                    {filter?.created_date &&
                      !filter?.reminder_date &&
                      searchValue &&
                      `No EMI note found with the selected created date "${filter.created_date}" and search value "${searchValue}".`}
                    {filter?.created_date &&
                      !filter?.reminder_date &&
                      !searchValue &&
                      `No EMI note found with the selected created date "${filter.created_date}".`}
                    {!filter?.created_date &&
                      filter?.reminder_date &&
                      searchValue &&
                      `No EMI note found with the selected reminder date "${filter.reminder_date}" and search value "${searchValue}".`}
                    {!filter?.created_date &&
                      filter?.reminder_date &&
                      !searchValue &&
                      `No EMI note found with the selected reminder date "${filter.reminder_date}".`}
                    {!filter?.created_date &&
                      !filter?.reminder_date &&
                      searchValue &&
                      `No EMI note found with the search value "${searchValue}".`}
                    {!filter?.created_date &&
                      !filter?.reminder_date &&
                      !searchValue &&
                      `No EMI note found for the customer yet.`}
                  </h5>

                  {(searchValue ||
                    filter?.created_date ||
                    filter?.reminder_date) && (
                    <button
                      onClick={handleReset}
                      style={{ marginLeft: "auto", marginRight: "auto" }}
                      className="submitButton"
                    >
                      <span className="material-icons">restart_alt</span>Reset
                    </button>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {list && list.length > 0 && (
        <div className="pagination-table-container">
          <Pagination
            totalPages={totalPages}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={page}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
}

export default CustomerEminNote;
