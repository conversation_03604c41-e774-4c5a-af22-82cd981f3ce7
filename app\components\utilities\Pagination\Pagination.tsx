import React, { useEffect, useState } from "react";
import "./Pagination.scss";
import { useAlert } from "../Alert/Alert";

interface prop{
  handlePage: (page: number) => void;
  page: number;
  itemsPerPage: number;
  handleItemsPerPageChange: (itemsPerPage: number) => void;
  totalPages: number;
}
function Pagination({
  handlePage,
  page,
  itemsPerPage,
  handleItemsPerPageChange,
  totalPages,
}:prop) {
  const [inputValue, setInputValue] = useState<string>(String(itemsPerPage));
  const { fire } = useAlert();

  const handlePrevious = () => {
    if (page > 1) {
      handleChangePage(page - 1);
    }
  };

  const handleNext = () => {
    if (page < totalPages) {
      handleChangePage(page + 1);
    }
  };

  const handleResultsPerPage = (e:React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if (/^\d*$/.test(value)) {
      setInputValue(value);
    }
  };

  const handleChangePage = (pageNo:number) => {
    if(pageNo != page){
      
      handlePage(pageNo)
    }
  }



  useEffect(() => {
    setInputValue(String(itemsPerPage));
  }, [itemsPerPage]);

  const renderPageNumbers = () => {
    const pages: JSX.Element[] = [];

    // Determine the range of pages to display
    const startPage = Math.max(1, page - 1); // One before the current page
    const endPage = Math.min(totalPages, page + 1); // One after the current page

    // Always show the first page
    if (startPage > 1) {
      pages.push(
        <div
          key="1"
          className={`page paginationButton pNo ${
            page === 1 ? "active" : ""
          }`}
          onClick={() => handleChangePage(1)}
        >
          1
        </div>
      );
    }

    // Show ellipsis if there's a gap between the first page and startPage
    if (startPage > 2) {
      pages.push(
        <div
          key="ellipsis-start"
          className="page paginationButtonWithoutHover pNo"
        >
          ...
        </div>
      );
    }

    // Add pages from startPage to endPage
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <div
          key={i}
          className={`page paginationButton pNo ${
            i === page ? "active" : ""
          }`}
          onClick={() => handleChangePage(i)}
        >
          {i}
        </div>
      );
    }

    // Show ellipsis if there's a gap between endPage and the last page
    if (endPage < totalPages - 1) {
      pages.push(
        <div
          key="ellipsis-end"
          className="page paginationButtonWithoutHover pNo"
        >
          ...
        </div>
      );
    }

    // Always show the last page
    if (endPage < totalPages) {
      pages.push(
        <div
          key={totalPages}
          className={`page paginationButton pNo ${
            page === totalPages ? "active" : ""
          }`}
          onClick={() => handleChangePage(totalPages)}
        >
          {totalPages}
        </div>
      );
    }

    return pages;
  };

  const handleApply = () => {
    const parsedValue = parseInt(inputValue, 10);
  
    if (isNaN(parsedValue) || parsedValue <= 0 || parsedValue > 100) {
      fire({
        position: "center",
        icon: "info",
        title: "Invalid Page Size!",
        text: "Please enter a number between 1 and 100",
        confirmButtonText: "Ok",
      });
      setInputValue(String(itemsPerPage))
    } else {
      handleItemsPerPageChange(parsedValue);
      handleChangePage(1);
    }
  };

  return (
    <div className="pagination ">
      <div className="result-per-page">
        <div>
          <label htmlFor="resultPerPage">Result per page:</label>
          <input
            id="resultPerPage"
            type="text"
            value={inputValue}
            onChange={handleResultsPerPage}
          />
        </div>
        <div>
          <button className="paginationButton2" onClick={handleApply}>
            Apply
          </button>
        </div>{" "}
      </div>

      <div className="pages">
        <div
          className={`page paginationButton2 ${
            page === 1 ? "disabled" : ""
          }`}
          onClick={handlePrevious}
        >
          <span className="material-icons prev">arrow_back</span>Previous
        </div>
        <div className="paginationButton2 pageOfPages ">
          {page} of {totalPages}
        </div>
        {/* <div className="page paginationButton pNo">1</div>
            <div className="page paginationButton pNo">2</div>
            <div className="page paginationButton pNo">3</div>
            <div className="page paginationButtonWithoutHover pNo">...</div>
            <div className="page paginationButton pNo">8</div>
            <div className="page paginationButton pNo">9</div> */}

        {renderPageNumbers()}
        <div
          className={`page paginationButton2 ${
            page === totalPages ? "disabled" : ""
          } `}
          onClick={handleNext}
        >
          Next <span className="material-icons next">arrow_forward</span>
        </div>
      </div>
    </div>
  );
}

export default Pagination;
