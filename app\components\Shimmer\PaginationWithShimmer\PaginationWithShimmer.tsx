import React from 'react';
import './PaginationWithShimmer.scss';

interface TableWithShimmerProps {
  no_of_rows: number;
  no_of_cols: number;
  colWidths?: number[];
}

const TableWithShimmer: React.FC<TableWithShimmerProps> = ({ no_of_rows, no_of_cols }) => {
  return (
    <div className="shimmer-container">
      {[...Array(no_of_rows)].map((_, rowIndex) => (
        <div key={rowIndex} className="shimmer-row">
          {[...Array(no_of_cols)].map((_, colIndex) => (
            <div key={colIndex} className="shimmer-col"></div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default TableWithShimmer;
