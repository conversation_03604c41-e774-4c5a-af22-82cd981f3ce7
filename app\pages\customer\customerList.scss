@import '/app/styles/variables.scss';


.customerList{
    width: 100%;
    background-color: $white_color1;
    height: auto;

  



  .table-style{
     table{

      
      thead tr{
        

        .customerScore{
          text-align: center;
        }

        
      }

      

      tbody{
        tr{
          .customerDetails{
            .name{
              font-size: 12px;
              font-weight: 700;
              color: $black_color;
            }
            
            .parentName{
              font-size: 10px;
              font-weight: 500;
              color: $black_color;

            }

            .rating{
              padding-top: 5px;
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              gap: 3px;
  
       
              .star{
              color: $rating_color;
              font-size: 13px;
                 }
              .star-unfilled{
              color: $black_color2;
              font-size: 13px;
                        }
                    }

                   

                     .creditScore-progessBar{
                      display: flex;
                      flex-direction: column;
                      align-items: center;
          
                      .creditScore{
                        
                        p{
                          font-size: 11px;
                          color: $black_color2;
                          font-weight: 500;
          
          
                          span{
                            font-size: 12px;
                            font-weight: 600;
                            color: $black_color;
                          }
          
                        }
                      }
          
                      .progressBar{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        gap: 5px;
          
                        .min-max{
                          font-size: 12px;
                          font-weight: 600;
                          color: $black_color3;
                        }
                        .bar{
                          height: 7px;
                          width: 200px;
                          background: linear-gradient(
                to right,
                red,
                orange,
                yellow,
                green
              );
                          position: relative;
                          border-radius: 10px;
          
                          .circleShapeContainer{
                            position: absolute;
                            top: -4px;
                            
                            
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 15px;
                            width: 15px;
                            border-radius: 50%;
                            background-color: green;
                           
          
                            .circle{
                              height: 8px;
                              width: 7px;
                              border-radius: 50%;
                              background-color: white;
                            }
                          }
                        }
                      }
                     }

                     .creditScore-progessBar-inactive{
                      display: flex;
                      flex-direction: column;
                      align-items: center;
          
                      .creditScore{
                        
                        p{
                          font-size: 11px;
                          color: $black_color5;
                          font-weight: 500;
          
          
                          span{
                            font-size: 12px;
                            font-weight: 600;
                            color: $black_color5;
                          }
          
                        }
                      }
          
                      .progressBar{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        gap: 5px;
          
                        .min-max{
                          font-size: 12px;
                          font-weight: 600;
                          color: $black_color4;
                        }
                        .bar{
                          height: 7px;
                          width: 200px;
                          
                          background-color: $black_color4;
                          position: relative;
                          border-radius: 10px;
          
                          .circleShapeContainer{
                            position: absolute;
                            top: -4px;
                            left: 30px;
                            
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 15px;
                            width: 15px;
                            border-radius: 50%;
                            background-color: $black_color4;
                           
          
                            .circle{
                              height: 8px;
                              width: 7px;
                              border-radius: 50%;
                              background-color: $black_color3;
                            }
                          }
                        }
                      }
                     }
           }

           .customerDetails-inactive{
            .name{
              font-size: 12px;
              font-weight: 700;
              color: $black_color5;
            }
            
            .parentName{
              font-size: 10px;
              font-weight: 500;
              color: $black_color5;

            }

            .rating{
              padding-top: 5px;
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              gap: 3px;
  
       
              .star{
              color: $black_color4;
              font-size: 13px;
                 }
              .star-unfilled{
              color: $black_color4;
              font-size: 13px;
                        }
                    }
           }

           .currentStatus{
            font-size: 12px;
            color: #8199B3;
            font-weight: 600;
           }

           .creditScore-progessBar{
            display: flex;
            flex-direction: column;
            align-items: center;

            .creditScore{
              
              p{
                font-size: 11px;
                color: $black_color2;
                font-weight: 500;


                span{
                  font-size: 12px;
                  font-weight: 600;
                  color: $black_color;
                }

              }
            }

            .progressBar{
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: 5px;

              .min-max{
                font-size: 12px;
                font-weight: 600;
                color: $black_color3;
              }
              .bar{
                height: 7px;
                width: 200px;
                background: linear-gradient(
      to right,
      red,
      orange,
      yellow,
      green
    );
                position: relative;
                border-radius: 10px;

                .circleShapeContainer{
                  position: absolute;
                  top: -4px;
                  
                  
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 15px;
                  width: 15px;
                  border-radius: 50%;
                  background-color: green;
                 

                  .circle{
                    height: 8px;
                    width: 7px;
                    border-radius: 50%;
                    background-color: white;
                  }
                }
              }
            }
           }

           

           .creditScore-progessBar-inactive{
            display: flex;
            flex-direction: column;
            align-items: center;

            .creditScore{
              
              p{
                font-size: 11px;
                color: $black_color5;
                font-weight: 500;


                span{
                  font-size: 12px;
                  font-weight: 600;
                  color: $black_color5;
                }

              }
            }

            .progressBar{
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: 5px;

              .min-max{
                font-size: 12px;
                font-weight: 600;
                color: $black_color4;
              }
              .bar{
                height: 7px;
                width: 200px;
                
                background-color: $black_color4;
                position: relative;
                border-radius: 10px;

                .circleShapeContainer{
                  position: absolute;
                  top: -4px;
                  left: 30px;
                  
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 15px;
                  width: 15px;
                  border-radius: 50%;
                  background-color: $black_color4;
                 

                  .circle{
                    height: 8px;
                    width: 7px;
                    border-radius: 50%;
                    background-color: $black_color3;
                  }
                }
              }
            }
           }

        

            .agentName {
              font-size: 11px;
              color: #9C77B2; /* Bright Purple for the font */
              background-color: #F3EDF9; /* Plum - a soft purple background */
              font-weight: 600;
              padding: 2px 9px;
              border-radius: 15px;
              border: 1px solid #CDA1D2; /* Light Purple border, lighter than the font */
            }
            
            .score{
              font-size: 12px;
              color: $green_color2;
              font-weight: 600;
            }

            .totalProduct{
              font-size: 12px;
              color: $black_color;
              font-weight: 600;
            }

            .totalProduct-inactive{
              font-size: 12px;
              color: $black_color4;
              font-weight: 600;
            }

            .viewDetails{
              display: flex;

              a{

              font-size: 12px;
              color: #637CBD;
              cursor: pointer;
              text-underline-offset: 4px;
              font-weight: 600;
             
     
             &:hover{
                  color: darken(#637CBD, 10%);
     
              }
              }
             }

             .user-not-available{
              width: 95px;              
              color: $red_color;
              font-size: 10px;
              font-weight: 500;
              text-align: center;
              

             }

             


        }
      }

      tbody td:last-child {
        padding-right: 20px;
       
      }

      tbody th:last-child {
        padding-right: 20px;
        
      }

           

     
     }
    
     }
}
