"use client";
import "./Sidebar.scss";
import Link from "next/link";
import Logo from "../../../public/images/logo.png";
import Logo2 from "../../../public/images/Vector (3).png";
import Image from "next/image";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

function Sidebar() {
  const { isMenuExpanded, setIsMenuExpanded } = useCommonContext();
  const [isSecondSidebarVisible, setIsSecondSidebarVisible] = useState(false);

  const currentRoute = usePathname();

  useEffect(() => {
    if (isMenuExpanded) {
      setIsSecondSidebarVisible(false); // Hide second sidebar when the main one expands
    } else {
      setTimeout(() => {
        setIsSecondSidebarVisible(true); // Show second sidebar after the first hides
      }, 300); // Delay to match the transition duration of the first sidebar
    }
  }, [isMenuExpanded]);
  // const [isMenuOpen,setIsMenuOpen] = useState<boolean>(false)
  return (
    <div className="side-menu-bar-wrapper">
      <div
        className={`sidebar-mainContainer ${!isMenuExpanded ? "show" : "hide"}`}
      >
        <div className="headerPart">
          <Image src={Logo2} alt="" />
        </div>
        <div className="sidebar-container">
          <div
            className="chevron-right"
            onClick={() => setIsMenuExpanded(true)}
          >
            <span className="material-icons">chevron_right</span>
          </div>
          <ul>
            <li>
              <Link href="/pages/home">
                <div
                  className={currentRoute === "/pages/home" ? "active" : "icon"}
                >
                  <span className="material-icons">home</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/customer">
                <div
                  className={
                    currentRoute === "/pages/customer" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">group</span>
                </div>
              </Link>
            </li>
            <li>
              <Link href="/pages/agent">
                <div
                  className={
                    currentRoute === "/pages/agent" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">support_agent</span>
                </div>
              </Link>
            </li>
            <li>
              <Link href="/pages/route">
                <div
                  className={
                    currentRoute.includes("/pages/route") ? "active" : "icon"
                  }
                >
                  <span className="material-icons">route</span>
                </div>
              </Link>
            </li>
            <li>
              <Link href="/pages/emiCollection">
                <div
                  className={
                    currentRoute === "/pages/emiCollection" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">analytics</span>
                </div>
              </Link>
            </li>
            <li>
              <Link href="/pages/purchaseOrder">
                <div
                  className={
                    currentRoute === "/pages/purchaseOrder" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">dashboard</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/branch">
                <div
                  className={
                    currentRoute === "/pages/branch" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">business</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/users">
                <div
                  className={
                    currentRoute === "/pages/users" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">person</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/report">
                <div
                  className={
                    currentRoute === "/pages/report" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">insert_chart</span>
                </div>
              </Link>
            </li>
          </ul>

          {/* <ul className="secondUL">
            <li>
              <Link href="#">
                <div className="icon">
                  <span className="material-icons">receipt_long</span>
                </div>
              </Link>
            </li>
            <li>
              <Link href="#">
                <div className="icon">
                  <span className="material-icons">account_balance_wallet</span>
                </div>
              </Link>
            </li>
          </ul> */}
        </div>
      </div>

      {!isSecondSidebarVisible && (
        <div
          className={`sidebar2-mainContainer ${
            isMenuExpanded ? "show" : "hide"
          }`}
        >
          <div className="headerPart">
            <div className="image">
              <Image src={Logo} width={100} height={100} alt="" />
            </div>
          </div>
          <div className="sidebar-container2">
            <div className="sidebarHeader">
              <span className="header">MAIN MENU</span>
              <div
                className="chevron-left"
                onClick={() => setIsMenuExpanded(false)}
              >
                <span className="material-icons">chevron_left</span>
              </div>
            </div>
            <ul>
              <li className={currentRoute === "/pages/home" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/home">
                  <span className="material-icons">home</span>Home
                </Link>
              </li>

              <li
                className={currentRoute === "/pages/customer" ? "active" : ""}
                onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/customer">
                  <span className="material-icons">group</span>
                  Customer
                </Link>
              </li>

              <li className={currentRoute === "/pages/agent" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/agent">
                  <span className="material-icons">support_agent</span> Agent
                </Link>
              </li>

              <li className={currentRoute.includes("/pages/route") ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/route">
                  <span className="material-icons">route</span>Route
                </Link>
              </li>

              <li
                className={
                  currentRoute === "/pages/emiCollection" ? "active" : ""
                }
              onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/emiCollection">
                  <span className="material-icons">analytics</span>EMI
                  Collection Overview
                </Link>
              </li>

              <li
                className={
                  currentRoute === "/pages/purchaseOrder" ? "active" : ""
                }
                onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/purchaseOrder">
                  <span className="material-icons">dashboard</span>Purchase
                  Order Overview
                </Link>
              </li>

              <li className={currentRoute === "/pages/branch" ? "active" : ""}>
                <Link href="/pages/branch" onClick={()=>{setIsMenuExpanded(false)}}>
                  <span className="material-icons">business</span>Branch
                </Link>
              </li>

              <li className={currentRoute === "/pages/users" ? "active" : ""}>
                <Link href="/pages/users" onClick={()=>{setIsMenuExpanded(false)}}>
                  <span className="material-icons">person</span>Users
                </Link>
              </li>

              <li className={currentRoute === "/pages/report" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
                <Link href="/pages/report">
                  <span className="material-icons">insert_chart</span>Report
                </Link>
              </li>
            </ul>

            {/* <ul className='secondUL'>
        <div className="header2">
          FINANCE
        </div>
        
            <li><Link href = "#"><span className="material-icons">
            receipt_long
</span>Billing</Link></li>
            <li><Link href = "#"><span className="material-icons">
            account_balance_wallet
</span>Payouts</Link></li>
        </ul> */}
          </div>
        </div>
      )}
    </div>
  );
}

export default Sidebar;
