import axiosInstance from "@/app/api/axiosInstance";
import { reportList } from "./report.model";

export const getReportList = async (
    skip: number,
    itemsPerPage: number,
    search: string,
    report_item:string
    ) : Promise<reportList> => {
        try {
            const queryParams = new URLSearchParams({
              skip : skip.toString(),
              limit : itemsPerPage.toString(),
              search : search,
            });
       
            if (report_item) {
              queryParams.append('report_item', encodeURIComponent(report_item));
            }
       
            const response = await axiosInstance.get<reportList>(
              `/dashboard/reports/?${queryParams.toString()}`
            );
       
            return response.data;
          }catch(error){
            console.error("Error fetching purchase order overview list", error);
            throw new Error("Failed to fetch purchase order overview list")
        }
    }