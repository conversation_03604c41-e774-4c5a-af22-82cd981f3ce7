"use client";
import React from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { useCommonContext } from "@/app/contexts/commonContext";
import "./AddDiscount.scss";
import { addDiscount } from "@/app/pages/customer/customer-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { discountCreate } from "@/app/pages/customer/customer.model";
import { AxiosError } from "axios";
import { ErrorResponseData } from "@/app/pages/agent/page";

interface DiscountCreateProps {
  showAddDiscount: boolean;
  handleCloseDiscountCreate: VoidFunction;
  orderId: number;
}

function AddDiscount({
  showAddDiscount,
  handleCloseDiscountCreate,
  orderId,
}: DiscountCreateProps) {
  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<discountCreate>();

  const onSubmit: SubmitHandler<discountCreate> = async (data) => {
    console.log("Discount Data:", data);
    data.status = "active";
    if(!data.code){
      data.code = 'null'
    }
    try {
      // Simulate API call
      setIsLoading(true);
      await addDiscount(data, orderId);
      reset();
      handleCloseDiscountCreate();
      fire({
        position: "top-right",
        icon: "success", // Use success icon
        title: "discount created",
        text: "The discount is created successfully!",
        autoClose: 2000,
      });
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`add-discount-container ${showAddDiscount ? "show" : ""}`}>
      <div className="add-discount-form">
        <h3>Add Discount</h3>
        <span
          className="material-icons closeIcon"
          onClick={handleCloseDiscountCreate}
        >
          close
        </span>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="code">Code</label>
              <input
                {...register("code")}
                id="code"
                type="text"
                placeholder="Enter discount code"
              />
              <p className="error-message">{errors.code?.message}</p>
            </div>
          </div>

          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="amount">Amount</label>
              <input
                {...register("amount", { required: "Amount is required." })}
                id="amount"
                type="text"
                placeholder="Enter amount"
              />
              <p className="error-message">{errors.amount?.message}</p>
            </div>
          </div>

          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="description">Description</label>
              <input
                {...register("description")}
                id="description"
                type="text"
                placeholder="Enter discount description"
              />
              <p className="error-message">{errors.description?.message}</p>
            </div>
          </div>

          {/* <div className="input-field-container w-100">
          <div className="input-field wf-50">
                <label htmlFor="status">Status</label>
                <div className="input purchase-order">
                  <select
                    {...register("status", {
                      required: "Please select a status.",
                    })}
                    id="status"
                  >
                  
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>

                  
                 
                  </select>
                </div>
                <p className="error-message">{errors.status?.message}</p>
              </div>
          </div> */}

          <div className="SubmitBtn">
            <button className="submitButton" type="submit">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default AddDiscount;
