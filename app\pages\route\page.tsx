"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./routeList.scss";
import Link from "next/link";
import TableWithShimmer from "@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import Image from "next/image";
import RouteCreate from "./components/routeCreate/RouteCreate";
import { useCommonContext } from "@/app/contexts/commonContext";
import { deleteRoute, getRouteList } from "./route-service";
import { route } from "./route.model";
import { formatDate } from "@/app/components/utilities/Pipes/dateFormat";
import { debounce } from "@mui/material";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { DropdownItem } from "../users/page";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";

interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [routeList, setRouteList] = useState<route[]>([]);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(true);
  const [visible, setVisible] = useState<boolean>(false);
  const { setIsLoading } = useCommonContext();
  //filters
  const [selectedDay, setSelectedDay] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };
  const { fire } = useAlert();
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPageNo: number,
      searchKey: string,
      day: string
    ) => {
      try {
        const skip = (pageNo - 1) * itemsPerPageNo;
        setIsTableLoading(true);
        const response = await getRouteList(
          skip,
          itemsPerPageNo,
          searchKey,
          day
        );
        setRouteList(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setIsTableLoading(false);
      }
    },
    [setIsTableLoading]
  );

  useEffect(() => {
    getList(1, 10, "", "");
  }, [getList]);

  const handleReset = () => {
    setSearchValue("");
    setSelectedDay("");
    getList(1, 10, "", "");
  };

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(currentPage, itemsPerPage, searchValue, selectedDay);
  };

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue, selectedDay);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, selectedDay);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const date = event.target.value;

    if (item === "day") {
      setSelectedDay(event.target.value);
      getList(1, itemsPerPage, searchValue, date);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, selectedDay),
    300
  );

  const handleDeleteRoute = (route_id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: `Are you sure you want to delete this route?`,
      text: "This action cannot be undone!",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        try {
          setIsLoading(true);
          await deleteRoute(route_id);
          getList(currentPage, itemsPerPage, searchValue, selectedDay);
          fire({
            position: "top-right",
            icon: "success",
            title: "Route deleted",
            text: "The route is deleted successfully!",
            autoClose: 2000,
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error(err);
        } finally {
          setIsLoading(false);
          getList(currentPage, itemsPerPage, searchValue, selectedDay);
        }
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id == "delete-route") {
      handleDeleteRoute(id);
    }
  };
  return (
    <>
      <div
        className={`routeList overall-list-padding ${visible ? "visible" : ""}`}
      >
        {/* <RouteOverview /> */}
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Route +
          </div>
        </div>

        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <h5>Route Planner</h5>
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>Day {selectedDay ? `(${selectedDay})` : ""}</span>
                    <select
                      className="dropdown"
                      value={selectedDay}
                      onChange={(e) => handleFilterSelect(e, "day")}
                    >
                      <option value="">None</option>
                      <option value="sunday">Sunday</option>
                      <option value="monday">Monday</option>
                      <option value="tuesday">Tuesday</option>
                      <option value="wednesday">Wednesday</option>
                      <option value="thursday">Thursday</option>
                      <option value="friday">Friday</option>
                      <option value="saturday">Saturday</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[
                    `Search "route name"`,
                    `Search "route name"`,
                    `Search "branch name"`,
                    `Search "created by"`,

                  ]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={6}
              />
            ) : (
              <div className="table-style table-vertical-scroll-customerList">
                <table>
                  <thead>
                    <tr>
                      <th>Agent Name</th>
                      <th>Routes</th>
                      <th>Branch</th>
                      <th>Created By</th>
                      <th>Created Date</th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    {routeList && routeList.length > 0 ? (
                      routeList.map((item, index) => {
                        const menuItems = [
                          { id: "delete-route", label: "Delete Route" },
                        ];
                        return (
                          <tr key={index}>
                            <td>
                              <div className="name-profilePic">
                                <div className="profile-pic">
                                  {item.agent_profile_photo ? (
                                    <Image
                                      src={mediaUrl + item.agent_profile_photo}
                                      alt="Profile Picture"
                                      fill
                                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                      style={{ objectFit: "cover" }}
                                    />
                                  ) : (
                                    <> </>
                                  )}
                                </div>
                                <div className="name">
                                  {item.agent_name || "Agent Not Assigned"}
                                </div>
                              </div>
                            </td>

                            <td>
                              {item.route_name ? (
                                <div className="routeDetails">
                                  <span className="day">{item.day}</span>
                                  <div className="route">{item.route_name}</div>
                                  <span className="viewDetails">
                                    <Link
                                      href={`/pages/route/routeDetails/${item.id}`}
                                    >
                                      View Details
                                    </Link>
                                  </span>
                                </div>
                              ) : (
                                <span className="notAssigned">
                                  Route Not Assigned
                                </span>
                              )}
                            </td>

                            <td>
                              {item.branch_name ? (
                                <span className="branchName">
                                  {item.branch_name}
                                </span>
                              ) : (
                                <div className="notAssigned">
                                  Branch Not Assigned
                                </div>
                              )}
                            </td>

                            <td>
                              {item.created_by_user_name ? (
                                <div className="createdBy">
                                  <span className="name">
                                    {item.created_by_user_name}
                                  </span>
                                  <span className="position">
                                    {item.created_by_usertype}
                                  </span>
                                </div>
                              ) : (
                                <span className="noData">No Data</span>
                              )}
                            </td>

                            <td>
                              <span className="createdDate">
                                {formatDate(item.created_at)}
                              </span>
                            </td>

                            <td onClick={(e) => e.stopPropagation()}>
                              <TableMenuTwo
                                items={menuItems}
                                onClick={handleMenuItemClick}
                                id={item.id}
                              />
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={6} className="no-data2">
                          <h5>
                            {selectedDay &&
                              !searchValue &&
                              `There are no routes on "${selectedDay}".`}
                            {searchValue &&
                              !selectedDay &&
                              `No routes match your search for "${searchValue}".`}
                            {!searchValue &&
                              !selectedDay &&
                              "It looks like you don't have any routes yet."}
                            {searchValue &&
                              selectedDay &&
                              `No routes on "${selectedDay}" match your search for "${searchValue}".`}
                          </h5>
                          {(selectedDay || searchValue) && (
                            <button
                              onClick={handleReset}
                              style={{
                                marginLeft: "auto",
                                marginRight: "auto",
                              }}
                              className="submitButton"
                            >
                              <span className="material-icons">
                                restart_alt
                              </span>
                              Reset
                            </button>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {routeList && routeList?.length > 0 && (
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </div>
      <RouteCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
      />
    </>
  );
}

export default Page;
