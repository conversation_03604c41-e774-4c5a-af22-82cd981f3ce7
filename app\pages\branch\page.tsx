"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./branchList.scss";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import BranchCreate from "./components/BranchCreate/BranchCreate";
import { Branch_List_Item } from "./branch.model";
import { disableBranch, fetchBranchList } from "./branch-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import Link from "next/link";
import ViewQR from "./components/ViewQR/ViewQR";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";

export interface DropdownItem {
  id: string;
  label: string;
}

interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [branchList, setBranchList] = useState<Branch_List_Item[]>([]);
  const [showQR, setShowQR] = useState<boolean>(false);
  const [selectedBranchId, setSelectedBranchId] = useState<number | null>(null);
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>("");
  const [visible, setVisible] = useState<boolean>(false);
  //fillters
  const [searchValue, setSearchValue] = useState<string>("");
  const { fire } = useAlert();
  const {userData} = useCommonContext()
  // const handleDeleteBranch = (branchName: string, id: number) => {
  //   fire({
  //     position: "center",
  //     icon: "success",
  //     title: `Are you sure you want to delete ${branchName}?`,
  //     text: "This action cannot be undone!",
  //     confirmButtonText: "yes",
  //     cancelButtonText: "No",
  //     autoClose: false,
  //     onConfirm: async () => {
  //       try {
  //         setIsLoading(true);
  //         await deleteBranch(id);
  //         getList(currentPage, itemsPerPage, searchValue);
  //       } catch (err) {
  //         console.error(err);
  //         setErrors("Failed to delete the branch");
  //       } finally {
  //         setIsLoading(false);
  //       }
  //     },
  //   });
  // };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleEditBranch = (id: number) => {
    setSelectedBranchId(id);
    setShowCreate(true);
  };

  const handleShowQR = (screenshotUrl: string | null) => {
    setShowQR(true);
    setScreenshotUrl(screenshotUrl);
  };

  const handleCloseQR = () => {
    setShowQR(false);
  };

  const { isLoading, setIsLoading } = useCommonContext();

  //fetch branch list
  const getList = useCallback(
    async (pageNo: number, itemsPerPageNo: number, searchKey: string) => {
      const skip = (pageNo - 1) * itemsPerPageNo;

      try {
        setIsLoading(true);
        const response = await fetchBranchList(skip, itemsPerPageNo, searchKey);
        setBranchList(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error("Failed to load branch list", err);
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading]
  );

  //fetch branchlist
  useEffect(() => {
    getList(1, 10, "");
  }, [getList]);

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(currentPage, itemsPerPage, searchValue);
    setSelectedBranchId(null);
  };

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value),
    300
  );

  const handleDisableBranch = (item: DropdownItem, id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: `Are you sure you want to delete this branch?`,
      text: "This action cannot be undone!",
      // title: `Are you sure?`,
      // text: `To ${item.id === 'disable-branch' ? 'disable' : 'enable'} the branch .`,
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        try {
          setIsLoading(true);
          await disableBranch(id);
          getList(currentPage, itemsPerPage, searchValue);
          await fire({
            position: "top-right",
            icon: "success", // Use success icon
            title: `Branch deleted successfully!`,
            autoClose: 2000,
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });

          console.error("Failed to disable/enable the branch", err);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    //console.log(item, id);

    if (item.id == "edit-branch") {
      handleEditBranch(id);
    } else {
      handleDisableBranch(item, id);
    }
  };

  return (
    <>
      <div
        className={`branchList overall-list-padding ${
          visible ? "visible" : ""
        }`}
      > 
          {userData?.user_type === "ManagingDirector" &&
            <div className="addUser">
              <div className="addButton" onClick={handleCreate}>
                  Add New Branch +
              </div>
            </div>
          }
        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <div className="filter-search-container">
                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[`Search "name"`, `Search "branch code"`]}
                />
              </div>
            </div>

            {isLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={7}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Branch Name</th>
                      {/* <th>CURRENT STATUS</th> */}
                      <th>Address</th>
                      <th>Email</th>
                      <th>Phone Number</th>
                      <th>Branch code</th>
                      <th>QR</th>
                      <th>Action</th>
                    </tr>
                  </thead>

                  <tbody>
                    {branchList && branchList.length > 0 ? (
                      branchList.map((data, index) => {
                        const menuItems = [
                          { id: "edit-branch", label: "Edit Branch" },
                          ...(userData?.user_type === "ManagingDirector" ? [{ id: "delete-branch", label: "Delete Branch" }] : []),
                        ];
                        return (
                          <tr key={index}>
                            <td>{data.name}</td>
                            {/* <td>
                          {data.is_active ? (
                            <div className="available">
                              <span>Available</span>
                            </div>
                          ) : (
                            <div className="offline">
                              <span>offline</span>
                            </div>
                          )}
                        </td> */}
                            <td>{data.address}</td>
                            <td>{data.email}</td>
                            <td>{data.phone_number}</td>
                            <td>{data.code}</td>
                            <td>
                              <div className="viewQR">
                                <Link
                                  onClick={() => handleShowQR(data.qr_code)}
                                  href=""
                                >
                                  View QR
                                </Link>
                              </div>
                            </td>

                            <td>
                              <TableMenuTwo
                                items={menuItems}
                                onClick={handleMenuItemClick}
                                id={data.id}
                              />
                              {/* <div className="actionIcons">
                  <span className="material-icons eyeIcon">visibility</span>
                  <span className="material-icons editIcon" onClick={() => {handleEditBranch(data.id)}}>edit</span>
                  <span className="material-icons deleteIcon" onClick={() => { handleDeleteBranch(data.name, data.id) }}>delete</span>
                </div> */}
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={7} className="no-data2">
                          <h5>
                            {searchValue &&
                              `No orders match your search for "${searchValue}".`}
                          </h5>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {branchList && branchList?.length > 0 && (
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </div>
      <BranchCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
        selectedBranchId={selectedBranchId}
      />
      <ViewQR
        screenshotUrl={screenshotUrl}
        showQR={showQR}
        handleCloseQR={handleCloseQR}
      />
    </>
  );
}

export default Page;
