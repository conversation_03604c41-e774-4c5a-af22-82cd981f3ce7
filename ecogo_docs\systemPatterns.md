# EcoGO System Patterns

## Architecture Overview
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **State Management**: React Context API (CommonContext)
- **Form Handling**: React Hook Form with validation
- **HTTP Client**: Axios with interceptors
- **UI Components**: Material-UI + Custom SCSS
- **PWA**: next-pwa for offline capabilities

## Key Design Patterns

### 1. Context Pattern
- **CommonContext**: Global state for loading, menu, user data, screen size
- **AlertProvider**: Centralized alert/notification system
- Provider wrapping in layout.tsx

### 2. Service Layer Pattern
- Separate service files for each module (e.g., emi-service.ts, purchaseOrder-service.ts)
- Axios instance with request/response interceptors
- Consistent error handling across services

### 3. Form Management Pattern
- React Hook Form for form state and validation
- Controller component for complex inputs (Autocomplete)
- Consistent validation error display
- useWatch for reactive form updates

### 4. Component Structure Pattern
```
pages/
  [module]/
    page.tsx (main page)
    [module].model.ts (TypeScript interfaces)
    [module]-service.ts (API calls)
    components/ (module-specific components)
    [module].scss (styles)
```

### 5. Branch Filtering Pattern
- Dummy cache system to avoid repeated API calls
- Customer-based branch filtering
- Dynamic branch list updates based on customer selection
- Fallback to show all branches when no customer selected

### 6. Error Handling Pattern
- Try-catch blocks in async functions
- AxiosError type checking
- Centralized alert system for user feedback
- Loading state management during API calls

### 7. Data Flow Pattern
- Props down, callbacks up
- useCallback for stable function references
- useEffect for side effects and data fetching
- Controlled components with setValue/getValues

## Technical Decisions
- App Router over Pages Router for better performance
- SCSS over CSS-in-JS for styling flexibility
- Material-UI for complex components, custom styles for layout
- Local storage for authentication token
- PWA for mobile-first experience
