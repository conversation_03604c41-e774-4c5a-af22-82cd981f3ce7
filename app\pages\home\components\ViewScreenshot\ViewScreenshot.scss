@import '/app/styles/variables.scss';
.view-screenshot-container{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
   right: 0;
   background-color: rgba(0, 0, 0, 0.7);
   backdrop-filter: blur(3px);
   z-index: 5001;
   display: flex;
   justify-content: center;
   align-items: center;
   opacity: 0;
   z-index: -1;
   transition: opacity 0.3s ease, z-index 0s linear 0.3s;
   overflow-y: auto;

   &.show{
    opacity: 1;
    z-index: 1000;
}


.view-screenshot{
        background-color: $white_color;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 500px;
        padding: 30px 25px;
        position: relative;
        border-radius: 6px;
        margin: 0 20px;

        h3{
            text-align: center;
            margin-bottom: 20px;
            color: darken( $black_color2, 15%);
            font-weight: 600;
            font-size: 18px;
        }

        .closeIcon{
            position: absolute;
            top: 15px;
            bottom: 0;
            right: 10px;
            //left: 0;
            font-size: 15px;
            cursor: pointer;
            font-weight: 600;

        }
            img{
            max-width: 100%;
            max-height: 300px;
            }

                
        // .buttons{
        //     display: flex;
        //     flex-direction: row;
        //     gap: 15px;


        //     .resetButton{
        //         background-color: $primary_color;
        //         color: $white_color1;
        //         font-size: 11px;
        //         padding: 6px 26px;
        //         border-radius: 3px;
        //         border: 0;
        //         cursor: pointer;
        //         transition: background-color 0.3s ease;
            
        //         &:hover{
        //             background-color: darken($primary_color, 10%);     
        //         }
            
            
              
        //     }

        //     .downloadButton{
        //         background-color: $primary_color;
        //         color: $white_color1;
        //         font-size: 11px;
        //         padding: 6px 26px;
        //         border-radius: 3px;
        //         border: 0;
        //         cursor: pointer;
        //         transition: background-color 0.3s ease;
            
        //         &:hover{
        //             background-color: darken($primary_color, 10%);     
        //         }
            
            
              
        //     }
        // }

    

    }
   }