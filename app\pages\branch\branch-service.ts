import axiosInstance from "@/app/api/axiosInstance";
import { Branch_List } from "./branch.model";


//fetch branch
export const fetchBranchList = async (
    skip : number, 
    itemsPerPage : number, 
    search : string, 
    
) : Promise<Branch_List> => {
    
    try{

        const queryParams = new URLSearchParams({
            skip: skip.toString(),
            limit: itemsPerPage.toString(),
            search : search,
        });

        const response = await axiosInstance.get<Branch_List>(`/branches/?${queryParams.toString()}`);
        return response.data;

    }catch(error){
        console.error("Error fetching branch list", error);
        throw new Error("Failed to fetch branch list");
    }
}

//create branch
export const createBranch = async (body:FormData) => {
    try{
        console.log("api called");
        
        const response = await axiosInstance.post("formData/branches/", body);
        return response.data
        
    }catch(error){
        console.error("Error creating new branch", error);
        throw new Error("Failed to create new branch") 
    }
}


//fetch branch by id
export const fetchBranchById = async (branch_id:number) => {
    try{
        const response = await axiosInstance.get(`/branches/${branch_id}`);
        return response.data;

    }catch(error){
        console.error("Error fetching branch by id", error);
        throw new Error("Failed to fetch branch id");
    }
}

//edit branch by id
export const editBranchById = async ( branch_id:number, body:FormData ) => {
    try{
       const response = await axiosInstance.put(`formData/branches/${branch_id}`, body);
       return response;
    }catch(error){
        console.error("Error updating branch data", error);
        throw new Error("Failed to update branch data")
        
    }
}

//delete branch
export const deleteBranch = async ( branch_id:number ) => {
    try{
        const response = await axiosInstance.delete(`/branches/${branch_id}`);
        return response;

    }catch(error){
        console.error("error deleting branch", error);
        throw new Error("Failed to delete the branch")
        
    }

}

//disable branch
export const disableBranch = async ( branch_id:number ) => {
    try{
        const response = await axiosInstance.delete(`/branches/${branch_id}/`);
        return response;

    }catch(error){
        console.error("error disabling branch", error);
        throw new Error("Failed to disable the branch")
        
    }
}
