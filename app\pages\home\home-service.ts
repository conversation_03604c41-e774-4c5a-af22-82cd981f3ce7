import axiosInstance from "@/app/api/axiosInstance";
import { Approval_Details, Bank_Transaction_List } from "./home.model";

//fetch bank transaction
export const fetchBankTransaction = async (
    skip : number ,
    itemsPerPage : number ,
    search : string ,
    status?: string ,
    date?: string) : Promise<Bank_Transaction_List> => {

    try{
        const queryParams = new URLSearchParams({
            skip : skip.toString(),
            limit : itemsPerPage.toString(),
            search : search,
        })
        queryParams.append('transaction_mode', encodeURIComponent('bank'));
        queryParams.append('status', encodeURIComponent('inprogress'));

        // if(status){
        //     queryParams.append('status', encodeURIComponent(status));
        // }


        if(date){
            queryParams.append('date', encodeURIComponent(date));
        }
        const response = await axiosInstance.get<Bank_Transaction_List>(`/dashboard/emi_overview/?${queryParams.toString()}`);
        return response.data;

    }catch(error){
        console.error("Error fetching emi bank transaction response", error);
        throw new Error("Failed to fetch bank transaction response")
    }
}

export const approvalStatusUpdate = async (transaction_id : number, body : Approval_Details) => {
    
    try{
        const response = await axiosInstance.put(`/customer_approve_or_reject/${transaction_id}`, body);
        return response.data;

    }catch(error){
        console.error("Error updating approval status", error);
        throw new Error("Failed to update approval status")
        
    }
}

//header api
export const fetchHeaderItems = async () => {
    try{
        const response = await axiosInstance.get(`/dashboard/header`);
        return response.data;

    }catch(err){
        console.error("Error fetching header items", err);
        throw new Error("Failed to fetch header items")
        
    }
}