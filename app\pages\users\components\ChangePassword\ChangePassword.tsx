import React, { useEffect } from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { User_Password_Change } from "../../users.model";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { changeUserPassword, changeUserPwd } from "../../users-service";
import { AxiosError } from "axios";

interface ChangePasswordProps {
  showChangePwd: boolean;
  handleCloseChangePassword: VoidFunction;
  selectedUserId: number | null;
  handleLogout: VoidFunction;
}

interface ErrorResponseData {
  detail?: string;
}

const ChangePassword: React.FC<ChangePasswordProps> = ({
  showChangePwd,
  handleCloseChangePassword,
  selectedUserId,
  handleLogout,
}) => {
  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<User_Password_Change>();

  useEffect(() => {
    if (!showChangePwd) {
      reset();
    }
  }, [showChangePwd, reset]);

  if (!showChangePwd) return null; // Hide the component if `showChangePwd` is false

  const onSubmit: SubmitHandler<User_Password_Change> = async (data) => {
    //console.log("Form Data:", data);

    try {
      if (selectedUserId) {
        //update user
        setIsLoading(true);
        const res = await changeUserPassword(selectedUserId, data);
        console.log("User password updated", res);

        await fire({
          icon: "success", // Use success icon
          title: "User password updated",
          text: "The user password updated successfully!",
          autoClose:2000
        });

        reset();
      } else {
        //api call
        setIsLoading(true);
        const res = await changeUserPwd(data);
        console.log("User password2 updated", res);

         fire({
          icon: "success", // Use success icon
          title: "User password updated",
          text: "The user password updated successfully!",
          autoClose:2000
        });
        if (res) {
          handleLogout();
        }

        reset();
      }

      handleCloseChangePassword();
    } catch (err) {
      //handleCloseCreate();
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="create-form-overlay show">
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Change Your Password</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseChangePassword}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-100">
                <label htmlFor="password">Password</label>
                <input
                  id="password"
                  type="text"
                  placeholder="Type..."
                  {...register("password", {
                    required: "Please enter the password.",
                  })}
                />
                <p className="error-message">{errors.password?.message}</p>
              </div>
            </div>

            <div className="SubmitBtn">
              <button type="submit" className="submitButton">
                Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
