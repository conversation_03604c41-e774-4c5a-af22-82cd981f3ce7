{"name": "choiceemidashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.1.10", "@svgr/webpack": "^8.1.0", "axios": "^1.7.9", "lodash": "^4.17.21", "next": "^15.1.5", "next-pwa": "^5.6.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-modal": "^3.16.3", "sass": "^1.81.0", "uuid": "^11.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "webpack": "^5.97.1"}}