@import '/app/styles/variables.scss';
.fileUploadField{
align-items: center;
gap: 13px;
}
.addMoreField{
    display: flex;
    justify-content: start;
    padding-bottom: 8px;

    p{
        font-size: 11px;
        text-decoration: underline;
        text-underline-offset: 2px;
        color: $black_color3;
        font-weight: 600;
        transition: color 0.2s ease;
        cursor: pointer;

        &:hover{
            color: darken($black_color3, 10%);
        }
    }
}
.name{
    gap: 13px;
}
.input-field-container-dotted{
    margin-bottom: 12px;

    button{
        background-color: $white_color;
        border: 2px dotted $black_color4;
        padding: 10px 0;
        font-size: 10px;
        font-weight: 500;
        color: $black_color3;
        border-radius: 6px;
    }
}
.fileUpload{
    background-color: $white_color1;
    border: 2px dotted $black_color4;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 1px 0 12px 0;
    margin-bottom: 20px;

    .uploadIcon{
        color: $black_color2;
        margin-bottom: 6px;

        span{
            font-size: 28px;
            cursor: pointer;
        }
    }

    .desc{
        font-size: 9px;
        color: $black_color;
        font-weight: 600;
        padding-bottom: 3px;

        span{
            color: $primary_color;
            text-decoration: underline;
            cursor: pointer;

            &:hover{
                color: darken($primary_color , 10%);
            }
        }

    }

    
    .fileFormat{
        font-size: 8px;
        color: $black_color3;
        font-weight: 600;
    }


}
.SubmitBtn{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}


    
