import axiosInstance from "@/app/api/axiosInstance";
import { editEMiCollection, Emi_Collection_Overview_List, Emi_Collection_Overview_List_Item } from "./emi.model";

//fetch emi collection overview response
export const fetchCollectionHistory = async (
    skip : number ,
    itemsPerPage : number ,
    search : string ,
    status?: string ,
    transaction_mode ?: string ,
    date?: string) : Promise<Emi_Collection_Overview_List> => {

    try{
        const queryParams = new URLSearchParams({
            skip : skip.toString(),
            limit : itemsPerPage.toString(),
            search : search,
        })

        if(status){
            queryParams.append('status', encodeURIComponent(status));
        }

        if(transaction_mode){
            queryParams.append('transaction_mode', encodeURIComponent(transaction_mode));
        }

        if(date){
            queryParams.append('date', encodeURIComponent(date));
        }
        const response = await axiosInstance.get<Emi_Collection_Overview_List>(`/dashboard/emi_overview/?${queryParams.toString()}`);
        return response.data;

    }catch(error){
        console.error("Error fetching emi collection overview response", error);
        throw new Error("Failed to fetch emi collection overview response")
    }
}

export const getEmiCollectionById = async (id:number): Promise<Emi_Collection_Overview_List_Item> => {
    try{
        const response = await axiosInstance.get<Emi_Collection_Overview_List_Item>(`emi_detail/dashboard/${id}/`)
        return response.data
    }catch (error) {
        throw error;
    }
}


export const editEmiCollection = async (id:number | null,body:editEMiCollection) => {
    try{
       const response = await axiosInstance.put(`dashboard/collected_emi/${id}/`,body)
       return response.data
    }catch (error) {
        throw error;
    }
}