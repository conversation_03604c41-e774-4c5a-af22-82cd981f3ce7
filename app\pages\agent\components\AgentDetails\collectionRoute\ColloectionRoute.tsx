import React, { useCallback, useEffect, useState } from "react";
import "./collection-route.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import Link from "next/link";
import { getAgentCollectionRoute } from "../../../agent-service";
import { route } from "@/app/pages/route/route.model";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import { debounce } from "@mui/material";
import SearchBox from "@/app/components/SearchBox/SearchBox";

interface prop {
  agentId: number | null;
}
function ColloectionRoute({ agentId }: prop) {
  const [list, setList] = useState<route[]>([]);
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [totalPage, setTotalPage] = useState<number>(10);

  const handlePageChange = (pageNo: number) => {
    console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue);
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(1, value, searchValue);
  };

  const getList = useCallback(
    async (pageNo: number, itemsPerPage: number, search: string) => {
      const skip = (pageNo - 1) * itemsPerPage;
      setIsTableLoading(true);
      try {
        const api = await getAgentCollectionRoute(
          agentId,
          skip,
          itemsPerPage,
          search
        );
        setList(api.results);
        setPage(api.page);
        setTotalPage(api.total_pages);
      } catch (error) {
        console.log(error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [agentId] // Only recreate when agentId changes
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value),
    300
  );

  const handleRest = () => {
    getList(1, itemsPerPage, "");
  };

  useEffect(() => {
    if (typeof agentId === "number" && !isNaN(agentId)) {
      getList(1, 10, "");
    }
  }, [getList, agentId]);

  return (
    <div className="collection-route-container">
      <div className="table-header-details-page">
        <div className="filter-search-container">
          <SearchBox
            value={searchValue}
            onChange={handleSearchChange}
            placeholders={[
              `Search " agent name"`,
              `Search " route name"`,
              `Search "branch name"`,
              `Search "created by"`,
            ]}
          />
        </div>
      </div>

      <div className="table2">
        <table>
          <thead>
            <tr>
              <th>Routes</th>
              <th>Branch</th>
              <th>Created By</th>
              <th>Created Date</th>
            </tr>
          </thead>
          <tbody>
            {isTableLoading ? (
              <tr>
                <td colSpan={4}>
                  <TableWithShimmer
                    no_of_cols={4}
                    no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                  />
                </td>
              </tr>
            ) : list && list.length > 0 ? (
              list.map((row, index) => (
                <tr key={index}>
                  <td>
                    <div className="routeDetails">
                      <span className="day">{row.day}</span>
                      <p className="route">{row.route_name}</p>
                      <span className="viewDetails">
                        <Link href={`/pages/route/routeDetails/${row.id}`}>
                          View Details
                        </Link>
                      </span>
                    </div>
                  </td>

                  <td>
                    <span className="branchName">{row.branch_name}</span>
                  </td>

                  <td>
                    <div className="createdBy">
                      <div className="name">{row.created_by_user_name}</div>
                      <div className="position">{row.created_by_usertype}</div>
                    </div>
                  </td>

                  <td>
                    <span className="createdDate">
                      {new Date(row.created_at).toLocaleDateString()}
                    </span>
                  </td>

                  {/* <td>
                      <TableMenuTwo
                        items={menuItems}
                        onClick={handleMenuItemClick}
                        id={row.id}
                      />
                    </td> */}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4} className="no-data-collectionRoute">
                  <h5>
                    {searchValue &&
                      `No Route Matches the search ${searchValue}`}
                    {!searchValue && "Looks like there is no route assigned"}
                  </h5>

                  <button
                    onClick={handleRest}
                    style={{ marginLeft: "auto", marginRight: "auto" }}
                    className="submitButton"
                  >
                    <span className="material-icons">restart_alt</span>Reset
                  </button>
                </td>
              </tr>
            )}

            {/* <tr>
        <td>
          <div className="routeDetails">
            <span className="day">
              Monday
            </span>
            <p className='route'>
              Palakkad-Pathiripala-Ottapalam
          </p>
            <span className="viewDetails">
            <Link href={''}>View Details</Link>
            </span>

          </div>
        </td>

        <td>
          <span className="branchName">
            Branch Name A
          </span>
        </td>

        <td>
          <div className="createdBy">
            <div className="name">
                Muhammed Muneer
            </div>

            <div className="position">
                Area manager
            </div>
          </div>
        </td>

        <td>
          <span className="createdDate">
            13-2-2024
          </span>
        </td>

        <td>
        <TableMenu
        data={[] || []}
        index={1}
        showMenu={showMenu}
        handleMenuToggle={handleMenuToggle}
        handleCloseMenu={handleCloseMenu}
/>
</td>

      </tr> */}
          </tbody>
        </table>
      </div>
      {list && list.length > 0 && (
        <div className="pagination-table-container">
          <Pagination
            totalPages={totalPage}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={page}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
}

export default ColloectionRoute;
