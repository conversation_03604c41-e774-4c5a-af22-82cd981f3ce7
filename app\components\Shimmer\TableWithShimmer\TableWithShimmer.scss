@keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  .shimmer-container {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Space between rows */
  }
  
  .shimmer-row {
    height: 40px; /* Set the height of the row */
    display: flex;
    gap: 10px; /* Space between columns */
  }
  
  .shimmer-col {
    width: 100px; /* Set the width of each column */
    height: 100%; /* Inherit height from the row */
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px; /* Optional: Rounded edges */
  }
  
  
  /* Customize specific columns if needed */
  .shimmer-col:nth-child(1) { flex: 2; } /* Example: Wider first column */
  .shimmer-col:nth-child(2) { flex: 1.5; }
  