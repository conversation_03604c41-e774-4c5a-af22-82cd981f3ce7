@import '/app/styles/variables.scss';
.collection-history-container{
    height: calc(100dvh - 85px);
    background-color: $white_color;
    //border-radius: 6px;
    overflow: hidden;

    @media( max-width: $breakpoint-lg){
      height: auto;
    }


    .table2{
      max-height: calc(100dvh - 348px);
        table{
          thead{
            position: sticky;
            top: 0;
            z-index: 2;
          }
            tbody{
                tr{
                    .date-time-invoiceNumber{
                        display: flex;
                        flex-direction: column;
                        white-space: nowrap;
                        
          
                        .dateAndTime{
                          display: flex;
                          flex-direction: row;
                          margin-bottom: 3px;
                          white-space: nowrap;
                          height: fit-content;
                          align-items: center;
                    
                          .date, .time{
                            font-size: 12px;
                            color: $black_color;
                            font-weight: 600;
                            white-space: nowrap;
                            height: fit-content;
                          }
          
                          .date{
                            border-right:  2px solid $black_color;
                            padding-right: 5px;
                          }
          
                          .time{
                            padding-left: 5px;
                          }
          
                        
                         }
          
                         .invoiceNumber{
                          font-size: 10px;
                          color: $black_color3;
                          white-space: nowrap;
                        }
                       }
                    
                       .amount{
                        color: $red_color;
                        font-weight: 600;
                        font-size: 12px;
                        white-space: nowrap;
                    }
                    
                       .agent{
                        color: $black_color2;
                        font-weight: 600;
                        font-size: 12px;
                        white-space: wrap;
                    }
                    .transactionMode{
                      display: flex;
                      flex-direction: column;
                      white-space: nowrap;

                      .modeOfTransaction{
                        display: flex;
                        flex-direction: row;
                        align-items: end;
                        gap: 10px;
                        .mode{
                          font-size: 12px;
                          color: $black_color2;
                          font-weight: 600;
                          margin-bottom: 2px;
                          white-space: nowrap;
                        }
                        
                        .fa-paperclip{
                          color: #1E90FF;
                          font-size: 12px;
                          cursor: pointer;

                      }
                      }
                      .transactionId{
                        font-size: 12px;
                        color: $black_color3;
                        font-weight: 500;
                        white-space: nowrap;
                      }
                  }


                      
                    .remark{
                        white-space: wrap;
                        font-size: 12px;
                        font-weight: 600;
                        color: $black_color3;
        
                        .remark-text-field {
                            input {
                                width: 100%;
                                height: 30px;
                                border: 1px solid $black_color4;
                                border-radius: 3px;
                                padding: 0 0 0 5px;
        
                                &::placeholder{
                                    color: $black_color3;
                                    font-size: 11px;
                                }
                            }
        
                          
                        }
                        }
                }
            }
        }
    }
}
.download-button-container{
  display: flex;
  justify-content: end;
  align-items: center;
  padding-top: 10px;
  padding-right: 10px;
}