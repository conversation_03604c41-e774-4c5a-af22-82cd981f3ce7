@use '/app/styles/variables' as *;

.home-container{

    .border-radius{
        background-color: $white_color;
        border-radius: 13px;
        overflow: hidden;
        margin-top: 25px;
        margin-bottom: 40px;
        padding-bottom: 15px;

        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.5s ease, transform 0.5s ease;

        &.visible{
            opacity: 1;
            transform: translateY(0);
        }


        .bank-transaction-box{
            max-width: 100%;
            background-color: $white_color;
            
            border-radius: 11px;
            overflow: hidden;
            padding: 15px;
        
            
        
        
            .bank-transaction-box-head{
                padding-left: 15px;
                font-weight: 500;
                color: $black_color;
                font-size: 17px;
                margin-bottom: 5px;
                
            }
        
            .bank-transaction-container{
                width: 100%;
                //height: 310px;
                height: calc(100dvh - 416px);
                display: flex;
                flex-direction: column;
                overflow-y: auto;
                overflow-x: auto;
                

                &::-webkit-scrollbar {
                    width: 8px; // Width of the scrollbar
                    height: 8px; // Height for horizontal scrollbar (if any)
                  }
                
                  &::-webkit-scrollbar-track {
                    background: #f0f0f0; // Background color of the scrollbar track
                    border-radius: 10px; // Rounded corners for the track
                  }
                
                  &::-webkit-scrollbar-thumb {
                    background: linear-gradient(45deg, $black_color2, $black_color3); // Gradient thumb
                    border-radius: 10px; // Rounded corners for the thumb
                    border: 2px solid #f0f0f0; // Adds padding between track and thumb
                  }
                
                  &::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(45deg, $black_color, $black_color2); // Thumb color on hover
                  }
                
               
        
                .bankTransaction{
                    min-width: 1050px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    margin: 10px 15px 4px 15px;
                    padding: 10px 25px 10px 13px;
                    background-color: $white_color1;
                    border-radius: 11px;
        
        
                    .profile-name-date-time{
                        width: 20%;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        .profile-pic{
                            position: relative;
                            width: 40px;
                            height: 40px;
                            background-color: $black_color2;
                            border-radius: 50%;
                            margin-right: 8px;
                            overflow: hidden;
                            img{
                                object-fit: contain;
                            }
        
                        }
        
                        
                        .name-date-time{
                            display: flex;
                            flex-direction: column;
        
                            .name{
                                font-size: 13px;
                                color: $black_color;
                                font-weight: 800;
                                margin-bottom: 3px;
                            }
        
                            .date-time{
                                font-size: 11px;
                                font-weight: 500;
                                color: $primary_color;
                            }
                        }
        
        
                    }
        
                    .amount-transactionId{
                        width: 20%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: start;
                       
        
                        .amount{
                            font-size: 15px;
                            font-weight: 800;
                            color: $green_color2;
        
                        }
        
                        .transactionId{
                            font-size: 11px;
                            color: $black_color2;
                        }
                    }
        
                    .buttons{
                        width: 20%;
                        flex: 1;
                        align-items: center;
                        justify-content: center;
                        display: flex;
                        flex-direction: row;
                        gap: 6px;
                        flex-wrap: nowrap;
                        
                   
        
                        .rejectButton{
                         background-color: $white_color1;
        
                         &:hover{
                            background-color: darken($white_color1, 5%);     
                        }
                        }
                    }

                    .inProcess{
                        width: 20%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        p{
                            font-size: 14px;
                            color: #ff9500;
                            font-weight: 600;

                        }
                        
                    }

                    .approved{
                        width: 20%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        p{
                            font-size: 14px;
                            color: $green_color2;
                            font-weight: 600;

                        }
                        
                    }

                    .rejected{
                        width: 20%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        p{
                            font-size: 14px;
                            color: $red_color;
                            font-weight: 600;

                        }
                        
                    }
        
            

                    .remark{
                        width: 30%;
                        display: flex;
                        flex-direction: column;

                        h5{
                            font-size: 12px;
                            color: $black_color2;
                            margin-bottom: 2px;
                            
                        }

                        p{
                            font-size: 12px;
                            color: $black_color2;
                        }
                    }

                    .viewAttachment{
                        width: 10%;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .fa-file{
                            color: $primary_color;
                            font-size: 22px;
                            cursor: pointer;
                            transition: color 0.2s ease;

                            &:hover{
                                color: darken($primary_color, 10%);
                            }
                        }
                    }


                    @media(max-width:950px){
                        .profile-name-date-time{
                          
                            .profile-pic{
                                position: relative;
                                width: 30px;
                                height: 30px;   
                            }
            
                            
                            .name-date-time{
                                display: flex;
                                flex-direction: column;
            
                                .name{
                                    font-size: 11px;
                                   
                                }
            
                                .date-time{
                                    font-size: 9px;
                                   
                                }
                            }
            
            
                        }
            
                        .amount-transactionId{
                          
                           
            
                            .amount{
                                font-size: 13px;
                                font-weight: 700;
                                color: $green_color2;
            
                            }
            
                            .transactionId{
                                font-size: 9px;
                                color: $black_color2;
                            }
                        }

                        .buttons{

                            .acceptButton, .rejectButton{
                             
                                width: 70px;
                                height: 23px;
                                font-size: 10px
                              
                            }
                        }
            
               
    
                        .inProcess{
                        
    
                            p{
                                font-size: 11px;
                                color: #ff9500;
                                font-weight: 600;
    
                            }
                            
                        }
    
                        .approved{
                       
    
                            p{
                                font-size: 11px;
                                color: $green_color2;
                                font-weight: 600;
    
                            }
                            
                        }
    
                        .rejected{
                          
    
                            p{
                                font-size: 11px;
                                color: $red_color;
                                font-weight: 600;
    
                            }
                            
                        }
            
                
    
                        .remark{
                            width: 30%;
                            display: flex;
                            flex-direction: column;
    
                            h5{
                                font-size: 11px;
                                color: $black_color2;
                                margin-bottom: 2px;
                                
                            }
    
                            p{
                                font-size: 10px;
                                color: $black_color2;
                            }
                        }
    
                  

                    }
                }

                .no-data-message{
                    min-height: 300px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 12px;
                    font-weight: 600;
                }
           
        
        
        
            }
        
           
        
        }

        .border-bottom-container{
            width: 100%;
            height: 2px;
            display: flex;
            justify-content: center;
            align-items: center;

            .border-bottom{
            width: 100%;
            height: 2px;
            border-bottom: 1px solid $black_color4;

            }
        }
      }

    .table-header{
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;

        @media (max-width: $breakpoint-sm) {
            padding: 20px 18px;
            align-items: start;
            ;
          }
    }

    

}
.view-file-icon{
    width: 28px;
    cursor: pointer;
    transition:  transform ease-in-out 0.3s;
    &:hover{
        transform: scale(1.1);
    }
}

//media queries
// @media( max-width : $breakpoint-sm ){
//     .home-container{
//         .border-radius{
            
            
//         }
//     }

// }



