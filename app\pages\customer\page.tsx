"use client";
// import CustomerOverview from "@/app/pages/customer/components/CustomerOverview/CustomerOverview";
import Link from "next/link";
import React, { useCallback, useEffect, useState } from "react";
import "./customerList.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import CustomerDetails from "./components/CustomerDetails/CustomerDetails";
import CustomerCreate from "./components/CustomerCreate/CustomerCreate";
import { customerListItem } from "./customer.model";
import {
  deleteCustomer,
  disableCustomer,
  getCustomerList,
} from "./customer-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import { debounce } from "@mui/material";
import TableMenuTwo, {
  DropdownItem,
} from "@/app/components/TableMenuTwo/TableMenuTwo";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";

interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const { fire } = useAlert();
  const [searchValue, setSearchValue] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const { setIsLoading } = useCommonContext();
  const [isTabeleLoading, setIsTableLoading] = useState<boolean>();
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [SelectedCustomerId, setSelectedCustomerId] = useState<number | null>(
    null
  );
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [customerList, setCustomerList] = useState<customerListItem[]>();
  const [status, setStatus] = useState<string>("");
  const [visible, setVisible] = useState<boolean>(false);

  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleShowDetails = (id: number) => {
    setShowDetails(true);
    setSelectedCustomerId(id);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleCloseDetails = () => {
    setShowDetails(false);
    getList(1, itemsPerPage, "", "");
  };

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(currentPage, itemsPerPage, "", "");
  };

  const handleMennuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "delete-customer") {
      fire({
        position: "center",
        icon: "info",
        title: "Are you sure?",
        text: "You want to delete the customer.",
        confirmButtonText: "Ok",
        cancelButtonText: "Cancel",
        onConfirm: () => {
          setIsLoading(true);
          deleteCustomer(id)
            .then((res) => {
              if (res) {
                getList(1, itemsPerPage, "", "");
                setIsLoading(false);
                fire({
                  position: "top-right",
                  icon: "success",
                  title: "customer deleted successfully",
                  autoClose: 2000,
                });
              }
            })
            .catch((error) => {
              const axiosError = error as AxiosError<ErrorResponseData>;
              fire({
                position: "center",
                icon: "error",
                title: "Something went wrong",
                text:
                  axiosError?.response?.data?.detail ||
                  axiosError?.message ||
                  "An unknown error occurred",
                confirmButtonText: "Ok",
                // cancelButtonText: "No",
                onConfirm: () => {
                  // console.log("Confirmed:");
                },
              });
              console.log(error, "error");
              setIsLoading(false);
            });
        },
      });
    } else {
      fire({
        position: "center",
        icon: "info",
        title: "Are you sure?",
        text: `Mark Customer as  ${
          item.id === "disable-customer" ? "Offline" : "Online"
        }  .`,
        ...(item.id === "disable-customer"
          ? { initialValue: "Currently Unavailable" }
          : {}),
        confirmButtonText: "Ok",
        cancelButtonText: "Cancel",
        onConfirm: (value: string) => {
          setIsLoading(true);
          disableCustomer(id, value)
            .then((res) => {
              if (res) {
                getList(1, itemsPerPage, "", "");
                setIsLoading(false);
                fire({
                  position: "top-right",
                  icon: "success",
                  title: `${
                    item.id == "disable-customer"
                      ? "Customer switched offline successfully!"
                      : "Customer switched online successfully!"
                  }`,
                  autoClose: 2000,
                });
              }
            })
            .catch((error) => {
              const axiosError = error as AxiosError<ErrorResponseData>;
              fire({
                position: "center",
                icon: "error",
                title: "Something went wrong",
                text:
                  axiosError?.response?.data?.detail ||
                  axiosError?.message ||
                  "An unknown error occurred",
                confirmButtonText: "Ok",
                // cancelButtonText: "No",
                onConfirm: () => {
                  // console.log("Confirmed:");
                },
              });
              console.log(error, "error");
              setIsLoading(false);
            });
        },
      });
    }
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPage: number,
      searchKey: string,
      status: string
    ) => {
      let statusFilter = "";
      if (status === "available") {
        statusFilter = "true";
      } else if (status === "offline") {
        statusFilter = "false";
      }

      const skip = (pageNo - 1) * itemsPerPage;
      setIsTableLoading(true);

      try {
        const response = await getCustomerList(
          skip,
          itemsPerPage,
          searchKey,
          statusFilter
        );
        setCustomerList(response?.results);
        setCurrentPage(response?.page);
        setTotalPages(response?.total_pages);
      } catch (error) {
        if (error instanceof Error) {
          console.log(error.message);
        } else {
          console.log("An unexpected error occurred.");
        }
      } finally {
        setIsTableLoading(false);
      }
    },
    [setCustomerList, setCurrentPage, setTotalPages]
  );

  useEffect(() => {
    if (!showDetails) {
      setSelectedCustomerId(null);
    }
  }, [showDetails]);

  useEffect(() => {
    getList(1, 10, "", "");
  }, [getList]);

  const handleReset = () => {
    setSearchValue("");
    setStatus("");
    getList(1, itemsPerPage, "", "");
  };

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo, "page changed");
    getList(pageNo, itemsPerPage, searchValue, status);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    getList(1, value, searchValue, status);
  };

  const handleStatusChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setStatus(value);
    getList(1, itemsPerPage, searchValue, value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, status),
    300
  );

  return (
    <>
      <div
        className={`customerList overall-list-padding ${
          visible ? "visible" : ""
        }`}
      >
        {/* <CustomerOverview /> */}
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Customer +
          </div>
        </div>
        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <h5>Customer List</h5>

              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>Status {status ? `(${status})` : ""}</span>
                    <select
                      className="dropdown"
                      value={status}
                      onChange={handleStatusChange}
                    >
                      <option value="">None</option>
                      <option value="available">Available</option>
                      <option value="offline">Offline</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[`Search "name"`, `Search "short title"`]}
                />
              </div>
            </div>

            {isTabeleLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={5}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll-customerList">
                <table>
                  <thead>
                    <tr>
                      <th>CUSTOMER DETAILS</th>
                      <th>CURRENT STATUS</th>
                      <th className="customerScore">CUSTOMER SCORE</th>
                      {/* <th>TOTAL PRODUCT</th> */}
                      <th>DETAILS</th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    {customerList && customerList.length > 0 ? (
                      customerList.map((data, index) => {
                        const menuItems = [
                          {
                            id: data.is_active
                              ? "disable-customer"
                              : "enable-customer",
                            label: data.is_active
                              ? "Set Customer Offline"
                              : "Set Customer Online",
                          },
                          { id: "delete-customer", label: "Delete Customer" },
                        ];
                        return (
                          <tr
                            key={index}
                            onClick={() => handleShowDetails(data.id)}
                          >
                            <td>
                              <div
                                className={`customerDetails ${
                                  data.is_active
                                    ? ""
                                    : "customerDetails-inactive"
                                }`}
                              >
                                <span className="name">{data.name}</span>
                                <p className="parentName">{data.short_title}</p>
                                <p className="parentName">{data.care_of ? 'C/O ' + data.care_of : ''}</p>
                                <div className="rating">
                                  {[...Array(5)].map((_, starIndex) => (
                                    <span
                                      key={starIndex}
                                      className={`material-icons ${
                                        starIndex < data.star_rating
                                          ? "star"
                                          : "star-unfilled"
                                      }`}
                                    >
                                      {starIndex < data.star_rating
                                        ? "star"
                                        : "star_outline"}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </td>

                            <td>
                              {data.is_active ? (
                                <div className="available">
                                  <span>Available</span>
                                </div>
                              ) : (
                                <div className="offline">
                                  <span>offline</span>
                                </div>
                              )}
                            </td>

                            <td>
                              <div
                                className={`${
                                  data.is_active
                                    ? "creditScore-progessBar"
                                    : "creditScore-progessBar-inactive"
                                }`}
                              >
                                <div className="creditScore">
                                  <p>
                                    CREDIT SCORE:{" "}
                                    <span>{data.credit_score}</span>
                                  </p>
                                </div>

                                <div className="progressBar">
                                  <span className="min-max">0</span>
                                  <div className="bar">
                                    <div
                                      className="circleShapeContainer"
                                      style={{
                                        left: `${
                                          ((data.credit_score - 0) /
                                            (1000 - 0)) *
                                          200
                                        }px`,
                                      }}
                                    >
                                      <div className="circle"></div>
                                    </div>
                                  </div>
                                  <span className="min-max">1000</span>
                                </div>
                              </div>
                            </td>

                            {/* <td>
                            <div className="totalProduct">
                              {data.totalProduct}
                            </div>
                          </td> */}

                            <td>
                              {data.status == true ? (
                                <div className="viewDetails">
                                  <Link
                                    href=""
                                    onClick={() => handleShowDetails(data.id)}
                                  >
                                    View Details
                                  </Link>
                                </div>
                              ) : (
                                <div className="user-not-available">
                                  <p style={{ textAlign: "left" }}>
                                    {data.status_message
                                      ? data.status_message
                                      : "User Not Available"}
                                  </p>
                                </div>
                              )}
                            </td>
                            <td onClick={(e) => e.stopPropagation()}>
                              <TableMenuTwo
                                id={data.id}
                                items={menuItems}
                                onClick={handleMennuItemClick}
                              />
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={5} className="no-data-customerList">
                          <h5>
                            {status &&
                              !searchValue &&
                              `There are no customers with the status "${status}".`}
                            {searchValue &&
                              !status &&
                              `No customers match your search for "${searchValue}".`}
                            {!searchValue &&
                              !status &&
                              "It looks like you don't have any customers yet."}
                            {searchValue &&
                              status &&
                              `No customers with the status "${status}" match your search for "${searchValue}".`}
                          </h5>

                          {(status || searchValue) && (
                            <button
                              onClick={handleReset}
                              style={{
                                marginLeft: "auto",
                                marginRight: "auto",
                              }}
                              className="submitButton"
                            >
                              <span className="material-icons">
                                restart_alt
                              </span>
                              Reset
                            </button>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
            {customerList && customerList?.length > 0 && (
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>

        <div></div>
      </div>
      <CustomerDetails
        showDetails={showDetails}
        customerId={SelectedCustomerId}
        handleClose={handleCloseDetails}
      />
      <CustomerCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
      />
    </>
  );
}

export default Page;
