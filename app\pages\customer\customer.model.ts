export interface customerList{
    links:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: customerListItem[]
}

export interface customerListItem{
    id:number
    name: string
    short_title:string
    care_of:string
    star_rating: number
    status: boolean
    credit_score: number
    remark:string
    is_active:boolean
    status_message:string
}

export interface customer{
    id: number
    name: string
    short_title:string
    branch_id:number
    city:string
    address:string
    pincode:number
    son_of:string
    code:string
    phone_number:string
    whatsapp_number:string
    email:string
    phone_number2:string
    wallet_amount:number
    status_message:string
    status:boolean
    star_rating:number
    credit_score:number
    emi_type:string
    emi_period_type:string
    next_emi_due_date: string
    is_new_customer:boolean
}

export interface customer_create{
    name: string
    short_title:string
    city:string
    care_of:string
    address:string
    pincode:number
    son_of:string
    code:string
    phone_number:string
    whatsapp_number:string
    email:string
    phone_number2:string
    profile_photo:File | null
    attachments: number[];
    branch_id: number | null;
}

export interface customer_edit{
    name: string
    short_title:string
    city:string
    care_of:string
    address:string
    pincode:number
    son_of:string
    code:string
    phone_number:string
    whatsapp_number:string
    email:string
    phone_number2:string
    phone_number3:string
    phone_number4:string
    profile_photo:File | null
    star_rating: number
}

export interface post_attachment{
    label:string
    file:Blob
}
export interface getAttachments{
    label:string
    file:string
}

export interface customer_details{
    id:number
    profile_photo:string
    name:string
    short_title:string
    city:string
    address:string
    pincode:number
    son_of:string
    care_of:string
    code:string
    phone_number:string
    whatsapp_number:string
    email:string
    phone_number2:string
    phone_number3:string
    phone_number4:string
    status_message:string
    status:boolean
    star_rating:number
    credit_score:number
    total_order_count:number
    total_amount:number
    balance_amount: number
    wallet_amount: number
    next_emi_due_date: string
    monthly_emi_due_date: number;
    weekly_emi_due_date: string;
    emi_type: string;
    emi_period_type: string;
    attachments:[
        {
            id:number
            title:string
            uploaded_file:string
            customer_id:number
            agent_id:string
            user_id:number
            created_at:string
        }
    ]
    addresses:[
        {
            id:number
            customer_id:number
            address_title:string
            address:string
            latitude:string
            longitude:string
            is_primary:boolean
        }
    ]
}

export interface customer_purchase_order_list{
    total:number
    page:number
    page_size:number
    total_pages:number
    results:customer_purchase_order[]
}

export interface customer_purchase_order{
    id: number
    title:string
    invoice_no:string
    reference_no:string
    total_amount:number
    advance_amount:number
    customer_id:number
    emi_amount:number
    is_accounted:boolean
    emi_type:string
    emi_period:number
    emi_period_type:string
    is_detail_open?:boolean
    items:[
        {
            id:number
            name:string
            qty:number
            amount:number
            order_id:number
        }
    ]
}

export interface purchaseOrderDetail {
    title: string;
    invoice_no: string;
    reference_no: string;
    total_amount: number;
    advance_amount: number;
    customer_id: number;
    created_by: string;
    emi_amount: number;
    emi_type: string;
    emi_period: string; 
    emi_period_type: string;
    monthly_emi_due_date: number;
    weekly_emi_due_date: string;
    purchased_date: string; 
    due_date: string; 
    id: number;
    remark: string;
    items: OrderItem[];
    discount: Discount[];
    attachments: purchaseOrderAttachment[];
  }

  export interface purchaseOrderAttachment{
    id:number
    title:string
    description:string
    uploaded_file:string
    created_at:string
  }
  
  interface OrderItem {
    name: string;
    qty: number;
    amount: number;
    id: number;
  }
  
  export interface Discount {
    date: string; 
    amount: number; 
    description:string;
    code:string
    created_at:string
    applied_by_user_name:string
  }
  
export interface customer_collectioon_overview_list{
    total:number
    page:number
    page_size:number
    total_pages:number
    results:customer_collection_overview[]
}

export interface customer_collection_overview{
    id: number
    created_at:string
    transaction_mode:string
    emi_amount:number
    transaction_id:string
    agent_name:string
    remark:string
    payment_reference_no:string
    screenshot:string
    status:string
}

export interface customer_emi_note_list{
    total:number
    page:number
    page_size:number
    total_pages:number
    results:customer_emi_note[]
}

export interface customer_emi_note{
    id:number
    created_at:string
    agent_id:number
    agent_name:string
    note:string
    reminder_time:string
    attachment:string
}

export interface discountCreate{
   code: string;
   amount: number;
   description: string;
   status: string;
}

export interface attachmentCreate{
    customer_id: number | null;
    title: string | undefined;
    uploadedFile: File | null | undefined;
}