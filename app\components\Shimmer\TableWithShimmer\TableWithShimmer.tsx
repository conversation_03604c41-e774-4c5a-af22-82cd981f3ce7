import React from 'react';
import '/app/components/Shimmer/TableWithShimmer/TableWithShimmer.scss';

interface TableWithShimmerProps {
  no_of_rows: number; // Number of shimmer rows
  no_of_cols: number; // Number of shimmer columns per row
  colWidths?: number[]; // Optional: Array of column width ratios
}

const TableWithShimmer: React.FC<TableWithShimmerProps> = ({
  no_of_rows,
  no_of_cols,
  colWidths = [],
}) => {
  return (
    <div className="shimmer-container">
      {[...Array(no_of_rows)].map((_, rowIndex) => (
        <div key={rowIndex} className="shimmer-row">
          {[...Array(no_of_cols)].map((_, colIndex) => (
            <div
              key={colIndex}
              className="shimmer-col"
              style={{ flex: colWidths[colIndex] || 1 }}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default TableWithShimmer;
