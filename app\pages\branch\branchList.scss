@import '/app/styles/variables.scss';

.branchList{
    //margin-top: 20px;

    .user-list-table-container{
        
        .tableBorder{
            .table-header{
                justify-content: end;

                @media(max-width: 640px){
                    justify-content: start;
                }

                .addNewBranchBtn{
                    padding-left: 20px;
                }
            }

            table{
                tbody{
                    tr{

                   



                        td{
                            font-size: 13px;
                            color: $black_color;
                        }

                        .viewQR{
                            a{
                                font-size: 11px;
                                font-weight: 600;
                                color: $link_color;
                                cursor: pointer;
                                text-decoration: underline;
                                text-underline-offset: 2px;
                               
                       
                               &:hover{
                                    color: darken($link_color, 10%);
                       
                                }
                            }
                        }

                        .actionIcons{
                            .material-icons{
                                font-size: 15px;
                                color: $black_color5;
                                margin-right: 5px;
                            }

                            .eyeIcon, .editIcon, .deleteIcon{
                                transition: color .2s ease;
                                cursor: pointer;

                                &:hover{
                                    color: darken($black_color5, 20%);
                                }

                            }

                        }
                    }
                                        
                }
            }
        }
    }

}

