@import '/app/styles/variables.scss';

.menu {
    position: absolute;
    background-color: $primary_color;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    z-index: 1011;
    padding: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
  
    &::after {
      content: '';
      position: absolute;
      top: 10px;
      right: -7px;
      width: 0;
      height: 0;
      border-top: 10px solid transparent;
      border-bottom: 10px solid transparent;
      border-left: 10px solid $primary_color;
    }
  
    &.show {
      opacity: 1;
      transition: opacity 0.2s ease;
    }
  
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
  
      li {
        padding: 5px 3px;
        font-size: 12px;
        cursor: pointer;
        color: $white_color;
        font-weight: 600;
        border-bottom: 0.1px solid $white_color;
  
        &:last-child {
          border-bottom: none;
        }
  
        &:hover {
          background-color: darken($primary_color, 5%);
          color: $white_color;
        }
      }
    }
  }
  
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1010;
  }
  
  .menu-button {
    cursor: pointer;
    font-size: 18px;
    color: $black_color2;
  }
  