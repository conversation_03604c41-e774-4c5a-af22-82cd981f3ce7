export interface routeList{
    links:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: route[]
}


export interface route{
    page: number;
    route_name: string;
    agent_id : number;
    branch_name : string;
    created_by_user_name : string;
    day : string;
    created_at : string;
    agent_name : string;
    id : number;
    created_by_usertype : string;
    agent_profile_photo : string;
}

export interface route_details{
    route_name: string;
    day: string;
    created_by: number;
    branch_id: number;
    agent_id: number;
    route_customers: customer_route_item[]
}

export interface customer_route_item{
    id:number
    name: string;
    short_title: string;
    priority:number
    profile_photo:string
    location: string;
    address: string;
    is_new_customer:boolean
    emi_type:string
    emi_period_type:string
    next_emi_due_date:string
    star_rating: number;
    branch_id: number;
}

export interface customerList{
    links:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: customer_route_item[]
}

export interface Route_Create{
    id:number
    route_name: string;
    agent_id: number;
    day: string;
    created_by: number;
    branch_id: number;

    // customers: {
    //     customer_id: number;
    //     priority: number;
    //   }[];

    name: string;
}


export interface create_route{
    day: string;
    route_name: string;
    agent_id?: number;
    branch_id?: number;
  };

export interface routeEdit{
    agent_id: number;
    branch_id: number;
    customers: {
        customer_id: number;
        priority: number;
    }[];
    day: string;
    route_name: string;
}

// export interface route{
//     id:string
//     route_name:string
//     agent_id:string
//     day:string
//     created_by: string
//     branch_id:string
// }

export interface  customers {
    customer_id: number;
    priority: number;
  };