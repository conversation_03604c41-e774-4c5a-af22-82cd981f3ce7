"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import "./routeDetails.scss";
import profilePic from "../../../../public/images/profile.png";
import Image from "next/image";
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from "react-beautiful-dnd";
import { usePathname, useRouter } from "next/navigation";
import { useCommonContext } from "@/app/contexts/commonContext";
import {
  editRoute,
  fetchCustomerList,
  getAgentsList,
  getRouteDetails,
} from "../route-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { customer_route_item, route_details, routeEdit } from "../route.model";
import RatingComponent from "@/app/components/Rating/Rating";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { Branch_List_Item } from "../../branch/branch.model";
import { getBrachList } from "../../agent/agent-service";
import { Agent_List_Item } from "../../agent/agent.model";
import { Autocomplete, TextField } from "@mui/material";

function Page() {
  const { fire } = useAlert();
  const navigate = useRouter();
  const { setIsLoading , screenSize } = useCommonContext();
  const [routeDetails, setRouteDetails] = useState<route_details>();
  const [branchList, setBranchList] = useState<Branch_List_Item[]>([]);
  const [agentList, setAgentList] = useState<Agent_List_Item[]>([]);
  const [customerSelected, setCustomerSelected] = useState<number>();
  const [customerError, setCustomerError] = useState<string>("");
  const [selectedCustomers, setSelectedCustomers] = useState<number[]>([]);
  const [customerOption, setCustomerOption] = useState<customer_route_item[]>([]);
  const [customerList, setCustomerList] = useState<customer_route_item[]>([]);
  const [dummyCustomerList, setDummyCustomerList] = useState<customer_route_item[]>([]);
  const [splitRouteCustomers,setSplitRouteCustomers] = useState<customer_route_item[]>();
  const [secondColumnCustomers,setSecondColumnCustomers] = useState<customer_route_item[]>();

  const hasFetched = useRef(false);
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
    register,
  } = useForm<routeEdit>();
  const pathname = usePathname();
  const id: number | null = pathname.split("/").pop()
    ? parseInt(pathname.split("/").pop() as string)
    : null;

  const getCustomerList = useCallback(async () => {
    try {
      const res = await fetchCustomerList();
      const filteredCustomers = res.results.filter(customer => customer.next_emi_due_date);
      setCustomerList(filteredCustomers);
      setDummyCustomerList(filteredCustomers); // Store original data in dummy list
      setCustomerOption(filteredCustomers);
    } catch (err) {
      console.error("error", err);
    }
  }, []);

  const handleCustomerListOrder = useCallback((items: customer_route_item[]) => {
    if (items.length > 5) {
      if (screenSize < 1280) {
        setSplitRouteCustomers(items);
      } else {
        const midIndex = Math.ceil(items.length / 2);
        setSplitRouteCustomers(items.slice(0, midIndex));
        setSecondColumnCustomers(items.slice(midIndex));
      }
    } else {
      setSplitRouteCustomers(items);
    }
  }, [screenSize, setSplitRouteCustomers, setSecondColumnCustomers]);

  // Function to filter customers based on route's branch_id
  const filterCustomersForBranch = useCallback((routeBranchId: number | null) => {
    if (!routeBranchId || dummyCustomerList.length === 0) {
      // If no route branch ID or no dummy data, show all customers
      setCustomerList(dummyCustomerList);
      return;
    }

    // Filter customers to show only those with matching branch_id
    const branchCustomers = dummyCustomerList.filter(customer => customer.branch_id === routeBranchId);
    setCustomerList(branchCustomers);
  }, [dummyCustomerList]);

  // Function to handle branch change and validate customers
  const handleBranchChange = useCallback((newBranchId: number) => {
    if (!routeDetails?.route_customers || routeDetails.route_customers.length === 0) {
      // No customers in route, just update branch and filter customer list
      setValue("branch_id", newBranchId);
      filterCustomersForBranch(newBranchId);
      return;
    }

    // Check if any customers in the route don't match the new branch
    const mismatchedCustomers = routeDetails.route_customers.filter(
      customer => customer.branch_id !== newBranchId
    );

    if (mismatchedCustomers.length > 0) {
      // Show confirmation dialog
      fire({
        position: "center",
        icon: "info",
        title: "Branch Change Warning",
        text: `${mismatchedCustomers.length} customer(s) in this route don't belong to the selected branch. They will be removed from the route. Do you want to continue?`,
        confirmButtonText: "Yes, Change Branch",
        cancelButtonText: "Cancel",
        onConfirm: () => {
          // Remove mismatched customers from route
          const matchingCustomers = routeDetails.route_customers.filter(
            customer => customer.branch_id === newBranchId
          );

          // Update route details with only matching customers
          setRouteDetails(prev => {
            if (!prev) return prev;
            const reorderedCustomers = matchingCustomers.map((customer, index) => ({
              ...customer,
              priority: index + 1,
            }));
            handleCustomerListOrder(reorderedCustomers);
            return {
              ...prev,
              route_customers: reorderedCustomers,
            };
          });

          // Update selected customers
          setSelectedCustomers(matchingCustomers.map(customer => customer.id ?? 0));

          // Update branch and filter customer list
          setValue("branch_id", newBranchId);
          filterCustomersForBranch(newBranchId);

          // Show success message
          fire({
            position: "top-right",
            icon: "success",
            title: "Branch Updated",
            text: `${mismatchedCustomers.length} customer(s) removed from route due to branch mismatch.`,
            autoClose: 3000,
          });
        },
        onCancel: () => {
          // Reset branch selection to current value
          setValue("branch_id", routeDetails.branch_id);
        }
      });
    } else {
      // All customers match the new branch, just update
      setValue("branch_id", newBranchId);
      filterCustomersForBranch(newBranchId);
    }
  }, [routeDetails, setValue, filterCustomersForBranch, fire, handleCustomerListOrder]);

  const getDetails = useCallback(async () => {
    if (hasFetched.current) return;
    setIsLoading(true);
    try {
      const response = await getRouteDetails(id); // Ensure id is defined and available
      setRouteDetails({
        ...response,
        route_customers: response.route_customers.map((customer, index) => ({
          ...customer,
          priority: index + 1,
        })),
      });
      handleCustomerListOrder(response.route_customers)
      setValue("day", response.day);
      setValue("agent_id", response.agent_id);
      setValue("branch_id", response.branch_id);
      setValue("route_name", response.route_name);
      setSelectedCustomers(
        response.route_customers.map((customer) => customer.id ?? 0)
      );
      // Get customer list and then filter by branch
      await getCustomerList();
      // Filter customers based on route's branch after customer list is loaded
      filterCustomersForBranch(response.branch_id);
      hasFetched.current = true;
    } catch (err) {
      console.error(err);
      fire({
        icon: "error",
        title: "Operation Failed",
        text: "Something went wrong. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [id, setIsLoading, fire, setValue, getCustomerList,handleCustomerListOrder, filterCustomersForBranch]);

  useEffect(() => {
    if (id !== null) {
      getDetails();
    } else {
      navigate.push("/pages/route");
    }
  }, [id, navigate, getDetails]);

  const handleCancel = () => {
    fire({
      position: "center",
      icon: "info",
      title: "Are You Sure",
      text: "The current changes will be lost.",
      confirmButtonText: "Ok",
      cancelButtonText: "Cancel",
      onConfirm: () => {
        navigate.push("/pages/route");
      },
    });
  };

  const getBranchLists = async () => {
    try {
      const api = await getBrachList();
      setBranchList(api.results);
    } catch (error) {
      console.log("error", error);
    }
  };

  const getAgentLists = async () => {
    try {
      const api = await getAgentsList();
      setAgentList(api.results);
    } catch (error) {
      console.log("error", error);
    }
  };

  const filterCustomers = useCallback(() => {
    //console.log("Filtering customers...");
    //console.log("Selected Customers:", selectedCustomers);

    const newCustomerList = customerList.filter(
      (customer) => !selectedCustomers.includes(customer.id)
    );

    //console.log("Filtered customer list:", newCustomerList);
    setCustomerOption(newCustomerList);
  }, [customerList, selectedCustomers]);

  useEffect(() => {
    getCustomerList();
    getAgentLists();
    getBranchLists();
  }, [getCustomerList]);

  useEffect(() => {
    filterCustomers();
  }, [selectedCustomers, customerList, filterCustomers]);

  // Filter customers when dummy list is populated and route details are available
  useEffect(() => {
    if (dummyCustomerList.length > 0 && routeDetails?.branch_id) {
      filterCustomersForBranch(routeDetails.branch_id);
    }
  }, [dummyCustomerList, routeDetails?.branch_id, filterCustomersForBranch]);

  const onSubmit: SubmitHandler<routeEdit> = (data) => {
    setIsLoading(true);
    const body = {
      route_name: data.route_name,
      day: data.day,
      agent_id: data.agent_id,
      branch_id: data.branch_id,
      customers:
        routeDetails?.route_customers?.map((customer) => ({
          customer_id: customer.id ?? 0,
          priority: customer.priority,
        })) || [],
    };
    editRoute(id, body)
      .then((res) => {
        if (res) {
          setIsLoading(false);
          navigate.push("/pages/route");
          fire({
            position: "top-right",
            icon: "success",
            title: "Edited Successfully",
            autoClose: 2000,
          });
        }
      })
      .catch((error) => {
        setIsLoading(false);
        fire({
          position: "center",
          icon: "error",
          title: "Some error occured",
          text:
            error?.response?.data?.detail ||
            error?.message ||
            "Some error occured",
          confirmButtonText: "Ok",
          onConfirm: () => {
            // console.log("Confirmed:");
          },
        });
      });
  };

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    if (!destination || destination.index === source.index) return;

    const updatedCustomers = Array.from(routeDetails?.route_customers || []);
    const [movedItem] = updatedCustomers.splice(source.index, 1);
    updatedCustomers.splice(destination.index, 0, movedItem);

    // Recalculate priorities
    const reorderedCustomers = updatedCustomers.map((customer, index) => ({
      ...customer,
      priority: index + 1,
    }));

    setRouteDetails((prev) => {
      if (!prev) return undefined;

      return {
        ...prev,
        route_customers: reorderedCustomers,
      };
    });
    handleCustomerListOrder(reorderedCustomers)
  };

  const handleAddCustomer = () => {
    if (!customerSelected) {
      setCustomerError("Please select a customer before adding.");
      return;
    }

    if (!selectedCustomers.includes(customerSelected)) {
      const selectedCustomer = customerList.find(
        (cust) => cust.id === customerSelected
      );

      if (selectedCustomer) {
        setRouteDetails((prev) => {
          if (!prev) return prev;

          const updatedRouteCustomers = [
            {
              ...selectedCustomer,
              priority: 1, // New customer gets priority 1
            },
            ...prev.route_customers.map((cust) => ({
              ...cust,
              priority: cust.priority + 1, // Shift existing customers down
            })),
          ];
          handleCustomerListOrder(updatedRouteCustomers)
          return {
            ...prev,
            route_customers: updatedRouteCustomers,
          } as route_details;
        });

        // Add to selected customers
        setSelectedCustomers((prevSelectedCustomers) => [
          ...prevSelectedCustomers,
          customerSelected,
        ]);

        // Customer will be filtered out automatically by the filterCustomers useEffect
        // No need to manually remove from customerList as it's managed by branch filtering

        // Clear selection and error
        setCustomerSelected(undefined);
        setCustomerError("");
      }
    } else {
      setCustomerError("This customer has already been added.");
    }
  };


  const handleDeleteCustomer = (customerId: number | undefined) => {
    fire({
      position: "center",
      icon: "info",
      title: "Are You Sure",
      text: "Remove the customer from the route?",
      confirmButtonText: "Ok",
      cancelButtonText: "Cancel",
      onConfirm: () => {
        setSelectedCustomers((prev) => prev.filter((id) => id !== customerId));

        // Remove from route customers
        setRouteDetails((prev) => {
          if (!prev) return prev;

          const updatedRouteCustomers = prev.route_customers.filter(
            (customer) => customer.id !== customerId
          );

          // Recalculate priorities after removal
          const reorderedCustomers = updatedRouteCustomers.map(
            (customer, index) => ({
              ...customer,
              priority: index + 1,
            })
          );

          handleCustomerListOrder(reorderedCustomers)

          return {
            ...prev,
            route_customers: reorderedCustomers,
          };
        });

        // Customer will be automatically restored to customerOption by the filterCustomers useEffect
        // since they're no longer in selectedCustomers
      },
    });
  };

  return (
    <div className="route-details-container">
      <div className="routeDetailsHeader">
        <div className="routeListHead">
          <h5>{routeDetails?.day}</h5>
          <h3>{routeDetails?.route_name}</h3>
        </div>
      </div>

      <div className="routeMapCard-filter-container">
        {routeDetails?.route_customers &&
        routeDetails?.route_customers.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className="routeMapCardContainer">
              <div
                className="routeMapColumns"
                style={{ display: "flex", gap: "10px" }}
              >
                {/* First column */}
                <Droppable droppableId="routeMapColumn1" direction="vertical">
                  {(provided) => (
                    <div
                      className="routeMapColumn"
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                    >
                      {splitRouteCustomers?.map((data, index) => (
                        <Draggable
                          key={data.id}
                          draggableId={String(data.id)}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              className="verticalLine-routeMapCard"
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div className="circleShape-verticalLine">
                                <div className="circleShape">
                                  <div className="shape">{data.priority}</div>
                                </div>
                                <div className="line" />
                              </div>
                              <div className="routeMapCard">
                                <div className="delete-button-container-route">
                                  <button
                                    className="delete-bttn"
                                    onClick={() =>
                                      handleDeleteCustomer(data.id)
                                    }
                                  >
                                    <div className="material-icons">delete</div>
                                  </button>
                                </div>
                                {data.is_new_customer &&
                                  <div className="recatangle-indicater-container">
                                    <div className="recatangle-indicater">
                                      <span>New</span>
                                    </div>
                                  </div>
                                }
                                <div className="profilePic-name-rating">
                                  <div className="profile-pic">
                                    <Image
                                      src={
                                        data.profile_photo
                                          ? mediaUrl + data.profile_photo
                                          : profilePic
                                      }
                                      alt={`${data.name}'s Profile Picture`}
                                      layout="fill"
                                      objectFit="cover"
                                      objectPosition="center"
                                      loading="eager"  // Set lazy loading to false
                                    />
                                  </div>
                                  <div className="name-rating">
                                    <div className="name">{data.name}</div>
                                    <div className="rating">
                                      <RatingComponent
                                        rating={data.star_rating}
                                        isReadOnly={true}
                                        size="small"
                                      />
                                    </div>
                                  </div>
                                </div>
                                <div className="parent-detail">
                                  <p>{data.short_title}</p>
                                </div>
                                <div className="location-detail">
                                  <span className="location">
                                    {data.location}
                                  </span>
                                  <span className="locationIcon">
                                    <span className="material-icons">
                                      location_on
                                    </span>
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>

                {/* Second column */}
                <Droppable droppableId="routeMapColumn2" direction="vertical">
                  {(provided) => (
                    <div
                      className="routeMapColumn"
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                    >
                      {secondColumnCustomers?.map((data, index) => (
                        <Draggable
                          key={data.id}
                          draggableId={String(data.id)}
                          index={index + (splitRouteCustomers?.length || 0)}
                        >
                          {(provided) => (
                            <div
                              className="verticalLine-routeMapCard"
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div className="circleShape-verticalLine">
                                <div className="circleShape">
                                  <div className="shape">{data.priority}</div>
                                </div>
                                <div className="line" />
                              </div>
                              <div className="routeMapCard">
                                <div className="delete-button-container-route">
                                  <button
                                    className="delete-bttn"
                                    onClick={() =>
                                      handleDeleteCustomer(data.id)
                                    }
                                  >
                                    <div className="material-icons">delete</div>
                                  </button>
                                </div>
                                {data.is_new_customer &&
                                  <div className="recatangle-indicater-container">
                                    <div className="recatangle-indicater">
                                      <span>New</span>
                                    </div>
                                  </div>
                                }
                                <div className="profilePic-name-rating">
                                  <div className="profile-pic">
                                    <Image
                                      src={
                                        data.profile_photo
                                          ? mediaUrl + data.profile_photo
                                          : profilePic
                                      }
                                      alt={`${data.name}'s Profile Picture`}
                                      layout="fill"
                                      objectFit="cover"
                                      objectPosition="center"
                                      loading="eager"  // Set lazy loading to false
                                    />
                                  </div>
                                  <div className="name-rating">
                                    <div className="name">{data.name}</div>
                                    <div className="rating">
                                      <RatingComponent
                                        rating={data.star_rating}
                                        isReadOnly={true}
                                        size="small"
                                      />
                                    </div>
                                  </div>
                                </div>
                                <div className="parent-detail">
                                  <p>{data.short_title}</p>
                                </div>
                                <div className="location-detail">
                                  <span className="location">
                                    {data.location}
                                  </span>
                                  <span className="locationIcon">
                                    <span className="material-icons">
                                      location_on
                                    </span>
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
            </div>
          </DragDropContext>
        ) : (
          <div className="no-details-found"><h3>
            No customers have been assigned to this route.</h3></div>
        )}

        <div className="filter">
          <div className="select-input-field input-dropDown wf-100">
            <label htmlFor="customer_id">Add Customer</label>
            <Autocomplete
              id="customer_id"
              options={customerOption}
              getOptionLabel={(option) => option?.name}
              value={
                customerOption.find(
                  (option) => option.id === customerSelected
                ) || null
              }
              onChange={(_, value) => {
                setCustomerSelected(value?.id);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select Customer"
                  variant="outlined"
                  slotProps={{
                    input: {
                      ...params.InputProps,
                      style: {
                        padding: "2px",
                        borderRadius: "6px",
                        backgroundColor: "#ffffff",
                      },
                    },
                  }}
                  sx={{
                    "& .MuiInputBase-input::placeholder": {
                      opacity: 1,
                    },
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        border: "2px solid #D6D6D6",
                      },
                      "&:hover fieldset": {
                        border: "2px solid #D6D6D6", // No color change on hover
                      },
                      "&.Mui-focused fieldset": {
                        border: "2px solid #1E1E1E", // No color change on focus
                      },
                      "&.Mui-error fieldset": {
                        border: "2px solid #D6D6D6", // Prevent red border when there's an error
                      },
                      "&.Mui-focused.Mui-error fieldset": {
                        border: "2px solid #1E1E1E", // Border color on focus even when error=true
                      },
                      "& input": {
                        padding: "10px", // Adjust input padding as needed
                      },
                    },
                  }}
                />
              )}
            />
            {customerError && (
              <p className="error-message" style={{ color: "#f44336" }}>
                {customerError}
              </p>
            )}
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <button
                onClick={handleAddCustomer}
                className="submitButton"
                style={{ marginTop: "10px" }}
              >
                + Add Customer
              </button>
            </div>
            <div className="divider">
              <span></span>
            </div>

            {/* Show error message if the field is empty and not selected */}
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="select-input-field input-dropDown wf-100">
              <label htmlFor="route-name">Route Title</label>
              <input
                id="route-name"
                className="input"
                type="text"
                placeholder="Type..."
                {...register("route_name", {
                  required: "Please enter the route title.",
                })}
              />
              <p className="error-message">{errors.route_name?.message}</p>
            </div>
            <div className="select-input-field input-dropDown wf-100">
              <label htmlFor="route-name">Day</label>
              <div className="input">
                <select
                  id="day"
                  {...register("day", {
                    required: " Please select a day for the route. ",
                  })}
                >
                  <option value="Sunday">Sunday</option>
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                </select>
              </div>
              <p className="error-message">{errors.day?.message}</p>
            </div>
            <div className="select-input-field input-dropDown wf-100">
              <label htmlFor="agent">Select Agent</label>

              <Controller
                name="agent_id"
                control={control}
                rules={{ required: "Please select a agent." }} // Adding required rule with custom message
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    id="agent_id"
                    options={agentList}
                    getOptionLabel={(option) => option?.name}
                    value={
                      agentList.find(
                        (agent) => agent.id === getValues("agent_id")
                      ) || null
                    }
                    onChange={(_, value) => {
                      setValue("agent_id", value?.id ?? 0);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select Agent"
                        error={!!errors?.agent_id}
                        variant="outlined"
                        slotProps={{
                          input: {
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          },
                        }}
                        sx={{
                          "& .MuiInputBase-input::placeholder": {
                            opacity: 1, // Ensures the placeholder is fully visible
                          },
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              border: "2px solid #D6D6D6", // Default border color is black with 2px width
                            },
                            "&:hover fieldset": {
                              border: "2px solid #D6D6D6", // No color change on hover
                            },
                            "&.Mui-focused fieldset": {
                              border: "2px solid #1E1E1E", // No color change on focus
                            },
                            "&.Mui-error fieldset": {
                              border: "2px solid #D6D6D6", // Prevent red border when there's an error
                            },
                            "&.Mui-focused.Mui-error fieldset": {
                              border: "2px solid #1E1E1E", // Border color on focus even when error=true
                            },
                            "& input": {
                              padding: "10px", // Adjust input padding as needed
                            },
                          },
                        }}
                      />
                    )}
                  />
                )}
              />

              {/* Show error message if the field is empty and not selected */}
              {errors?.agent_id && (
                <p className="error-message" style={{ color: "#f44336" }}>
                  {errors?.agent_id?.message}
                </p>
              )}
            </div>
            <div className="select-input-field input-dropDown wf-100">
              <label htmlFor="customers">Select Branch</label>
              <Controller
                name="branch_id"
                control={control}
                rules={{ required: "Please select a branch." }} // Adding required rule with custom message
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    id="branch_id"
                    options={branchList}
                    getOptionLabel={(option) => option?.name}
                    value={
                      branchList.find(
                        (option) => option.id === getValues("branch_id")
                      ) || null
                    }
                    onChange={(_, value) => {
                      if (value?.id) {
                        handleBranchChange(value.id);
                      }
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select Branch"
                        error={!!errors?.branch_id}
                        variant="outlined"
                        slotProps={{
                          input: {
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          },
                        }}
                        sx={{
                          "& .MuiInputBase-input::placeholder": {
                            opacity: 1, // Ensures the placeholder is fully visible
                          },
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              border: "2px solid #D6D6D6", // Default border color is black with 2px width
                            },
                            "&:hover fieldset": {
                              border: "2px solid #D6D6D6", // No color change on hover
                            },
                            "&.Mui-focused fieldset": {
                              border: "2px solid #1E1E1E", // No color change on focus
                            },
                            "&.Mui-error fieldset": {
                              border: "2px solid #D6D6D6", // Prevent red border when there's an error
                            },
                            "&.Mui-focused.Mui-error fieldset": {
                              border: "2px solid #1E1E1E", // Border color on focus even when error=true
                            },
                            "& input": {
                              padding: "10px", // Adjust input padding as needed
                            },
                          },
                        }}
                      />
                    )}
                  />
                )}
              />

              {/* Show error message if the field is empty and not selected */}
              {errors?.branch_id && (
                <p className="error-message" style={{ color: "#f44336" }}>
                  {errors?.branch_id?.message}
                </p>
              )}
            </div>
            {/* <div className="select-input-field input-dropDown wf-100">
              <label htmlFor="customers">Select Customers</label>

              <Autocomplete
                multiple
                id="customers"
                options={customerList}
                getOptionLabel={(option) => option?.name}
                value={customerList.filter((customer) =>
                  selectedCustomers.includes(customer.id)
                )}
                onChange={(_, value) => {
                  const selectedIds = value.map((customer) => customer.id);
                  setSelectedCustomers(selectedIds);
                }}
                renderOption={(props, option) => (
                  <ListItem
                    {...props}
                    key={option.id}
                    secondaryAction={
                      selectedCustomers.includes(option.id) && (
                        <IconButton
                          edge="end"
                          onClick={(event) => {
                            event.stopPropagation();
                            setCustomerList(
                              customerList.filter((c) => c.id !== option.id)
                            );
                            setSelectedCustomers(
                              selectedCustomers.filter((id) => id !== option.id)
                            );
                          }}
                        >
                          <div
                            style={{ color: "red" }}
                            className="material-icons"
                          >
                            delete
                          </div>
                        </IconButton>
                      )
                    }
                  >
                    <ListItemText primary={option.name} />
                  </ListItem>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select Customers"
                    variant="outlined"
                  />
                )}
              />

              {errors?.customers && (
                <p className="error-message" style={{ color: "#f44336" }}>
                  {errors?.customers?.message}
                </p>
              )}
            </div> */}

            <div className="saveBtn" style={{ gap: "10px" }}>
              <button
                type="button"
                onClick={handleCancel}
                className="closeButton"
              >
                Cancel
              </button>
              <button type="submit" className="closeButton">
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default Page;
