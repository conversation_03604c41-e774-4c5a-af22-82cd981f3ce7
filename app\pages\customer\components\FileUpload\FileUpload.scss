@use "sass:color";
@use '/app/styles/variables' as *;
@import '/app/styles/variables.scss';


.input-field-insertButton{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 14px;
  }

/* Container for the file upload component */
.file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    font-family: Arial, sans-serif;
  }
  
  /* Wrapper for the input field and Add Document button */
  .input-button-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

 
     
  
  /* Input field styling */
  .input-field{
    display: flex;
    flex-direction: column;
    margin-bottom: 4px;
   

    label{
        font-size: 11px;
        color: $black_color2;
        font-weight: 600;
        margin-bottom: 5px;
    }

    input{
        padding: 8px;
        border: 2px solid $black_color4;
       border-radius: 6px;
       

       &::placeholder{
        color: $black_color4;
        font-size:11px;
       }
    }

    
}
  
  /* Add Document button styling */
 
  /* Modal content styling */
  .modal-content {
    position: fixed; /* Use fixed positioning to keep the modal centered */
    top: 50%; /* Center vertically */
    left: 50%; /* Center horizontally */
    transform: translate(-50%, -50%); /* Adjust position */
    background-color: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1001; /* Ensure modal is on top */
    
   
  }
  
  /* Modal overlay styling */
  .modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed; /* Ensure the overlay covers the entire screen */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000; /* Overlay should be below the modal */
  }
  
  /* Modal header styling */
  .modal-header {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;

  }
  
  /* Dropzone styling */
  .dropzone {
    border: 2px dashed #39c0f4;
    border-radius: 5px;
    padding: 1.5rem;
    text-align: center;
    color: #666;
    cursor: pointer;
    transition: border-color 0.3s;
  
    &:hover {
      border-color: #30a4d1;
    }
  }
  
  /* Cancel button styling */
  .cancel-button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #ff4d4d;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  
    &:hover {
      background-color: #e04343;
    }
  }
  
  .uploadedFiles-inputField{
    width: 100%;
  

    h5{
      font-size: 12px;
      color: $black_color;
      text-decoration: underline;
      padding-bottom: 5px;
    }
    .uploaded-files {

   
          
        h4 {
          font-size: 1.25rem;
          font-weight: bold;
          color: #333;
          margin-bottom: 1rem;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
      
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          display: flex;
          flex-wrap: wrap;
          gap: 13px;
      
        }
      
        .uploaded-file-item {
            width: 48%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background-color: $white_color;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            border: 2px solid $black_color4;
          }
          
          .file-info {
            display: flex;
            align-items: center;
          }
          
          .file-name {
            margin-right: 1rem;
            font-size: 14px;
          }
          
          .remove-file {
            padding: 5px;
            background-color: #ff4d4d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 8px;
            transition: background-color 0.3s;

            .fa-trash{
                font-size: 10px;
            }
          
            &:hover {
              background-color: #e04343;
            }
          }
          
      }

    .inputField{
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 13px;
    }
  }
  /* Uploaded files section styling */
  
  

  -container{
    display: flex;
    justify-content: center;

}

.error-message {
    color: red;
    font-size: 0.7rem;
    margin-top: 0.5rem;
  }

  .fileUpload{
    background-color: $white_color1;
    border: 2px dotted $black_color4;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 15px 30px;
    //margin-bottom: 30px;

    .uploadIcon{
        color: $black_color2;
        margin-bottom: 6px;

        span{
            font-size: 38px;
            cursor: pointer;
        }
    }

    .desc{
        font-size: 13px;
        color: $black_color;
        font-weight: 600;
        padding-bottom: 3px;

        span{
            color: $primary_color;
            text-decoration: underline;
            cursor: pointer;

            &:hover{
                //color: darken($primary_color , 10%);
                color: color.scale($primary_color, $lightness: -10%);

            }
        }

    }

    
    .fileFormat{
        font-size: 9px;
        color: $black_color3;
        font-weight: 600;
    }


}

.SubmitBtn{
  margin-top: 25px;
}

//media queries

@media(max-width: $breakpoint-md){
  .fileUpload .desc {
    font-size: 11px;
  }

}

@media(max-width: $breakpoint-sm){
  .fileUpload .desc {
    font-size: 9px;
  }

  .modal-content{
    width: 90%;
  }

}

 
  