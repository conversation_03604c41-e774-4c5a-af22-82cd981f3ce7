export interface User_List {
    links:{
              next: string
              previous: string
          }
          total:number
          page:number
          page_size:number
          total_pages:number
          results: User_List_Item[]   
}

export interface User_List_Item {
    name: string;
    email: string;
    phone: string;
    address: string;
    user_type: string;
    username: string;
    user_id: number;
    created_at: string;
    updated_at: string;
    branch_name: string;
    is_active: boolean;
}

export interface User_Create {
    name: string;
    email: string;
    phone: string;
    address: string;
    user_type: string;
    username: string;
    password: string;
    role: string;
    branch_id: number | null;
}

export interface User_Password_Change{
    password: string;
}

export interface Branch{
    address: string;
    code: string;
    email: string;
    id: number;
    name: string;
    phone_number: number;
}