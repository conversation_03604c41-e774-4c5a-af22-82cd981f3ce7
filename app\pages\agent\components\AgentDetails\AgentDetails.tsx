import React, { useCallback, useEffect, useRef, useState } from "react";
import "./AgentDetails.scss";
import Image from "next/image";
import ColloectionRoute from "./collectionRoute/ColloectionRoute";
import WalletTransactions from "./walletTransactions/WalletTransactions";
import AgentActivity from "./AgentActivity/AgentActivity";
import { useCommonContext } from "@/app/contexts/commonContext";
import { agentDetails } from "../../agent.model";
import { getAgentDetail } from "../../agent-service";
import AgentEmiCollection from "./agentEmiCollection/AgentEmiCollection";
import AgentCreate from "../AgentCreate/AgentCreate";
import { useRouter } from "next/navigation";

interface agentDetailsProps {
  showDetails: boolean;
  agentId: number;
  handleClose: VoidFunction;
}

function AgentDetails({
  showDetails,
  agentId,
  handleClose,
}: agentDetailsProps) {
  const route = useRouter();
  const [activeMenuIndex, setActiveMenuIndex] = useState<number>(0);
  const [detail, setDetail] = useState<agentDetails>();
  const [showEdit, setShowEdit] = useState<boolean>(false);
  const { setIsLoading } = useCommonContext();
  const detailContainerRef = useRef<HTMLDivElement | null>(null)
  const media_bae_url = process.env.NEXT_PUBLIC_MEDIA_PATH;
  const handleMenuClick = (index: number) => {
    setActiveMenuIndex(index);
  };

  const getDetail = useCallback(async () => {
    try {
      setIsLoading(true);
      const api = await getAgentDetail(agentId);
      setDetail(api);
    } catch (error) {
      console.log(error, "error");
    } finally {
      setIsLoading(false);
    }
  }, [agentId, setIsLoading]);

  useEffect(() => {
    if (showDetails) {
      getDetail();
    }
  }, [showDetails, getDetail]);

  const handleEditClose = () => {
    setShowEdit(false);
    getDetail();
  };

  const handleDetailOverFlow = (state : boolean) => {
    if(detailContainerRef.current){
      if(state){
        detailContainerRef.current.style.overflow = 'hidden'

      }else{
        detailContainerRef.current.style.overflowY = 'auto'

      }
    }
  }

  useEffect(() => {

    handleDetailOverFlow(showEdit)

  }, [detailContainerRef, showEdit])

  return (
    <>
      <div ref={detailContainerRef} className={`agent-details-container ${showDetails ? "show" : ""}`}>
        <div className="agentDetailsHeader">
          <p>
            {" "}
            <span className="active" onClick={() => route.push("/pages/home/")}>
              Dashboard
            </span>
            {">"}
            <span className="active" onClick={handleClose}>
              AgentList
            </span>
            {">"}
            <span>Agent Details</span>
          </p>
  
          <div>
            <button className="closeButton" onClick={handleClose}>
              Close
            </button>
          </div>
        </div>
  
        <div className="user-details-transaction-details-container">
          <div className="user-details">
            <div className="user-profile">
              <div className="profilePicture">
                {/* <Image
                  src={detail?.profile_photo? `${media_bae_url + detail?.profile_photo}` : ''}
                  alt="profile picture"
                  fill
                  className="profileImage"
                /> */}
  
                {media_bae_url && detail?.profile_photo ? (
                  <Image
                    src={
                      detail?.profile_photo
                        ? `${media_bae_url + detail?.profile_photo}`
                        : ""
                    }
                    alt="profile picture"
                    fill
                    className="profileImage"
                  />
                ) : (
                  <p>Unavailable</p>
                )}
              </div>
              <div className="editBtn">
                <span
                  className="material-icons-outlined"
                  onClick={() => setShowEdit(true)}
                >
                  edit
                </span>
              </div>
              {!detail?.is_active && (
                <div className="offline-status-message">
                  <span>{detail?.status_message}</span>
                </div>
              )}
              <div className="username">
                <div></div>
                {detail?.name}
              </div>
              <p className="address">{detail?.agent_code}</p>
            </div>
            <div className="user-additional-details">
              <div className="detail detail1">
                <div className="icon">
                  <span className="material-icons-outlined">lock</span>
                </div>
                <div className="content">
                  <span className="label">User ID</span>
                  <span className="value">0000{detail?.id}</span>
                </div>
              </div>
  
              <div className="detail detail2">
                <div className="icon">
                  <span className="material-icons-outlined">mail</span>
                </div>
                <div className="content">
                  <span className="label">Email</span>
                  <span className="value">{detail?.email}</span>
                </div>
              </div>
  
              <div className="detail detail3">
                <div className="icon">
                  <span className="material-icons-outlined">call</span>
                </div>
  
                <div className="content">
                  <span className="label">Phone Number</span>
                  <span className="value">{detail?.phone}</span>
  
                  {/* <Link href="more">
            more...
            </Link> */}
                </div>
              </div>
  
              <div className="detail detail4">
                <div className="icon">
                  <span className="material-icons-outlined">location_on</span>
                </div>
                <div className="content ">
                  <span className="label ">Permanent address</span>
                  <span className="value permanentAddress">
                    {detail?.address}
                  </span>
                  {/* <Link href="#more">
          more...
          </Link> */}
                </div>
              </div>
  
              <div className="detail detail5">
                <div className="icon">
                  <span className="material-icons-outlined">access_time</span>
                </div>
                {detail?.last_login && (
                  <div className="content ">
                    <span className="label">Last Login</span>
                    <p className="value lastLogin">
                      <span className="date">
                        {new Date(detail?.last_login).toLocaleDateString()}
                      </span>
                      <span className="time">
                        {new Date(detail?.last_login).toLocaleTimeString()}
                      </span>
                    </p>
                  </div>
                )}
              </div>
  
              {/* <div className="detail detail6">
                <div className="icon">
                  <span className="material-icons-outlined">wallet</span>
                </div>
                <div className="content">
                  <span className="label">Wallet Pending Approval</span>
                  <span className="value">2</span>
                </div>
              </div>
  
              <div className="detail detail7">
                <div className="icon">
                  <span className="material-icons-outlined">route</span>
                </div>
                <div className="content">
                  <span className="label">Total Route Covered</span>
                  <span className="value">68</span>
                </div>
              </div> */}
            </div>
  
            {/* <div className="user-additional-details-border">
      <div className="border"></div>
    </div> */}
  
            {/* <div className="loadMore">
      <div className="arrowDownButton">
      <span className="material-icons">
  keyboard_arrow_down
  </span>
      </div>
    </div> */}
          </div>
  
          <div className="transaction-details">
            <div className="user-overview-container">
              <div className="userOverview userOverview1 ">
                <h3>Agent Wallet</h3>
                <h1>₹{detail?.wallet_amount}</h1>
              </div>
              <div className="userOverview userOverview2 ">
                <div className="icon">
                  <span className="material-icons">support_agent</span>
                </div>
                <p>Branch Name</p>
                <h3>{detail?.branch_name}</h3>
              </div>
              <div className="userOverview userOverview3">
                <div className="icon">
                  <span className="material-icons-outlined">route</span>
                </div>
                <p>Total Route</p>
                <h3 style={{ color: "green" }}>{detail?.total_route || 2}</h3>
              </div>
              {/* <div className="userOverview userOverview4 ">
                <div className="icon">
                <span className="material-icons">
  verified
  </span>
  
                  </div>
                  <p>Customer Score</p>
  <h3>900</h3>
                </div> */}
            </div>
  
            <div className="view-detail-table-container">
              <div className="view-detail-table-menu">
                <div
                  className={`menuItem menuItem1 ${
                    activeMenuIndex === 0 ? "active" : ""
                  }`}
                  onClick={() => handleMenuClick(0)}
                >
                  Emi Collection History
                </div>
                <div
                  className={`menuItem menuItem2 ${
                    activeMenuIndex === 1 ? "active" : ""
                  }`}
                  onClick={() => {
                    handleMenuClick(1);
                  }}
                >
                  Collection Route
                </div>
                <div
                  className={`menuItem menuItem3 ${
                    activeMenuIndex === 2 ? "active" : ""
                  }`}
                  onClick={() => {
                    handleMenuClick(2);
                  }}
                >
                  Wallet Transaction
                </div>
  
                <div
                  className={`menuItem menuItem3 ${
                    activeMenuIndex === 3 ? "active" : ""
                  }`}
                  onClick={() => {
                    handleMenuClick(3);
                  }}
                >
                  Activity
                </div>
              </div>
  
              {activeMenuIndex == 0 && (
                <AgentEmiCollection agentId={agentId} isOpen={showDetails} />
              )}
  
              {activeMenuIndex == 1 && <ColloectionRoute agentId={agentId} />}
  
              {activeMenuIndex == 2 && <WalletTransactions agentId={agentId} />}
  
              {activeMenuIndex === 3 && <AgentActivity agentId={agentId} />}
            </div>
          </div>
        </div>
      </div>
      <AgentCreate
        id={agentId}
        showCreate={showEdit}
        handleCloseCreate={handleEditClose}
      />
    </>
  );
}

export default AgentDetails;
