@import '/app/styles/variables.scss';
.view-qr-container{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
   right: 0;
   background-color: rgba(0, 0, 0, 0.7);
   backdrop-filter: blur(3px);
   z-index: 5001;
   display: flex;
   justify-content: center;
   align-items: center;
   opacity: 0;
   z-index: -1;
   transition: opacity 0.3s ease, z-index 0s linear 0.3s;
   overflow-y: auto;

   &.show{
    opacity: 1;
    z-index: 1000;
}


.view-qr{
        background-color: $white_color;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 500px;
        padding: 30px ;
        position: relative;
        border-radius: 6px;
        margin: 30px;

        h3{
            text-align: center;
            margin-bottom: 20px;
            color: darken( $black_color2, 15%);
            font-weight: 600;
            font-size: 18px;
        }

        .closeIcon{
            position: absolute;
            top: 15px;
            bottom: 0;
            right: 10px;
            //left: 0;
            font-size: 15px;
            cursor: pointer;
            font-weight: 600;

        }

        .qrImage {
            position: relative;  // Add this line
            width: 100%;          // Ensure the width is set to 100% or a specific value
            height: 300px;        // Set the height of the image container
          }
          
          .qrImage img {
            object-fit: contain;  // Keeps the image contained within the parent container
            width: 100%;           // Makes the image scale properly within the container
            height: 100%;          // Ensures the image takes the full height of the parent container
          }
          
                
        .buttons{
            display: flex;
            flex-direction: row;
            gap: 15px;
            margin-top: 15px;


            .resetButton{
                background-color: $primary_color;
                color: $white_color1;
                font-size: 11px;
                padding: 6px 26px;
                border-radius: 3px;
                border: 0;
                cursor: pointer;
                transition: background-color 0.3s ease;
            
                &:hover{
                    background-color: darken($primary_color, 10%);     
                }
            
            
              
            }

            .downloadButton{
                background-color: $primary_color;
                color: $white_color1;
                font-size: 11px;
                padding: 6px 26px;
                border-radius: 3px;
                border: 0;
                cursor: pointer;
                transition: background-color 0.3s ease;
                
            
                &:hover{
                    background-color: darken($primary_color, 10%);     
                }
            
            
              
            }
        }

        .no-qr-found{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;

            h3{
                font-size: 16px;
                font-weight: 600;
                color: #6c757d;
            }
        }

    

    }
   }