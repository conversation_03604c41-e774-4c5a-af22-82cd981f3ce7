@import '/app/styles/variables.scss';

.activity-card-div {
  max-width: 100%;
  background-color: $white_color;
  display: flex;
  flex-direction: column;
  height: calc(100dvh - 237px);
  overflow-y: auto;
  padding: 30px 10px;

  .table-header-details-page{
    border-bottom: 1px solid #ddd;
  }
}

.activity-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.activity-day {
  max-width: 100%;
  overflow-x: auto;
  padding: 15px;
  border-bottom: 1px solid #ddd;

  &:last-child {
    border-bottom: none;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: $black_color3;
}

.view-details {
  color: #007bff;
  font-size: 13px;
  text-decoration: none;
  font-weight: bold;
}

.timeline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  width: 100%;
  position: relative;
  min-width: 100px;
}

.event {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 140px;
}

.time {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 5px;
}
.description{
  font-size: 12px;
  font-weight: 500;
  margin-top: 5px;
}

.dot-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.dot {
  width: 10px;
  height: 10px;
  background: $primary_color;
  border-radius: 50%;
  z-index: 2;
  border: 2px solid #fff;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.dotted-line {
  position: absolute;
  top: 50%;
  height: 2px;
  width: 50%;
  background: repeating-linear-gradient(
    90deg,
    $primary_color,
    $primary_color 4px,
    transparent 4px,
    transparent 8px
  );
  z-index: 1;
}

.left {
  left: -50%;
}

.right {
  right: -50%;
}

/* Hide the first left dotted line */
.event:first-child .dotted-line.left {
  display: none;
}

/* Hide the last right dotted line */
.event:last-child .dotted-line.right {
  display: none;
}
