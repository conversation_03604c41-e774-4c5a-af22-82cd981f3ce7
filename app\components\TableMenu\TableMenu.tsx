import React, { useRef, useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import './TableMenu.scss';

interface MenuProps {
  // data: any;
  index: number;
  showMenu: number | null;
  handleMenuToggle: (index: number) => void;
  handleCloseMenu: () => void;
}

const TableMenu: React.FC<MenuProps> = ({  index, showMenu, handleMenuToggle, handleCloseMenu }) => {
  const menuRef = useRef<HTMLDivElement | null>(null);
  const moreHorizRef = useRef<HTMLSpanElement | null>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        moreHorizRef.current &&
        !moreHorizRef.current.contains(event.target as Node)
      ) {
        handleCloseMenu();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleCloseMenu]);

  // Update menu position when showMenu changes
  useEffect(() => {
    if (showMenu === index && moreHorizRef.current) {
      const rect = moreHorizRef.current.getBoundingClientRect();
      const menuWidth = 150; // Adjust this value to match your menu width in CSS
      setMenuPosition({
        top: rect.top + window.scrollY, // Position the menu below the icon
        left: rect.left + window.scrollX - menuWidth + 20, // Position the menu on the left side of the icon
      });
    }
  }, [showMenu, index]);

  // Menu rendering logic
  const menuContent = (
    <div
      ref={menuRef}
      className={`menu ${menuPosition ? 'show' : ''}`}
      style={{
        top: `${menuPosition?.top ?? 0}px`,
        left: `${menuPosition?.left ?? 0}px`,
      }}
    >
      <ul>
        <li onClick={() => alert(` Customer Disabled`)}>Disable Customer</li>
        <li onClick={() => alert(`Customer Deleted `)}>Delete Customer</li>
      </ul>
    </div>
  );

  return (
    <>
      <span
        ref={moreHorizRef}
        className="material-icons moreHoriz moreHoriz-icon"
        onClick={() => handleMenuToggle(index)}
      >
        more_horiz
      </span>
      {showMenu === index &&
        ReactDOM.createPortal(
          <>
            {menuContent}
            <div className="overlay" onClick={handleCloseMenu}></div>
          </>,
          document.body
        )}
    </>
  );
};

export default TableMenu;
