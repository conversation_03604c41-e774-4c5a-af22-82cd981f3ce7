export interface loginForm{
    username:string
    password:string
}
export interface loginRes{
    access_token:string
    token_type:string
}
export interface userDetails{
    id: number
    name:string
    email:string
    phone:string
    address:string
    user_type: string
    username:string
    user_id:number
}

export interface address{
    address_title:string
    address:string
    is_primary:boolean
}

export interface createAddressType{
    address_title:string
    address:string
    is_primary: boolean
    customer_id:number | null
}