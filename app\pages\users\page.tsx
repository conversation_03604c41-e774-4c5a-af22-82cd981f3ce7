"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./users.scss";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import UserCreate from "./components/UserCreate/UserCreate";
import { useCommonContext } from "@/app/contexts/commonContext";
import { deleteUser, disableUser, fetchUserList } from "./users-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import ChangePassword from "./components/ChangePassword/ChangePassword";
import { User_List_Item } from "./users.model";
import { useRouter } from "next/navigation";
import { AxiosError } from "axios";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { debounce } from "@mui/material";

export interface DropdownItem {
  id: string;
  label: string;
}

interface ErrorResponseData {
  detail?: string;
}

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [showChangePwd, setShowChangePwd] = useState<boolean>(false);
  const [userList, setUserList] = useState<User_List_Item[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [isTableLoading, setIsTableLoading] = useState<boolean>();
  const [visible, setVisible] = useState<boolean>(false);

  //fillters
  const [searchValue, setSearchValue] = useState<string>("");
  const [userType, setUserType] = useState<string>("");
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();
  const navigate = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  //fetch user list
  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPageNo: number,
      searchKey: string,
      user_type: string
    ) => {
      const skip = (pageNo - 1) * itemsPerPageNo;

      try {
        setIsTableLoading(true);
        const response = await fetchUserList(
          skip,
          itemsPerPageNo,
          searchKey,
          user_type
        );
        setUserList(response.results);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setIsTableLoading(false);
      }
    },
    [setIsTableLoading]
  );

  useEffect(() => {
    getList(1, 10, "", "");
  }, [getList]);

  const handleReset = () => {
    setUserType("");
    setSearchValue("");
    getList(1, itemsPerPage, "", "");
  };

  useEffect(() => {
    //console.log("changed");

    if (!showCreate) {
    }
  }, [showCreate]);

  const handleCreate = () => {
    setShowCreate(true);
  };

  const handleCloseCreate = () => {
    setShowCreate(false);
    getList(currentPage, itemsPerPage, searchValue, userType);
    setSelectedUserId(null);
  };

  const handleCloseChangePassword = () => {
    setShowChangePwd(false);
  };

  // useEffect(() => {
  //   console.log(isSearchBoxVisible); // This will log the updated state
  // }, [isSearchBoxVisible]);

  const handlePageChange = (pageNo: number) => {
    //console.log(pageNo,"page changed");
    getList(pageNo, itemsPerPage, searchValue, userType);
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    getList(1, value, searchValue, userType);
  };

  const handleEditUser = (id: number) => {
    setSelectedUserId(id);
    setShowCreate(true);
  };

  const handleChangePassword = (id: number) => {
    setSelectedUserId(id);
    setShowChangePwd(true);
  };

  const handleDeleteUser = (id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: `Are you sure you want to delete this user?`,
      text: "This action cannot be undone!",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        try {
          setIsLoading(true);
          await deleteUser(id);
          getList(currentPage, itemsPerPage, searchValue, userType);
          fire({
            icon: "success", // Use success icon
            title: "User deleted",
            text: "The user is deleted successfully!",
            autoClose:2000
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error("Failed to delete the branch:", err);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleDisableUser = (item: DropdownItem, id: number) => {
    fire({
      position: "center",
      icon: "success",
      title: `Are you sure`,
      text: `To ${
        item.id === "disable-user" ? "disable" : "enable"
      } the user .`,
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        try {
          setIsLoading(true);
          await disableUser(id);
          getList(currentPage, itemsPerPage, searchValue, userType);
          fire({
            position: "top-right",
            icon: "success", // Use success icon
            title: `User ${
              item.id == "disable-user" ? "disabled" : "enabled"
            } successfully`,
            autoClose: 2000,
          });
        } catch (err) {
          const axiosError = err as AxiosError<ErrorResponseData>;
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              axiosError?.response?.data?.detail ||
              axiosError?.message ||
              "An unknown error occurred",
            confirmButtonText: "Ok",
            // cancelButtonText: "No",
            onConfirm: () => {
              // console.log("Confirmed:");
            },
          });
          console.error("Failed to disable the user", err);
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const user_type = event.target.value;
    if (item == "user_type") {
      setUserType(user_type);
      getList(1, itemsPerPage, searchValue, user_type);
    }
  };

  // useEffect(() => {
  //     console.log('Search Value:', searchValue);
  //  }, [searchValue]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) => getList(1, itemsPerPage, value, userType),
    300
  );

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    //console.log(item,id);
    if (item.id == "change-password") {
      handleChangePassword(id);
    } else if (item.id == "edit-user") {
      handleEditUser(id);
    } else if (item.id == "delete-user") {
      handleDeleteUser(id);
    } else {
      handleDisableUser(item, id);
    }
  };

  const handleLogout = () => {
    localStorage.setItem("token", "");
    navigate.push("/");
  };

  return (
    <>
      <div
        className={`userList overall-list-padding ${visible ? "visible" : ""}`}
      >
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add New User +
          </div>
        </div>
        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>User Type {userType ? `(${userType})` : ""}</span>
                    <select
                      className="dropdown"
                      value={userType}
                      onChange={(e) => {
                        handleFilterSelect(e, "user_type");
                      }}
                    >
                      <option value="">None</option>
                      {/* <option value="Customer">Customer</option>
                    <option value="Agent">Agent</option> */}
                      <option value="Manager">Branch Manager</option>
                      <option value="ManagingDirector">
                        Managing Director
                      </option>
                      {/* <option value="due">Due</option> */}
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>
                </div>
                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={[
                    `Search "username"`,
                    `Search "name"`,
                    `Search "email"`,
                    `Search "phone"`,
                    `Search "branch name"`,
                  ]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={8}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>CURRENT STATUS</th>
                      <th>Address</th>
                      <th>Phone Number</th>
                      <th>Username</th>
                      <th>Email</th>

                      <th>User Type</th>

                      <th>Branch</th>
                      {/* <th>Action</th> */}
                      <th>Action</th>
                    </tr>
                  </thead>

                  <tbody>
                    {userList && userList.length > 0 ? (
                      userList.map((user, index) => {
                        const menuItems = [
                          { id: "change-password", label: "Change Password" },
                          { id: "edit-user", label: "Edit User" },
                          {
                            id: user.is_active ? "disable-user" : "enable-user",
                            label: user.is_active
                              ? "Disable User"
                              : "Enable User",
                          },
                          { id: "delete-user", label: "Delete User" },
                        ];
                        return (
                          <tr key={index}>
                            <td>{user.name}</td>
                            <td>
                              {user.is_active ? (
                                <div className="available">
                                  <span>Available</span>
                                </div>
                              ) : (
                                <div className="offline">
                                  <span>offline</span>
                                </div>
                              )}
                            </td>
                            <td>{user.address}</td>
                            <td>{user.phone}</td>
                            <td>{user.username}</td>
                            <td>{user.email}</td>

                            <td>{user.user_type}</td>

                            <td>{user.branch_name}</td>

                            {/* <td>
                    <div className="actionIcons">
                      <span className="material-icons eyeIcon">visibility</span>
                      <span className="material-icons editIcon" onClick={() => {handleEditUser(user.user_id)}}>edit</span>
                      <span className="material-icons deleteIcon" onClick={() => handleDeleteUser(user.name, user.user_id)} >delete</span>
                    </div>
                  </td> */}

                            <td>
                              <TableMenuTwo
                                items={menuItems}
                                onClick={handleMenuItemClick}
                                id={user.user_id}
                              />
                              {/* <TableMenu
                      data={[] || []}
                      index={index}
                      showMenu={showMenu}
                      handleMenuToggle={handleMenuToggle}
                      handleCloseMenu={handleCloseMenu}
                    /> */}
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="no-data2">
                          <h5>
                            {userType &&
                              !searchValue &&
                              `There is no user with the selected user type "${userType}".`}
                            {searchValue &&
                              !userType &&
                              `No orders match your search for "${searchValue}".`}
                            {!searchValue &&
                              !userType &&
                              "It looks like you don't have any user yet."}
                            {searchValue &&
                              userType &&
                              `No user with the user type "${userType}" match your search for "${searchValue}".`}
                          </h5>

                          {(userType || searchValue) && (
                            <button
                              onClick={handleReset}
                              style={{
                                marginLeft: "auto",
                                marginRight: "auto",
                              }}
                              className="submitButton"
                            >
                              <span className="material-icons">
                                restart_alt
                              </span>
                              Reset
                            </button>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {userList && userList?.length > 0 && (
              <Pagination
                totalPages={totalPages}
                handlePage={handlePageChange}
                itemsPerPage={itemsPerPage}
                page={currentPage}
                handleItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </div>

      <UserCreate
        showCreate={showCreate}
        handleCloseCreate={handleCloseCreate}
        selectedUserId={selectedUserId}
      />
      <ChangePassword
        handleLogout={handleLogout}
        showChangePwd={showChangePwd}
        handleCloseChangePassword={handleCloseChangePassword}
        selectedUserId={selectedUserId}
      />
    </>
  );
}

export default Page;
