import "./BranchCreate.scss";
import { useForm, SubmitHandler } from "react-hook-form";
import { branch_create } from "../../branch.model";
import {
  createBranch,
  editBranchById,
  fetchBranchById,
} from "../../branch-service";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { useCallback, useEffect, useState } from "react";
import { useCommonContext } from "@/app/contexts/commonContext";
import FileUpload from "@/app/components/utilities/Fileupload/FileUpload";
import { AxiosError } from "axios";

interface BranchCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  selectedBranchId: number | null;
}

interface ErrorResponseData {
  detail?: string;
}

function BranchCreate({
  showCreate,
  handleCloseCreate,
  selectedBranchId,
}: BranchCreateProps) {
  // const [branchDetails, setBranchDetails] = useState<Branch>([]);
  const { setIsLoading } = useCommonContext();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
  } = useForm<branch_create>();
  const [uploadFile, setUploadFile] = useState<string | File | null>(null);

  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png", "application/pdf"];

  const fetchBranchDetails = useCallback(
    async (id: number) => {
      try {
        setIsLoading(true);
        const data = await fetchBranchById(id);
        reset({
          code: data.code,
          name: data.name,
          address: data.address,
          email: data.email,
          phone_number: data.phone_number,
          qr_code: data.qr_code,
        });
        setUploadFile(data.qr_code);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    [reset, setIsLoading, setUploadFile] // ✅ Dependencies to ensure function stability
  );

  useEffect(() => {
    if (selectedBranchId !== null) {
      fetchBranchDetails(selectedBranchId);
    } else {
      reset({
        code: "",
        name: "",
        address: "",
        email: "",
        phone_number: "",
      });
    }
  }, [selectedBranchId, fetchBranchDetails, reset]);

  const { fire } = useAlert();

  const onSubmit: SubmitHandler<branch_create> = async (data) => {
    //console.log('Form Data:', data);
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("address", data.address);
    formData.append("email", data.email);
    formData.append("phone_number", data.phone_number);
    formData.append("code", data.code);

    // if(data.id){
    //   formData.append("id", String(data.id));
    // }

    if (data.qr_code instanceof File) {
      formData.append("qr_code", data.qr_code);
    }

    try {
      if (selectedBranchId) {
        //update branch
        setIsLoading(true);
        const response = await editBranchById(selectedBranchId, formData);
        console.log("branch updated", response);

        fire({
          icon: "success", // Use success icon
          title: "Branch Updated",
          text: "The branch details were updated successfully!",
          autoClose: 2000,
        });
      } else {
        //create branch
        setIsLoading(true);
        const res = await createBranch(formData);
        console.log("Branch created", res);

        fire({
          icon: "success", // Use success icon
          title: "Branch Created",
          text: "The branch is created successfully!",
          autoClose: 2000,
        });
      }

      handleCloseCreate();
      reset();
    } catch (err) {
      //setIsLoading(false);
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
        // cancelButtonText: "No",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }

    // handleCloseCreate()
    // reset();
  };

  const onFileUpload = (file: File | null) => {
    setUploadFile(file);
    setValue("qr_code", file); // Update form value
  };

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>{selectedBranchId ? "Edit" : "Add"} Branch</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>

        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="branchCode">Branch Code</label>
                <input
                  {...register("code", {
                    required: "Please enter the branch code.",
                  })}
                  id="branchCode"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.code?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="branchName">Branch Name</label>
                <input
                  id="branchName"
                  type="text"
                  placeholder="Type..."
                  {...register("name", {
                    required: "Please provide the branch name.",
                  })}
                />
                <p className="error-message">{errors.name?.message}</p>
              </div>

              <div className="input-field wf-100">
                <label htmlFor="address">Address</label>

                <input
                  {...register("address", {
                    required: "Please enter the branch's address.",
                  })}
                  id="address"
                  type="text"
                  placeholder="Type..."
                />

                <p className="error-message">{errors.address?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="email">Email</label>
                <input
                  {...register("email", {
                    required: "Please enter an email address.",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // Regex for email validation
                      message:
                        "The email format is invalid. Please enter a valid email address.",
                    },
                  })}
                  id="email"
                  type="email"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.email?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="phoneNumber">Phone Number</label>
                <input
                  {...register("phone_number", {
                    required: "Please enter the phone number.",
                  })}
                  id="phoneNumber"
                  type="text"
                  placeholder="Type..."
                />
                <p className="error-message">{errors.phone_number?.message}</p>
              </div>

              <FileUpload
                uploadedFile={uploadFile}
                isOpen={showCreate}
                allowedTypes={ALLOWED_TYPES}
                maxSize={MAX_FILE_SIZE}
                onFileUpload={onFileUpload}
                label="QR"
                requiredMessage="Please upload a Qr image."
                maxFileSizeMessage="QR image size is too large."
                invalidTypeMessage="Invalid QR image type."
              />
              {errors.qr_code && (
                <p className="error-message">{errors.qr_code.message}</p>
              )}
            </div>

            <div className="SubmitBtn">
              <button className="submitButton">
                {selectedBranchId ? "Save" : "Submit"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default BranchCreate;
