import React, { useRef } from 'react'
import './ViewQR.scss'
import Image from 'next/image';


interface ViewQrProps{
  showQR: boolean;
  handleCloseQR:VoidFunction; 
  screenshotUrl: string | null;
}

function ViewQR({ showQR, handleCloseQR, screenshotUrl} : ViewQrProps) {
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;
  const qrRef = useRef<HTMLImageElement>(null);

  const handleDownload = () => {
    if(qrRef.current){
      const link = document.createElement('a');
      link.href = qrRef.current.src;
      link.download = 'QR_Code.png';
      link.click();
    }
  }


  return (
    <div className={`view-qr-container ${showQR ? 'show' : ''}`}>
     <div className="view-qr">
      
      <span className="material-icons closeIcon" onClick={handleCloseQR}>
close
</span>
<div className="qrImage">


{/* <Image
ref={qrRef}
  src={mediaUrl && screenshotUrl ? mediaUrl + screenshotUrl : ''}
  alt="QR code"
  fill
  style={{ objectFit: 'contain' }}
/> */}


{mediaUrl && screenshotUrl ? (
  <Image
    ref={qrRef}
    src={`${mediaUrl}${screenshotUrl}`}
    alt="QR code"
    fill
    style={{ objectFit: 'contain' }}
  />
) : (
  <div className="no-qr-found">
    <h3>No QR Code Available</h3>
  </div>
)}


</div>

<div className="buttons" >

   {/* <button className='submitButton'> <i className="fa-solid fa-rotate"></i> Reset </button> */}


   <button className='submitButton' onClick={handleDownload}><i className="fa-solid fa-download"></i> Download </button>

</div>
 




 
    
     </div>
    </div>
  )
}

export default ViewQR