import axiosInstance from "@/app/api/axiosInstance";
import { User_Create, User_List, User_Password_Change } from "./users.model";


export const fetchUserList = async (
    skip : number, 
    itemsPerPage : number, 
    search : string, 
    user_type? : string) : Promise<User_List> => {

    try{
        const queryParams = new URLSearchParams({
            skip: skip.toString(),
            limit: itemsPerPage.toString(),
            search : search,
        });

        if(user_type){
            queryParams.append('user_type', encodeURIComponent(user_type));
        }


        const response = await axiosInstance.get<User_List>(`/users/?${queryParams.toString()}`);
        return response.data;

    }catch(error){
        console.error("Error fetching user list", error);
        throw new Error("Failed to fetch user list."); 
    }
}

//create user
export const createUser = async (body: User_Create) => {
    try{
        console.log("api called");
        
        const response = await axiosInstance.post("/users/", body);
        return response.data
        
    }catch(error){
        console.error("Error creating new user", error);
        throw new Error("Failed to create new user") 
    }
}

//fetch user by id
export const fetchUserById = async (user_id:number) => {
    try{
        const response = await axiosInstance.get(`/users/${user_id}`);
        return response.data;

    }catch(error){
        console.error("Error fetching user by id", error);
        throw new Error("Failed to fetch by id");
    }
}

//edit user by id
export const editUserById = async ( user_id:number, body:User_Create ) => {
    try{
       const response = await axiosInstance.put(`/users/${user_id}`, body);
       return response;
    }catch(error){
        console.error("Error updating user data", error);
        throw new Error("Failed to update user data")
        
    }
}

//change user password 
export const changeUserPassword = async ( user_id:number, body:User_Password_Change ) => {
    try{
       const response = await axiosInstance.put(`/users/password_update/${user_id}/`, body);
       return response;
    }catch(error){
        console.error("Error updating user password", error);
        throw new Error("Failed to update user password")
        
    }
}

//change user password2 
export const changeUserPwd = async ( body:User_Password_Change ) => {
    try{
       const response = await axiosInstance.put(`/users/dashboard/password_update/`, body);
       return response;
    }catch(error){
        console.error("Error updating user password", error);
        throw new Error("Failed to update user password")
        
    }
}

//delete user
export const deleteUser = async ( user_id:number ) => {
    try{
        const response = await axiosInstance.delete(`/users/delete/${user_id}/`);
        return response;

    }catch(error){
        console.error("error deleting user", error);
        throw new Error("Failed to delete the user")
        
    }
}

//disable user
export const disableUser = async ( user_id:number ) => {
    try{
        const response = await axiosInstance.delete(`/users/disable/${user_id}/`);
        return response;

    }catch(error){
        console.error("error disabling user", error);
        throw new Error("Failed to disable the user")
        
    }
}

//fetchbranchlist
export const fetchBranchList = async () => {
    try{

        const response = await axiosInstance.get("/branches/?pagination=false");
        return response.data;

    }catch(error){

        console.error("Error fetching customer list", error);
        throw new Error("Failed to fetch customer list")

    }
}