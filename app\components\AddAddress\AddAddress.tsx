import React, { useCallback, useEffect } from "react";
import "./AddAddress.scss";
import { useForm, SubmitHandler } from "react-hook-form";
import {
  createAddress,
  editAddress,
  getAddressById,
} from "@/app/api/commonService";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useAlert } from "../utilities/Alert/Alert";

type AddressFormInputs = {
  label: string;
  address: string;
  isPrimary: boolean;
};

interface addressCreateProps {
  address_id: number | null;
  customer_id: number | null;
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
}

function AddAddress({
  address_id,
  customer_id,
  showCreate,
  handleCloseCreate,
}: addressCreateProps) {
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<AddressFormInputs>();

  const { setIsLoading } = useCommonContext();
  const { fire } = useAlert();

  const onSubmit: SubmitHandler<AddressFormInputs> = async (data) => {
    setIsLoading(true);
    if (address_id) {
      const body = {
        address_title: data.label,
        address: data.address,
        is_primary: data.isPrimary,
      };
      editAddress(address_id, body)
        .then((res) => {
          if (res) {
            reset();
            handleCloseCreate();
            setIsLoading(false);
            fire({
              position: "top-right",
              icon: "success",
              title: "Edited successfully",
              autoClose: 2000,
            });
          }
        })
        .catch((error) => {
          setIsLoading(false);
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              error?.response?.data?.detail ||
              error?.message ||
              "An unknown error occurred",
            confirmButtonText: "yes",
          });
        });
    } else {
      const body = {
        address_title: data.label,
        address: data.address,
        is_primary: data.isPrimary || false,
        customer_id: customer_id,
      };
      createAddress(body)
        .then((res) => {
          if (res) {
            reset();
            handleCloseCreate();
            setIsLoading(false);
            fire({
              position: "top-right",
              icon: "success",
              title: "Created successfully",
              autoClose: 2000,
            });
          }
        })
        .catch((error) => {
          setIsLoading(false);
          fire({
            position: "center",
            icon: "error",
            title: "Something went wrong",
            text:
              error?.response?.data?.detail ||
              error?.message ||
              "An unknown error occurred",
            confirmButtonText: "yes",
          });
        });
    }
  };
  const getAddress = useCallback(async () => {
    if (!address_id) return;

    setIsLoading(true);
    try {
      const api = await getAddressById(address_id);
      setValue("address", api.address);
      setValue("label", api.address_title);
      setValue("isPrimary", api.is_primary);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }, [address_id, setValue, setIsLoading]);

  useEffect(() => {
    if (showCreate) {
      getAddress();
    } else if (!showCreate) {
      reset();
    }
  }, [showCreate, getAddress, reset]);

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setValue("isPrimary", isChecked);
    console.log("ischecked", isChecked);
  };
  return (
    <div className={`add-address-container ${showCreate ? "show" : ""}`}>
      <div className="add-address-form">
        {address_id ? <h3>Edit Address</h3> : <h3>Add Address</h3>}
        <span className="material-icons closeIcon" onClick={handleCloseCreate}>
          close
        </span>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="label">Label</label>

              <input
                {...register("label", { required: "Label is required." })}
                id="label"
                type="text"
                placeholder="Type..."
              />

              <p className="error-message">{errors.label?.message}</p>
            </div>
          </div>

          <div className="input-field-container w-100">
            <div className="input-field w-100">
              <label htmlFor="address">Address</label>

              <input
                {...register("address", { required: "Address is required." })}
                id="address"
                type="text"
                placeholder="Type..."
              />

              <p className="error-message">{errors.address?.message}</p>
            </div>
          </div>
          <div className="isAccountedButton">
            <div className="content">
            <input
              type="checkbox"
              {...register("isPrimary")}
              onChange={handleCheckboxChange}
            />
            <span className="label">Is Primary</span>
            </div>
          </div>
          <div className="SubmitBtn">
            <button className="submitButton">Submit</button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default AddAddress;
