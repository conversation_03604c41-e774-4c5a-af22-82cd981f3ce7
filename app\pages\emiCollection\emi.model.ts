export interface Emi_Collection_Overview_List {
    links:{
              next: string
              previous: string
          }
          total:number
          page:number
          page_size:number
          total_pages:number
          results: Emi_Collection_Overview_List_Item[]
}

export interface Emi_Collection_Overview_List_Item {
    id:number
    created_at : string;
    emi_amount : number;
    customer_name : string;
    transaction_mode : string | null;
    agent_name: string | null;
    branch_name: string | null;
    remark: string | null;
    status: string;
    care_of: string;
    short_title: string;
    payment_reference_no: string;
    transaction_id: string
    screenshot: string;
}


export interface editEMiCollection{
    transaction_mode : string | null;
    emi_amount : number;
}