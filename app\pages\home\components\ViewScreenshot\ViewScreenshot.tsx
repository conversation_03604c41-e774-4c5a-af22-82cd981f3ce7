import React from 'react'
import './ViewScreenshot.scss'
import Image from 'next/image';


interface ViewScreenshotProps{
    showScreenshot: boolean;
    handleCloseScreenshot:VoidFunction; 
    screenshotUrl: string;
  }

function ViewScreenshot({showScreenshot, handleCloseScreenshot, screenshotUrl} : ViewScreenshotProps) {
  const mediaUrl = process.env.NEXT_PUBLIC_MEDIA_PATH;
  return (
    <div className={`view-screenshot-container ${showScreenshot ? 'show' : ''}`}>
     <div className="view-screenshot">
      
      <span className="material-icons closeIcon" onClick={handleCloseScreenshot}>
close
</span>
<div className="screenshot">


{/* <Image
  src={mediaUrl + screenshotUrl}
  alt="Screenshot Image"
  width={500}
  height={300}
  style={{ width: "100%", height: "auto" }} // Responsive behavior
/> */}

{mediaUrl && screenshotUrl ? (
  <Image
  src={mediaUrl + screenshotUrl}
  alt="Screenshot Image"
  width={500}
  height={300}
  style={{ width: "100%", height: "auto" }} // Responsive behavior
/>
) : (
  <p>No Screenshot Available</p>
)}


</div>

 




 
    
     </div>
    </div>
  )
}

export default ViewScreenshot