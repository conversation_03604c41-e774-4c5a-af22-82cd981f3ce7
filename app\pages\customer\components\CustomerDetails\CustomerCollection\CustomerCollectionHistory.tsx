"use client";
import Pagination from "@/app/components/utilities/Pagination/Pagination";
import ReactDOM from "react-dom";
import React, { useCallback, useEffect, useState } from "react";
import "./customerCollectionOverview.scss";
import TableWithShimmer from "@/app/components/Shimmer/TableWithShimmer/TableWithShimmer";
import {
  getCollectionHistory,
  getCustomerCollectionStatement,
} from "../../../customer-service";
import { debounce } from "@mui/material";
import { customer_collection_overview } from "../../../customer.model";
import ViewScreenshot from "@/app/pages/home/<USER>/ViewScreenshot/ViewScreenshot";
import EditEmiCollection from "@/app/pages/emiCollection/components/EditEmiCollection";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { useAlert } from "@/app/components/utilities/Alert/Alert";
import { useCommonContext } from "@/app/contexts/commonContext";
import { AxiosError } from "axios";
import { ErrorResponseData } from "@/app/pages/agent/page";
interface props {
  customerId: number | null;
}
function CustomerCollectionHistory({ customerId }: props) {
  const [page, setPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPage, setTotalPages] = useState<number>(10);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [searchValue, SetSearchValue] = useState<string>("");
  const [list, setList] = useState<customer_collection_overview[]>([]);
  const [status, setStatus] = useState<string>("");
  const [paymentMode, setPaymentMode] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [showScreenshot, setShowScreenshot] = useState<boolean>(false);
  const [screenshotUrl, setScreenShotUrl] = useState<string>("");
  const [selectedEmi, setSelectedEmi] = useState<number | null>(null);
  const [isEditEmi, setIsEditEmi] = useState<boolean>(false);
  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const { fire } = useAlert();
  const { setIsLoading , maxDate } = useCommonContext();
  //filter pop up

  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handlePageChange = (pageNo: number) => {
    getList(
      pageNo,
      itemsPerPage,
      searchValue,
      status,
      paymentMode,
      selectedDate
    );
    setPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setPage(1);
    getList(1, value, searchValue, status, paymentMode, selectedDate);
  };

  const getList = useCallback(
    async (
      pageNo: number,
      itemsPerPage: number,
      search: string,
      status: string,
      transactionMode: string,
      date: string
    ) => {
      setIsTableLoading(true);
      const skip = (pageNo - 1) * itemsPerPage;
      try {
        const api = await getCollectionHistory(
          customerId,
          skip,
          itemsPerPage,
          search,
          status,
          transactionMode,
          date
        );
        setList(api.results);
        setPage(api.page);
        setTotalPages(api.total_pages);
      } catch (error) {
        console.error("Error fetching collection history:", error);
      } finally {
        setIsTableLoading(false);
      }
    },
    [customerId] // Include only stable dependencies
  );

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
      getList(1, itemsPerPage, searchValue, value, paymentMode, selectedDate);
    } else if (item === "paymentMode") {
      setPaymentMode(value);
      getList(1, itemsPerPage, searchValue, status, value, selectedDate);
    } else if (item === "date") {
      setSelectedDate(value);
      getList(1, itemsPerPage, searchValue, status, paymentMode, value);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    SetSearchValue(value);
    debouncedGetList(value);
  };

  const debouncedGetList = debounce(
    (value: string) =>
      getList(1, itemsPerPage, value, status, paymentMode, selectedDate),
    300
  );

  const handleRest = () => {
    setSelectedDate("");
    setPaymentMode("");
    setStatus("");
    SetSearchValue("");
    getList(1, itemsPerPage, "", "", "", "");
  };

  const handleShowScreenshot = (imgUrl: string) => {
    setShowScreenshot(true);
    setScreenShotUrl(imgUrl);
  };

  const handleCloseScreenshot = () => {
    setShowScreenshot(false);
  };

  const handleCloseEdit = () => {
    setIsEditEmi(false);
    getList(page, itemsPerPage, searchValue, status, paymentMode, selectedDate);
  };

  const handleEdit = (id: number) => {
    setSelectedEmi(id);
    setIsEditEmi(true);
  };

  const handleDownloadStatement = async () => {
    setIsLoading(true)
    try {
      // Call API function, res is now a Blob
      const blob = await getCustomerCollectionStatement(customerId);

      // Ensure the response is a valid PDF
      if (blob.type !== "application/pdf") {
        throw new Error("Invalid PDF file received.");
      }

      // Create a temporary URL for the file
      const url = URL.createObjectURL(blob);

      // Create a link and trigger the download
      const link = document.createElement("a");
      link.href = url;
      link.download = "Customer_Statement.pdf";
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (err) {
      const axiosError = err as AxiosError<ErrorResponseData>;
      fire({
        position: "center",
        icon: "error",
        title: "Something went wrong",
        text:
          axiosError?.response?.data?.detail ||
          axiosError?.message ||
          "An unknown error occurred",
        confirmButtonText: "Ok",
      });
      console.error("Download error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (typeof customerId === "number" && !isNaN(customerId))
      getList(1, 10, "", "", "", "");
  }, [customerId, getList]);
  

  return (
    <>
      <div className="collection-history-container">
        <div className="download-button-container">
          <div className="primary-buttton" onClick={handleDownloadStatement}>
            <div className="material-icons">cloud_download</div> Download Statement
          </div>
        </div>
        <div className="table-header">
          <div className="filter-search-container">
            <div className="filterButton">
              <button onClick={handleToggleFilter}>
                <i className="fa-solid fa-filter"></i>
              </button>
            </div>
            <div
              className={`filter-options-select-box ${
                isFilterOpen ? "show" : ""
              }`}
            >
              <div className="filterOption">
                <span>Payment Mode {paymentMode ? `(${paymentMode})` : ""}</span>
                <select
                  className="dropdown"
                  value={paymentMode}
                  onChange={(e) => handleFilterSelect(e, "paymentMode")}
                >
                  <option value="">None</option>
                  <option value="cash">Cash</option>
                  <option value="bank">Bank</option>
                  {/* <option value="due">Due</option> */}
                </select>
                <span className="material-icons">keyboard_arrow_down</span>
              </div>
              <div className="filterOption">
                <span>Status {status ? `(${status})` : ""}</span>
                <select
                  className="dropdown"
                  value={status}
                  onChange={(e) => handleFilterSelect(e, "status")}
                >
                  <option value="">None</option>
                  <option value="inprogress">InProgress</option>
                  <option value="failed">Failed</option>
                  <option value="success">Success</option>
                </select>
                <span className="material-icons">keyboard_arrow_down</span>
              </div>
              <input
                className="date-picker"
                type="date"
                max={maxDate}
                value={selectedDate}
                onChange={(e) => handleFilterSelect(e, "date")}
              />
            </div>
  
            <SearchBox
              value={searchValue}
              onChange={handleSearchChange}
              placeholders={[`Search "transaction id"`]}
            />
          </div>
        </div>
  
        <div className="table2">
          <table>
            <thead>
              <tr>
                <th className="th-first">
                  <div className="tableHead">
                    <span className="heading">Date & Time</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-mid">
                  <div className="tableHead">
                    <span className="heading">Amount</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-mid">
                  <div className="tableHead">
                    <span className="heading">Agent</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-mid">
                  <div className="tableHead">
                    <span className="heading">Status</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-mid">
                  <div className="tableHead">
                    <span className="heading">Mode of transaction</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-mid">
                  <div className="tableHead">
                    <span className="heading">Remark</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                <th className="th-last">
                  <div className="tableHeadLast">
                    <span className="heading">Actions</span>
                    <span className="material-icons arrowdown">
                      keyboard_arrow_down
                    </span>
                  </div>
                </th>
                {/* <th className="th-last">
           <div className="tableHead">
           <span className="heading">Action</span>
           <span className="material-icons arrowdown">keyboard_arrow_down</span>
           </div>
         </th> */}
              </tr>
            </thead>
            <tbody>
              {isTableLoading ? (
                <tr>
                  <td colSpan={7}>
                    <TableWithShimmer
                      no_of_cols={7}
                      no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                    />
                  </td>
                </tr>
              ) : list && list.length > 0 ? (
                list.map((data, index) => (
                  <tr key={index}>
                    <td>
                      <div className="date-time-invoiceNumber">
                        <div className="dateAndTime">
                          <span className="date">
                            {new Date(data.created_at).toLocaleDateString()}
                          </span>
                          <span className="time">
                            {new Date(data.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="invoiceNumber">{data.transaction_id}</p>
                      </div>
                    </td>
                    <td className="amount">{data.emi_amount}</td>
                    <td className="agent">{data.agent_name}</td>
                    <td>
                      <div className="agent">
                        <div className={`status ${data.status} `}>
                          {data.status}
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="transactionMode">
                        <div className="modeOfTransaction">
                          <div className="mode">{data.transaction_mode}</div>
                          {data.transaction_mode === "bank" && (
                            <a>
                              {" "}
                              <i
                                className="fa-solid fa-paperclip"
                                onClick={() =>
                                  handleShowScreenshot(data.screenshot)
                                }
                              ></i>
                            </a>
                          )}
                        </div>
                        {data.payment_reference_no && (
                          <div className="transactionId">
                            {data.payment_reference_no}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="remark">
                      {data.remark ? data.remark : "N/A"}
                    </td>
                    <td className="table-actions">
                      <div
                        className="material-icons"
                        onClick={() => handleEdit(data.id)}
                      >
                        edit
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <td colSpan={7} className="no-data">
                  <h5>
                    {(() => {
                      const filters = [];
  
                      if (searchValue) filters.push(`search "${searchValue}"`);
                      if (status) filters.push(`status "${status}"`);
                      if (paymentMode)
                        filters.push(`transaction mode "${paymentMode}"`);
                      if (selectedDate) filters.push(`date "${selectedDate}"`);
  
                      if (filters.length > 0) {
                        return `No data found with ${filters.join(" and ")}.`;
                      }
  
                      return "It looks like there is no collection history for this customer yet.";
                    })()}
                  </h5>
  
                  {(status || searchValue || paymentMode || selectedDate) && (
                    <button
                      onClick={handleRest}
                      style={{ marginLeft: "auto", marginRight: "auto" }}
                      className="submitButton"
                    >
                      <span className="material-icons">restart_alt</span>Reset
                    </button>
                  )}
                </td>
              )}
            </tbody>
          </table>
        </div>
        {list && list.length > 0 && (
          <div className="pagination-table-container">
            <Pagination
              totalPages={totalPage}
              handlePage={handlePageChange}
              itemsPerPage={itemsPerPage}
              page={page}
              handleItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
        )}
  
      </div>
      {showScreenshot &&
  ReactDOM.createPortal(
    <ViewScreenshot
      screenshotUrl={screenshotUrl}
      showScreenshot={showScreenshot}
      handleCloseScreenshot={handleCloseScreenshot}
    />,
    document.body // Mounts the popup outside any scrolling container
  )}

      {ReactDOM.createPortal(
            <ViewScreenshot
            screenshotUrl={screenshotUrl}
            showScreenshot={showScreenshot}
            handleCloseScreenshot={handleCloseScreenshot}
          />,
          document.body
      )}
      {ReactDOM.createPortal(
          <EditEmiCollection
            id={selectedEmi}
            handleCloseCreate={handleCloseEdit}
            showCreate={isEditEmi}
          />,document.body
      )}

      {/* <ViewScreenshot
        screenshotUrl={screenshotUrl}
        showScreenshot={showScreenshot}
        handleCloseScreenshot={handleCloseScreenshot}
      /> */}
      {/* <EditEmiCollection
        id={selectedEmi}
        handleCloseCreate={handleCloseEdit}
        showCreate={isEditEmi}
      /> */}
    </>
  );
}

export default CustomerCollectionHistory;
