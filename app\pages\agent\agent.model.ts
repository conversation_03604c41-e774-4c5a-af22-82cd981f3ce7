export interface Agent_List{
    links?:{
                  next: string
                  previous: string
              }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: Agent_List_Item[]
}

export interface Agent_List_Item{
    name: string;
    agent_code: string;
    phone: string;
    wallet_amount: number;
    payment_qr: string;
    profile_photo: string;
    id: number;
    is_active: boolean;
    status_message:string
}

export interface createAgent{
    name:string
    email:string
    phone:string
    password:string
    profile_photo:string | File | null
    address:string
    agent_code:string
    branch_id:number | null
}

export interface agentDetails{
    id:number
    name:string
    agent_code:string
    wallet_amount:number
    email:string
    phone:string
    address:string
    branch_name:string
    last_login:string
    wallet_pending_approvel:number
    profile_photo:string
    total_route:number
    is_active:boolean
    status_message:string
}

export interface agentCollectionOverviewList{
    links?:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: agentCollectionOverview[]
}

export interface agentCollectionOverview{
    id:number
    created_at:string
    transaction_mode:string
    emi_amount:string
    remark:string
    transaction_id:string
    customer_name:string
    status:string
    payment_reference_no:string
    short_title:string
    care_of:string
}

export interface agentRouteList{
    links?:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: agentRoute[]
}

export interface agentRoute{
    id:number
    route_name:string
    agent_id:string
    agent_name:string
    branch_name:string
    created_by_user_name:string
    day:string
    created_at:string
    agent_profile_photo:string
    created_by_usertype:string
}

export interface agentWalletTransactionList{
links?:{
    next: string
    previous: string
}
total:number
page:number
page_size:number
total_pages:number
results: agentWalletTransaction[]
}
export interface agentWalletTransaction{
    id: number
    agent_id:number
    amount:number
    status:string
    agent_phone:number
    created_at:string
    remarks:string
    approved:string
    transaction_id:string
}


export interface activityEvent {
    time:string
    activity:string
    latitude:string
    longitude:string
  }
  
 export interface ActivityLog {
    id:number;
    created_at:string
    day: string;
    data: activityEvent[]; 
}

export interface agentActivityList{
    links?:{
        next: string
        previous: string
    }
    total:number
    page:number
    page_size:number
    total_pages:number
    results: ActivityLog[]
}