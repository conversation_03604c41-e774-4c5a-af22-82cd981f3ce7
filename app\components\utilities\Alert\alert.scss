@import '../../../styles/variables.scss';
.text-area{
    width: 100%;
    border: 2px solid $black_color4;
    border-radius: 5px;
    padding: 5px 5px 40px 5px;
    font-size: 12px;
    color: $black_color3;
    margin-bottom: 20px;
    &:focus {
        outline: none;
        border: 3px solid $black_color4;
    }
}
.alert-icons {
    .material-icons{
    font-size: 65px;
    color: $primary_color;
    }
}


@keyframes countdown {
    from {
      border: 4px solid var(--primary-color);
    }
    to {
      border: 4px solid transparent;
    }
  }
  
  .alert-box {
    position: relative;
    animation: none;
    border: 4px solid var(--primary-color);
    border-radius: 12px;
    margin: 10px;
  }
  
  .alert-box[style*="animation"] {
    animation: countdown linear forwards;
  }

  