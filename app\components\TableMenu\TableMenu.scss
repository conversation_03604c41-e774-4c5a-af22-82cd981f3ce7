@import '/app/styles/variables.scss';

/* The menu container */
.menu-container {
    position: relative;
  }
  
  /* The actual menu */
  .menu {
    position: absolute;
    background-color: $primary_color;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    z-index: 1000; /* Ensure it's above other elements */
    padding: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;

    &::after {
      content: '';
      position: absolute;
      top: 10px;  /* Adjust the position if needed */
      right: -7px; /* Adjust the position if needed */
      width: 0;
      height: 0;
      border-top: 10px solid transparent; /* Creates the top side of the triangle */
      border-bottom: 10px solid transparent; /* Creates the bottom side of the triangle */
      border-left: 10px solid $primary_color; /* Creates the left part of the triangle */
    
      transform: rotate(0deg); /* Rotates the triangle to the right */
    }
  }
  
  /* Show the menu */
  .menu.show {
    opacity: 1;
    transition: opacity 0.2s ease;
  }
  
  /* Menu items */
  .menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .menu li {
    padding: 5px 3px;
    font-size: 12px;
    cursor: pointer;
    color: $white_color;
    font-weight: 600;
    border-bottom: 0.1px solid $white_color;  }

  .menu li:last-child {
    border-bottom: none; /* Remove border for last li */
  }
  
  .menu li:hover {
    background-color: darken($primary_color, 5%); /* Lighter version of the primary color */
    color: $white_color; /* Ensure text color remains white */
    
  }
  
  /* The overlay background that appears behind the menu */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999; /* Below the menu */
  }
  
  /* Additional styles for the material icons */
  .moreHoriz-icon{
    cursor: pointer !important;
    font-size: 20px !important;
    color: $black_color2;
   

  }
  