export  const getOrdinalSuffix = (day: number) => {
    if (day < 1 || day > 31) return "Invalid day"; // Restrict range to 1-28
  
    if (day >= 11 && day <= 13) return `${day}th`; // Special case for 11th, 12th, 13th
    switch (day % 10) {
      case 1:
        return `${day}st`;
      case 2:
        return `${day}nd`;
      case 3:
        return `${day}rd`;
      default:
        return `${day}th`;
    }
  };