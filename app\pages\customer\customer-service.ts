import axiosInstance from "@/app/api/axiosInstance";
import {
  customer_collectioon_overview_list,
  customer_emi_note_list,
  customer_purchase_order_list,
  customerList,
  discountCreate,
  purchaseOrderDetail,
} from "./customer.model";
import { orderEdit } from "../purchaseOrder/purchaseOrder.model";

export const getCustomerList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<customerList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await axiosInstance.get<customerList>(
      `dashboard/customers/?${queryParams.toString()}`
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createCustomer = async (body: FormData) => {
  try {
    const response = await axiosInstance.post(
      "formData/customer/register",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const postAttachment = async (
  body: FormData
): Promise<{ id: number }> => {
  try {
    const response = await axiosInstance.post(
      "formData/customer/create_customer_attachment/",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getCustomerDetails = async (id: number | null) => {
  try {
    const response = await axiosInstance.get(`customer/${id}/detail/`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateCustomer = async (id: number | null, body: FormData) => {
  try {
    const response = await axiosInstance.put(
      `formData/dashboard/customer_update/${id}`,
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteCustomerAttachment = async (id: number) => {
  try {
    const respoonce = await axiosInstance.delete(
      `/delete_customer_attachment/${id}`
    );
    return respoonce.data;
  } catch (error) {
    throw error;
  }
};

export const disableCustomer = async (id: number,status_message:string) => {
  try {
    const response = await axiosInstance.delete(`dashboard/disable/${id}/?status_message=${status_message}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const deleteCustomer = async (id: number) => {
  try {
    const response = await axiosInstance.delete(`dashboard/delete/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getCustomerPurchaseOrder = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  id: number | null
) => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });
    const response = await axiosInstance.get<customer_purchase_order_list>(
      `purchase_order/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getCustomerOrderDetails = async (id: number): Promise<purchaseOrderDetail> => {
  try {
    const response = await axiosInstance.get<purchaseOrderDetail>(
      `purchase_order/detail/${id}/`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const editPurchaseOrder = async (id: number,body:orderEdit) => {
  try {
    const response = await axiosInstance.put(`order/${id}/`,body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getCollectionHistory = async (
  id: number | null,
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string,
  transaction_mode?: string,
  date?: string
): Promise<customer_collectioon_overview_list> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });
    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }
    if (transaction_mode) {
      queryParams.append(
        "transaction_mode",
        encodeURIComponent(transaction_mode)
      );
    }
    if (date) {
      queryParams.append("date", encodeURIComponent(date));
    }
    const response =
      await axiosInstance.get<customer_collectioon_overview_list>(
        `customer/${id}/emi_history/?${queryParams.toString()}`
      );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getCustomerEmiNote = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  created_at: string,
  remainder_date: string,
  id: number | null
): Promise<customer_emi_note_list> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });
    if (created_at) {
      queryParams.append("created_at", encodeURIComponent(created_at));
    }
    if (remainder_date) {
      queryParams.append("remainder_date", encodeURIComponent(remainder_date));
    }
    const response = await axiosInstance.get<customer_emi_note_list>(
      `dashboard/emi_notes/${id}/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteAddress = async(id:number) => {
  try{
    const response = await axiosInstance.delete(`customer_addresses/dashboard/${id}`);
    return response.data;
  }catch(error){
    throw error;
  }
}

export const deleteOrderAttachment = async(id:number) => {
  try{
    const response = await axiosInstance.delete(`formData/delete_order_attachment/${id}/`);
    return response.data;
  }catch(error){
    throw error;
  }
}

//add discount
export const addDiscount = async(body: discountCreate ,order_id: number) => {
  try{
    const response = await axiosInstance.post(`/dashboard/discount/order/${order_id}/`, body)
    return response.data;

  }catch(error){

    throw error;

  }
}

//add attachment
export const addAttachment = async(body: FormData) => {
  try{
    const response = await axiosInstance.post(`formData/customer/create_customer_attachment/`, body)
    return response.data;

  }catch(error){

    throw error;

  }
}

export const getCustomerCollectionStatement = async (id: number | null) => {
  try {
    const response = await axiosInstance.get<Blob>(`/customer/${id}/get_emi_statement`, {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};
