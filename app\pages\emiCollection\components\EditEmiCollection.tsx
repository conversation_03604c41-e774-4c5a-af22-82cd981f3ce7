import React, { useCallback, useEffect } from 'react';
import './edit-emi-collection.scss';
import { SubmitHandler, useForm } from 'react-hook-form';
import { editEMiCollection } from '../emi.model';
import { useAlert } from '@/app/components/utilities/Alert/Alert';
import { useCommonContext } from '@/app/contexts/commonContext';
import { editEmiCollection, getEmiCollectionById } from '../emi-service';

interface Props {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  id: number | null;
}

function EditEmiCollection({ showCreate, handleCloseCreate, id }: Props) {
  const { fire } = useAlert();
  const { setIsLoading } = useCommonContext();
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<editEMiCollection>();

  const onSubmit: SubmitHandler<editEMiCollection> = async (data) => {
    if (id === null) {
      fire({
        position: "center",
        icon: "error",
        title: "Invalid EMI Collection",
        text: "Cannot edit EMI collection without a valid ID",
        confirmButtonText: "Ok",
      });
      return;
    }

    setIsLoading(true);

    editEmiCollection(id, data)
      .then((res) => {
        if (res) {
          fire({
            position: "top-right",
            icon: "success",
            title: "Edited EMI Collection",
            text: "The EMI collection is edited successfully!",
            autoClose: 2000,
          });
          handleCloseCreate();
        }
      })
      .catch((error) => {
        fire({
          position: "center",
          icon: "error",
          title: "Something went wrong",
          text:
            error?.response?.data?.detail ||
            error?.message ||
            "An unknown error occurred",
          confirmButtonText: "Ok",
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const patchValue = useCallback(async () => {
    if (!id) return;

    setIsLoading(true);
    try {
      const api = await getEmiCollectionById(id);
      setValue("transaction_mode", api.transaction_mode);
      setValue("emi_amount", api.emi_amount);
    } catch {
      fire({
        position: "center",
        icon: "error",
        title: "An Error Occurred",
        text: "Please Try Again",
        confirmButtonText: "OK",
        onConfirm: () => {
          // handleCloseCreate();
        },
      });
    } finally {
      setIsLoading(false);
    }
  }, [id, setIsLoading, fire,setValue]);

  useEffect(() => {
    if (showCreate) {
      patchValue();
    } else {
      reset();
    }
  }, [showCreate, reset, patchValue]);

  return (
    <div className={`create-form-overlay ${showCreate ? 'show' : 'hide'}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Edit EMI Collection</h3>
          <span className="material-icons closeIcon" onClick={handleCloseCreate}>
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="amount">EMI Amount</label>
                <input
                  {...register("emi_amount")}
                  id="amount"
                  type="text"
                  placeholder="Enter the EMI amount"
                />
                <p className="error-message">{errors.emi_amount?.message}</p>
              </div>
              <div className="input-field wf-50">
                <label htmlFor="transaction_mode">Transaction Mode</label>
                <div className="input">
                  <select
                    {...register("transaction_mode", {
                      required: "Please choose an EMI Transaction Mode.",
                    })}
                    id="transaction_mode"
                  >
                    <option value="cash">Cash</option>
                    <option value="bank">Bank</option>
                  </select>
                </div>
                <p className="error-message">{errors.transaction_mode?.message}</p>
              </div>
            </div>
            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default EditEmiCollection;
